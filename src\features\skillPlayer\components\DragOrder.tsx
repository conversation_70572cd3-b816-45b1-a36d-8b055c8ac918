import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { DragOrderExercise } from "@/types/skill";
import { AnswerFeedback } from "../store";

interface Props {
  exercise: DragOrderExercise;
  onAnswer: (indices: number[]) => void;
  currentAnswer?: number[];
  feedback?: AnswerFeedback;
}

export default function DragOrder({ exercise, onAnswer, currentAnswer = [] }: Props) {
  const allItems = exercise.payload.items.map((item, index) => ({
    text: item,
    originalIndex: index,
  }));

  const [selectedIndices, setSelectedIndices] = useState<number[]>(currentAnswer);

  const availableItems = allItems.filter(item => !selectedIndices.includes(item.originalIndex));
  const selectedItems = selectedIndices.map(
    selectedIndex => allItems.find(item => item.originalIndex === selectedIndex)!
  );

  useEffect(() => {
    onAnswer(selectedIndices);
  }, [selectedIndices]);

  const selectItem = (item: { text: string; originalIndex: number }) => {
    setSelectedIndices(prev => [...prev, item.originalIndex]);
  };

  const deselectItem = (item: { text: string; originalIndex: number }) => {
    setSelectedIndices(prev => prev.filter(i => i !== item.originalIndex));
  };

  return (
    <View style={styles.container}>
      <Text style={styles.prompt}>Put the words in the correct order.</Text>
      
      <View style={styles.answerContainer}>
        {selectedItems.map((item) => (
          <TouchableOpacity
            key={item.originalIndex}
            style={styles.chip}
            onPress={() => deselectItem(item)}
          >
            <Text style={styles.chipText}>{item.text}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.bankContainer}>
        {availableItems.map((item) => (
          <TouchableOpacity
            key={item.originalIndex}
            style={[styles.chip, styles.bankChip]}
            onPress={() => selectItem(item)}
          >
            <Text style={styles.chipText}>{item.text}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "space-between",
  },
  prompt: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 24,
    color: "#1F2937",
    textAlign: 'center',
  },
  answerContainer: {
    borderBottomWidth: 2,
    borderColor: "#E5E7EB",
    minHeight: 140,
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    alignContent: 'flex-start',
    gap: 8,
    padding: 16,
  },
  bankContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    alignItems: "center",
    gap: 12,
    paddingVertical: 24,
  },
  chip: {
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  bankChip: {
    borderWidth: 2,
    borderColor: "#E5E7EB",
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  chipText: {
    fontSize: 18,
    fontWeight: "500",
    color: "#1F2937",
  },
});