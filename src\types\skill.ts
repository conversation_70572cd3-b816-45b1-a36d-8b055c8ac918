export type UUID = string;

// -------------------- Exercise Types --------------------
export type TextInfoExercise = {
  id: UUID;
  type: "text-info";
  payload: {
    markdown: string;
  };
};

export type SingleChoiceExercise = {
  id: UUID;
  type: "single-choice";
  payload: {
    prompt: string;
    choices: string[];
    answerIndex: number;
  };
};

export type MultiChoiceExercise = {
  id: UUID;
  type: "multi-choice";
  payload: {
    prompt: string;
    choices: string[];
    answerIndices: number[];
  };
};

export type FillBlankExercise = {
  id: UUID;
  type: "fill-blank";
  payload: {
    promptWithBlank: string;
    answer: string;
    choices?: string[]; // optional – UI can render as suggestions
  };
};

export type DragOrderExercise = {
  id: UUID;
  type: "drag-order";
  payload: {
    items: string[];
    correctOrderIndices: number[];
  };
};

export type TrueFalseExercise = {
  id: UUID;
  type: "true-false";
  payload: {
    prompt: string;
    answer: boolean;
  };
};

export type MatchingPairsExercise = {
  id: UUID;
  type: "matching-pairs";
  payload: {
    prompt: string;
    left: string[];
    right: string[];
    pairs: [number, number][]; // [leftIndex, rightIndex]
  };
};

// Union of all
export type Exercise =
  | TextInfoExercise
  | SingleChoiceExercise
  | MultiChoiceExercise
  | FillBlankExercise
  | DragOrderExercise
  | TrueFalseExercise
  | MatchingPairsExercise;

// -------------------- Three-Tier Hierarchy --------------------
export interface Lesson {
  name: string;
  objective?: string;
  exercises: Exercise[];
}

export interface Level {
  name: string;
  lessons: Lesson[];
}

export interface Skill {
  id: UUID;
  name: string;
  description: string;
  version: number;
  levels: Level[];
  // New fields for hierarchy integration
  topicId?: UUID;
  order?: number; // For ordering within a topic
  estimatedDuration?: number; // in minutes
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
}

export interface Topic {
  id: UUID;
  name: string;
  description: string;
  subjectId: UUID;
  order: number; // For ordering within a subject
  skills: Skill[];
  estimatedDuration?: number; // Total duration of all skills
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  prerequisites?: UUID[]; // Topic IDs that should be completed first
}

export interface Subject {
  id: UUID;
  name: string;
  description: string;
  category: string; // e.g., "AI Fundamentals", "Machine Learning", "Data Science"
  order: number; // For ordering in the subject list
  topics: Topic[];
  icon?: string; // Icon name or emoji for the subject
  color?: string; // Theme color for the subject
  estimatedDuration?: number; // Total duration of all topics
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  isPremium?: boolean; // Whether this subject requires premium access
}

// -------------------- Progress Tracking Types --------------------
export interface SkillProgress {
  skillId: UUID;
  lessonIndex: number;
  exerciseIndex: number;
  completedExercises: UUID[];
  answers: Record<UUID, unknown>;
  isCompleted: boolean;
  completionDate?: string;
  score?: number; // 0-100 percentage
  timeSpent?: number; // in seconds
  lastAccessed: string;
}

export interface TopicProgress {
  topicId: UUID;
  skillProgresses: Record<UUID, SkillProgress>;
  isCompleted: boolean;
  completionDate?: string;
  overallScore?: number; // Average score across all skills
  totalTimeSpent?: number; // Total time across all skills
  lastAccessed: string;
}

export interface SubjectProgress {
  subjectId: UUID;
  topicProgresses: Record<UUID, TopicProgress>;
  isCompleted: boolean;
  completionDate?: string;
  overallScore?: number; // Average score across all topics
  totalTimeSpent?: number; // Total time across all topics
  lastAccessed: string;
}

// -------------------- Hierarchy Helper Types --------------------
export interface HierarchyData {
  subjects: Subject[];
  topics: Topic[];
  skills: Skill[];
}

export interface NavigationState {
  currentSubjectId?: UUID;
  currentTopicId?: UUID;
  currentSkillId?: UUID;
}

export interface CompletionStats {
  totalSubjects: number;
  completedSubjects: number;
  totalTopics: number;
  completedTopics: number;
  totalSkills: number;
  completedSkills: number;
  overallProgress: number; // 0-100 percentage
}