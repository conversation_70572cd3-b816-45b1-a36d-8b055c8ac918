import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const clearSampleData = mutation({
  args: {},
  handler: async (ctx) => {
    // Get all subjects
    const subjects = await ctx.db.query('subjects').collect();

    // Delete all subjects
    for (const subject of subjects) {
      await ctx.db.delete(subject._id);
    }

    return {
      message: "Sample data cleared successfully!",
      deletedCount: subjects.length
    };
  },
});

export const seedSampleData = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();

    // Check if data already exists
    const existingSubjects = await ctx.db.query('subjects').collect();
    if (existingSubjects.length > 0) {
      return { message: "Sample data already exists", count: existingSubjects.length };
    }

    // Sample subjects with flat structure: topics and skills at same level
    const subjects = [
      {
        id: "ai-writing-assistant",
        name: "AI Writing Assistant",
        description: "Master ChatGPT, <PERSON>, and other AI writing tools to boost your productivity",
        category: "Writing & Content",
        order: 1,
        icon: "✍️",
        color: "#4F46E5",
        estimatedDuration: 180, // 3 hours
        difficulty: "beginner" as const,
        isPremium: false,
        tags: ["chatgpt", "writing", "productivity"],
        // Topics without nested skills
        topics: [
          {
            id: "getting-started-chatgpt",
            name: "Getting Started with ChatGPT",
            description: "Learn the basics of ChatGPT and how to create effective prompts",
            order: 1,
            estimatedDuration: 45,
            difficulty: "beginner" as const,
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "advanced-prompting",
            name: "Advanced Prompting Strategies",
            description: "Master advanced techniques for getting better results from AI",
            order: 2,
            estimatedDuration: 60,
            difficulty: "intermediate" as const,
            prerequisites: ["getting-started-chatgpt"],
            isActive: true,
            createdAt: now,
            updatedAt: now,
          }
        ],
        // Flat skills array with topicId references
        skills: [
          {
            id: "chatgpt-signup",
            topicId: "getting-started-chatgpt",
            name: "Setting Up ChatGPT",
            description: "Create your ChatGPT account and understand the interface",
            order: 1,
            estimatedDuration: 10,
            difficulty: "beginner" as const,
            contentFileName: "chatgpt-signup.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "basic-prompting",
            topicId: "getting-started-chatgpt",
            name: "Basic Prompting Techniques",
            description: "Learn how to write clear and effective prompts",
            order: 2,
            estimatedDuration: 20,
            difficulty: "beginner" as const,
            contentFileName: "basic-prompting.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "conversation-flow",
            topicId: "getting-started-chatgpt",
            name: "Managing Conversations",
            description: "Learn how to have productive back-and-forth conversations with AI",
            order: 3,
            estimatedDuration: 15,
            difficulty: "beginner" as const,
            contentFileName: "conversation-flow.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "role-prompting",
            topicId: "advanced-prompting",
            name: "Role-Based Prompting",
            description: "Learn to assign specific roles to AI for better responses",
            order: 1,
            estimatedDuration: 20,
            difficulty: "intermediate" as const,
            contentFileName: "role-prompting.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "chain-of-thought",
            topicId: "advanced-prompting",
            name: "Chain of Thought Prompting",
            description: "Guide AI through step-by-step reasoning",
            order: 2,
            estimatedDuration: 25,
            difficulty: "intermediate" as const,
            contentFileName: "chain-of-thought.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "few-shot-examples",
            topicId: "advanced-prompting",
            name: "Few-Shot Learning",
            description: "Use examples to teach AI your preferred style",
            order: 3,
            estimatedDuration: 15,
            difficulty: "intermediate" as const,
            contentFileName: "few-shot-examples.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          }
        ],
        isActive: true,
        createdAt: now,
        updatedAt: now,
      },
      {
        id: "ai-image-generation",
        name: "AI Image Generation",
        description: "Create stunning visuals with DALL-E, Midjourney, and Stable Diffusion",
        category: "Creative & Design",
        order: 2,
        icon: "🎨",
        color: "#EC4899",
        estimatedDuration: 240, // 4 hours
        difficulty: "beginner" as const,
        isPremium: true,
        tags: ["dalle", "midjourney", "design", "creativity"],
        // Topics without nested skills
        topics: [
          {
            id: "dalle-basics",
            name: "DALL-E Fundamentals",
            description: "Master the basics of AI image generation with DALL-E",
            order: 1,
            estimatedDuration: 90,
            difficulty: "beginner" as const,
            isActive: true,
            createdAt: now,
            updatedAt: now,
          }
        ],
        // Flat skills array with topicId references
        skills: [
          {
            id: "dalle-setup",
            topicId: "dalle-basics",
            name: "Getting Started with DALL-E",
            description: "Set up your DALL-E account and understand the interface",
            order: 1,
            estimatedDuration: 15,
            difficulty: "beginner" as const,
            contentFileName: "dalle-setup.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "image-prompting",
            topicId: "dalle-basics",
            name: "Writing Image Prompts",
            description: "Learn to describe images effectively for AI generation",
            order: 2,
            estimatedDuration: 30,
            difficulty: "beginner" as const,
            contentFileName: "image-prompting.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "style-modifiers",
            topicId: "dalle-basics",
            name: "Style and Artistic Modifiers",
            description: "Use artistic styles and modifiers to enhance your images",
            order: 3,
            estimatedDuration: 25,
            difficulty: "beginner" as const,
            contentFileName: "style-modifiers.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "image-editing",
            topicId: "dalle-basics",
            name: "Image Editing and Variations",
            description: "Edit and create variations of generated images",
            order: 4,
            estimatedDuration: 20,
            difficulty: "intermediate" as const,
            contentFileName: "image-editing.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          }
        ],
        isActive: true,
        createdAt: now,
        updatedAt: now,
      },
      {
        id: "ai-productivity-tools",
        name: "AI Productivity Tools",
        description: "Streamline your workflow with AI-powered productivity applications",
        category: "Productivity",
        order: 3,
        icon: "⚡",
        color: "#10B981",
        estimatedDuration: 150, // 2.5 hours
        difficulty: "beginner" as const,
        isPremium: false,
        tags: ["productivity", "automation", "workflow"],
        // Topics without nested skills
        topics: [
          {
            id: "ai-email-tools",
            name: "AI Email Management",
            description: "Use AI to write, organize, and manage your emails efficiently",
            order: 1,
            estimatedDuration: 75,
            difficulty: "beginner" as const,
            isActive: true,
            createdAt: now,
            updatedAt: now,
          }
        ],
        // Flat skills array with topicId references
        skills: [
          {
            id: "email-composition",
            topicId: "ai-email-tools",
            name: "AI Email Writing",
            description: "Generate professional emails with AI assistance",
            order: 1,
            estimatedDuration: 25,
            difficulty: "beginner" as const,
            contentFileName: "email-composition.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "email-summarization",
            topicId: "ai-email-tools",
            name: "Email Summarization",
            description: "Quickly understand long email threads with AI summaries",
            order: 2,
            estimatedDuration: 20,
            difficulty: "beginner" as const,
            contentFileName: "email-summarization.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          },
          {
            id: "email-scheduling",
            topicId: "ai-email-tools",
            name: "Smart Email Scheduling",
            description: "Use AI to optimize email timing and follow-ups",
            order: 3,
            estimatedDuration: 30,
            difficulty: "intermediate" as const,
            contentFileName: "email-scheduling.json",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          }
        ],
        isActive: true,
        createdAt: now,
        updatedAt: now,
      }
    ];

    // Insert all subjects
    const results = [];
    for (const subject of subjects) {
      const docId = await ctx.db.insert('subjects', subject);
      results.push(docId);
    }

    return {
      message: "Sample data seeded successfully!",
      subjectsCreated: results.length,
      subjects: subjects.map(s => ({ id: s.id, name: s.name }))
    };
  },
});
