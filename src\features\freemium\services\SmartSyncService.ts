import { localStorageService, LocalUserProgress, PendingSyncData } from './LocalStorageService';
import { useSubscriptionStore } from '../stores/SubscriptionStore';
import { UserTier } from '../types/UserTier';

interface SyncOperation {
  id: string;
  type: 'progress_update' | 'lesson_completion' | 'achievement_unlock' | 'streak_update';
  data: any;
  timestamp: number;
  priority: 'high' | 'medium' | 'low';
  retryCount: number;
  maxRetries: number;
}

interface SyncBatch {
  operations: SyncOperation[];
  batchId: string;
  createdAt: number;
  estimatedSize: number;
}

class SmartSyncService {
  private syncQueue: SyncOperation[] = [];
  private isSyncing: boolean = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private readonly MAX_BATCH_SIZE = 50;
  private readonly SYNC_INTERVAL_MS = 30000; // 30 seconds
  private readonly MAX_OFFLINE_DAYS = 7;

  // Initialize sync service
  async initialize(userId: string) {
    const subscription = useSubscriptionStore.getState().subscription;
    
    // Only initialize sync for premium users
    if (subscription.tier === UserTier.FREE) {
      console.log('Free user - sync disabled');
      return;
    }

    // Load pending sync data
    await this.loadPendingSyncData();
    
    // Start periodic sync
    this.startPeriodicSync(userId);
    
    console.log('Smart sync initialized for premium user');
  }

  // Start periodic sync for premium users
  private startPeriodicSync(userId: string) {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(async () => {
      await this.performSync(userId);
    }, this.SYNC_INTERVAL_MS);
  }

  // Stop sync service
  stop() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  // Add operation to sync queue
  async queueSyncOperation(operation: Omit<SyncOperation, 'id' | 'retryCount'>) {
    const subscription = useSubscriptionStore.getState().subscription;
    
    // Free users don't sync to cloud
    if (subscription.tier === UserTier.FREE) {
      return;
    }

    const syncOp: SyncOperation = {
      ...operation,
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      retryCount: 0,
    };

    this.syncQueue.push(syncOp);
    
    // Save to persistent storage
    await this.savePendingSyncData();
    
    // Trigger immediate sync for high priority operations
    if (operation.priority === 'high') {
      // Don't await to avoid blocking
      this.performSync(await this.getCurrentUserId()).catch(console.error);
    }
  }

  // Perform sync operation
  private async performSync(userId: string) {
    if (this.isSyncing || this.syncQueue.length === 0) {
      return;
    }

    this.isSyncing = true;
    
    try {
      // Create batches from queue
      const batches = this.createSyncBatches();
      
      for (const batch of batches) {
        await this.processSyncBatch(userId, batch);
      }
      
      // Clear successfully synced operations
      this.syncQueue = this.syncQueue.filter(op => op.retryCount >= op.maxRetries);
      await this.savePendingSyncData();
      
      // Update last sync timestamp
      await localStorageService.setLastSyncTimestamp(Date.now());
      
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  // Create optimized sync batches
  private createSyncBatches(): SyncBatch[] {
    const batches: SyncBatch[] = [];
    let currentBatch: SyncOperation[] = [];
    let currentSize = 0;

    // Sort by priority and timestamp
    const sortedOps = [...this.syncQueue].sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      return a.timestamp - b.timestamp;
    });

    for (const operation of sortedOps) {
      const opSize = this.estimateOperationSize(operation);
      
      if (currentBatch.length >= this.MAX_BATCH_SIZE || currentSize + opSize > 1024 * 100) { // 100KB limit
        if (currentBatch.length > 0) {
          batches.push({
            operations: currentBatch,
            batchId: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            createdAt: Date.now(),
            estimatedSize: currentSize,
          });
        }
        currentBatch = [];
        currentSize = 0;
      }
      
      currentBatch.push(operation);
      currentSize += opSize;
    }

    // Add remaining operations
    if (currentBatch.length > 0) {
      batches.push({
        operations: currentBatch,
        batchId: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: Date.now(),
        estimatedSize: currentSize,
      });
    }

    return batches;
  }

  // Process a sync batch
  private async processSyncBatch(userId: string, batch: SyncBatch) {
    try {
      // Group operations by type for efficient processing
      const groupedOps = this.groupOperationsByType(batch.operations);
      
      // Process each group
      for (const [type, operations] of Object.entries(groupedOps)) {
        await this.processOperationGroup(userId, type, operations);
      }
      
      console.log(`Successfully synced batch ${batch.batchId} with ${batch.operations.length} operations`);
      
    } catch (error) {
      console.error(`Failed to sync batch ${batch.batchId}:`, error);
      
      // Increment retry count for failed operations
      batch.operations.forEach(op => {
        op.retryCount++;
      });
      
      throw error;
    }
  }

  // Group operations by type for batch processing
  private groupOperationsByType(operations: SyncOperation[]): Record<string, SyncOperation[]> {
    return operations.reduce((groups, op) => {
      if (!groups[op.type]) {
        groups[op.type] = [];
      }
      groups[op.type].push(op);
      return groups;
    }, {} as Record<string, SyncOperation[]>);
  }

  // Process operations of the same type together
  private async processOperationGroup(userId: string, type: string, operations: SyncOperation[]) {
    switch (type) {
      case 'progress_update':
        await this.syncProgressUpdates(userId, operations);
        break;
      case 'lesson_completion':
        await this.syncLessonCompletions(userId, operations);
        break;
      case 'achievement_unlock':
        await this.syncAchievements(userId, operations);
        break;
      case 'streak_update':
        await this.syncStreakUpdates(userId, operations);
        break;
      default:
        console.warn(`Unknown sync operation type: ${type}`);
    }
  }

  // Sync progress updates to Convex
  private async syncProgressUpdates(userId: string, operations: SyncOperation[]) {
    // TODO: Implement Convex API calls
    // This would batch update user progress in the database
    console.log(`Syncing ${operations.length} progress updates for user ${userId}`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Sync lesson completions
  private async syncLessonCompletions(userId: string, operations: SyncOperation[]) {
    // TODO: Implement Convex API calls
    console.log(`Syncing ${operations.length} lesson completions for user ${userId}`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Sync achievement unlocks
  private async syncAchievements(userId: string, operations: SyncOperation[]) {
    // TODO: Implement Convex API calls
    console.log(`Syncing ${operations.length} achievements for user ${userId}`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Sync streak updates
  private async syncStreakUpdates(userId: string, operations: SyncOperation[]) {
    // TODO: Implement Convex API calls
    console.log(`Syncing ${operations.length} streak updates for user ${userId}`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Estimate operation size for batching
  private estimateOperationSize(operation: SyncOperation): number {
    return JSON.stringify(operation).length;
  }

  // Load pending sync data from storage
  private async loadPendingSyncData() {
    try {
      const pendingData = await localStorageService.getPendingSyncData();
      if (pendingData) {
        // Convert pending data to sync operations
        this.syncQueue = pendingData.progressUpdates.map(update => ({
          id: `progress_${update.userId}_${update.lastUpdated}`,
          type: 'progress_update' as const,
          data: update,
          timestamp: update.lastUpdated,
          priority: 'medium' as const,
          retryCount: 0,
          maxRetries: 3,
        }));
      }
    } catch (error) {
      console.error('Failed to load pending sync data:', error);
    }
  }

  // Save pending sync data to storage
  private async savePendingSyncData() {
    try {
      const pendingData: PendingSyncData = {
        progressUpdates: this.syncQueue
          .filter(op => op.type === 'progress_update')
          .map(op => op.data),
        completedLessons: this.syncQueue
          .filter(op => op.type === 'lesson_completion')
          .map(op => op.data.lessonId),
        achievements: this.syncQueue
          .filter(op => op.type === 'achievement_unlock')
          .map(op => op.data.achievementId),
        timestamp: Date.now(),
      };
      
      await localStorageService.setPendingSyncData(pendingData);
    } catch (error) {
      console.error('Failed to save pending sync data:', error);
    }
  }

  // Get current user ID (this would come from your auth system)
  private async getCurrentUserId(): Promise<string> {
    // TODO: Get from Clerk or your auth system
    return 'current_user_id';
  }

  // Public methods for triggering specific sync operations
  async syncLessonCompletion(userId: string, skillId: string, lessonId: string, stats: any) {
    await this.queueSyncOperation({
      type: 'lesson_completion',
      data: { userId, skillId, lessonId, stats },
      timestamp: Date.now(),
      priority: 'high',
      maxRetries: 3,
    });
  }

  async syncProgressUpdate(userId: string, progress: LocalUserProgress) {
    await this.queueSyncOperation({
      type: 'progress_update',
      data: progress,
      timestamp: Date.now(),
      priority: 'medium',
      maxRetries: 3,
    });
  }

  async syncAchievementUnlock(userId: string, achievementId: string) {
    await this.queueSyncOperation({
      type: 'achievement_unlock',
      data: { userId, achievementId },
      timestamp: Date.now(),
      priority: 'high',
      maxRetries: 3,
    });
  }

  async syncStreakUpdate(userId: string, streak: number) {
    await this.queueSyncOperation({
      type: 'streak_update',
      data: { userId, streak },
      timestamp: Date.now(),
      priority: 'low',
      maxRetries: 2,
    });
  }

  // Get sync status
  getSyncStatus() {
    return {
      isActive: this.isSyncing,
      queueLength: this.syncQueue.length,
      lastSync: localStorageService.getLastSyncTimestamp(),
    };
  }
}

export const smartSyncService = new SmartSyncService();
