import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

// ==================== CORE USER MANAGEMENT ====================

export const User = {
  email: v.string(),
  // this the Clerk ID, stored in the subject JWT field
  externalId: v.string(),
  imageUrl: v.optional(v.string()),
  name: v.optional(v.string()),
  // Enhanced user fields (made optional for backward compatibility)
  username: v.optional(v.string()),
  dateOfBirth: v.optional(v.string()),
  timezone: v.optional(v.string()),
  language: v.optional(v.string()),
  subscriptionTier: v.optional(v.union(v.literal('free'), v.literal('premium'), v.literal('enterprise'))),
  subscriptionStatus: v.optional(v.union(v.literal('active'), v.literal('inactive'), v.literal('cancelled'), v.literal('trial'))),
  subscriptionExpiresAt: v.optional(v.number()),
  onboardingCompleted: v.optional(v.boolean()),
  lastActiveAt: v.optional(v.number()),
  createdAt: v.optional(v.number()),
  updatedAt: v.optional(v.number()),
};

// ==================== SINGLE TABLE DESIGN - COST OPTIMIZED ====================

// Embedded Skill Schema (flat array at subject level with topicId reference)
const EmbeddedSkill = v.object({
  id: v.string(), // UUID for skill identification
  topicId: v.string(), // Reference to which topic this skill belongs to
  name: v.string(),
  description: v.string(),
  order: v.number(),
  estimatedDuration: v.number(), // Minutes
  difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
  contentFileName: v.string(), // Reference to JSON file or content ID
  contentVersion: v.optional(v.number()), // For content versioning
  prerequisites: v.optional(v.array(v.string())), // Array of skill IDs within same subject
  isActive: v.optional(v.boolean()),
  tags: v.optional(v.array(v.string())),
  createdAt: v.optional(v.number()),
  updatedAt: v.optional(v.number()),
});

// Embedded Topic Schema (no nested skills array)
const EmbeddedTopic = v.object({
  id: v.string(), // UUID for topic identification
  name: v.string(),
  description: v.string(),
  order: v.number(),
  estimatedDuration: v.number(), // Minutes
  difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
  prerequisites: v.optional(v.array(v.string())), // Array of topic IDs within same subject
  isActive: v.optional(v.boolean()),
  tags: v.optional(v.array(v.string())),
  // NO skills array here - skills are now at subject level
  createdAt: v.optional(v.number()),
  updatedAt: v.optional(v.number()),
});

// Main Subject Schema (contains all hierarchy data)
export const Subject = {
  id: v.string(), // UUID for subject identification (for compatibility)
  name: v.string(),
  description: v.string(),
  category: v.string(),
  order: v.number(),
  icon: v.string(),
  color: v.string(),
  estimatedDuration: v.number(), // Total minutes (calculated from topics)
  difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
  isPremium: v.boolean(),
  isActive: v.boolean(),
  tags: v.optional(v.array(v.string())),
  topics: v.array(EmbeddedTopic), // Embedded topics array (no nested skills)
  skills: v.array(EmbeddedSkill), // Flat skills array with topicId references
  createdBy: v.optional(v.id('users')),
  createdAt: v.number(),
  updatedAt: v.number(),
};

// ==================== PROGRESS TRACKING ====================

// Progress tracking now uses string IDs to reference embedded content
export const SkillProgress = {
  userId: v.id('users'),
  subjectId: v.id('subjects'), // Reference to subject document
  skillId: v.string(), // UUID of embedded skill
  topicId: v.string(), // UUID of embedded topic (for easier querying)
  lessonIndex: v.number(),
  exerciseIndex: v.number(),
  completedExercises: v.array(v.string()), // Array of exercise IDs
  answers: v.object({}), // Flexible object for storing answers
  isCompleted: v.boolean(),
  completionDate: v.optional(v.number()),
  score: v.optional(v.number()), // 0-100 percentage
  timeSpent: v.optional(v.number()), // Seconds
  attempts: v.number(),
  lastAccessed: v.number(),
  createdAt: v.number(),
  updatedAt: v.number(),
};

export const TopicProgress = {
  userId: v.id('users'),
  subjectId: v.id('subjects'), // Reference to subject document
  topicId: v.string(), // UUID of embedded topic
  isCompleted: v.boolean(),
  completionDate: v.optional(v.number()),
  overallScore: v.optional(v.number()), // Average score across skills
  totalTimeSpent: v.optional(v.number()), // Total seconds
  completedSkillsCount: v.number(),
  totalSkillsCount: v.number(),
  lastAccessed: v.number(),
  createdAt: v.number(),
  updatedAt: v.number(),
};

export const SubjectProgress = {
  userId: v.id('users'),
  subjectId: v.id('subjects'), // Reference to subject document
  isCompleted: v.boolean(),
  completionDate: v.optional(v.number()),
  overallScore: v.optional(v.number()), // Average score across topics
  totalTimeSpent: v.optional(v.number()), // Total seconds
  completedTopicsCount: v.number(),
  totalTopicsCount: v.number(),
  lastAccessed: v.number(),
  createdAt: v.number(),
  updatedAt: v.number(),
};

// ==================== GAMIFICATION & ACHIEVEMENTS ====================

export const UserStreak = {
  userId: v.id('users'),
  currentStreak: v.number(),
  longestStreak: v.number(),
  lastActivityDate: v.string(), // YYYY-MM-DD format
  streakFreezes: v.number(), // Available freezes
  streakFreezesUsed: v.number(), // Used this month
  lastFreezeDate: v.optional(v.string()),
  createdAt: v.number(),
  updatedAt: v.number(),
};

export const Achievement = {
  name: v.string(),
  description: v.string(),
  type: v.union(
    v.literal('streak'),
    v.literal('completion'),
    v.literal('score'),
    v.literal('time'),
    v.literal('milestone')
  ),
  criteria: v.object({}), // Flexible criteria object
  icon: v.string(),
  badgeColor: v.string(),
  points: v.number(),
  isActive: v.boolean(),
  createdAt: v.number(),
  updatedAt: v.number(),
};

export const UserAchievement = {
  userId: v.id('users'),
  achievementId: v.id('achievements'),
  earnedAt: v.number(),
  progress: v.optional(v.number()), // For progressive achievements
  metadata: v.optional(v.object({})), // Additional context
};

export const Certificate = {
  userId: v.id('users'),
  subjectId: v.id('subjects'),
  certificateNumber: v.string(), // Unique certificate identifier
  issuedAt: v.number(),
  completionScore: v.number(),
  totalTimeSpent: v.number(),
  skillsCompleted: v.number(),
  certificateUrl: v.optional(v.string()), // Generated certificate URL
  isValid: v.boolean(),
  createdAt: v.number(),
};

// ==================== ANALYTICS & INSIGHTS ====================

export const LearningSession = {
  userId: v.id('users'),
  skillId: v.id('skills'),
  sessionStart: v.number(),
  sessionEnd: v.optional(v.number()),
  exercisesCompleted: v.number(),
  correctAnswers: v.number(),
  totalAnswers: v.number(),
  timeSpent: v.number(), // Seconds
  deviceType: v.optional(v.string()),
  platform: v.optional(v.string()),
  createdAt: v.number(),
};

export const UserActivity = {
  userId: v.id('users'),
  activityType: v.union(
    v.literal('skill_started'),
    v.literal('skill_completed'),
    v.literal('topic_completed'),
    v.literal('subject_completed'),
    v.literal('streak_milestone'),
    v.literal('achievement_earned'),
    v.literal('login'),
    v.literal('logout')
  ),
  entityId: v.optional(v.string()), // ID of related entity (skill, topic, etc.)
  metadata: v.optional(v.object({})), // Additional activity data
  timestamp: v.number(),
};

// ==================== CONTENT MANAGEMENT ====================

export const SkillContent = {
  skillId: v.id('skills'),
  version: v.number(),
  content: v.object({}), // Full skill JSON content
  isActive: v.boolean(),
  createdBy: v.optional(v.id('users')),
  createdAt: v.number(),
  updatedAt: v.number(),
};

// ==================== SCHEMA DEFINITION WITH INDEXES ====================

export default defineSchema({
  // -------------------- Core User Management --------------------
  users: defineTable(User)
    .index('byExternalId', ['externalId'])
    .index('byEmail', ['email'])
    .index('bySubscriptionTier', ['subscriptionTier'])
    .index('byLastActive', ['lastActiveAt'])
    .index('byCreatedAt', ['createdAt']),

  // -------------------- Single Table Design - Cost Optimized --------------------
  // All educational content (subjects + topics + skills) in one table
  subjects: defineTable(Subject)
    .index('byOrder', ['order'])
    .index('byCategory', ['category'])
    .index('byDifficulty', ['difficulty'])
    .index('byPremium', ['isPremium'])
    .index('byActive', ['isActive'])
    .index('byCreatedAt', ['createdAt'])
    .index('byId', ['id']), // For UUID-based lookups

  // -------------------- Progress Tracking (Updated for Single Table) --------------------
  skillProgress: defineTable(SkillProgress)
    .index('byUser', ['userId'])
    .index('byUserSkill', ['userId', 'skillId'])
    .index('byUserSubject', ['userId', 'subjectId'])
    .index('byUserTopic', ['userId', 'topicId'])
    .index('bySkillId', ['skillId'])
    .index('byTopicId', ['topicId'])
    .index('bySubjectId', ['subjectId'])
    .index('byUserCompleted', ['userId', 'isCompleted'])
    .index('byLastAccessed', ['lastAccessed'])
    .index('byCompletionDate', ['completionDate']),

  topicProgress: defineTable(TopicProgress)
    .index('byUser', ['userId'])
    .index('byUserTopic', ['userId', 'topicId'])
    .index('byUserSubject', ['userId', 'subjectId'])
    .index('byTopicId', ['topicId'])
    .index('bySubjectId', ['subjectId'])
    .index('byUserCompleted', ['userId', 'isCompleted'])
    .index('byLastAccessed', ['lastAccessed']),

  subjectProgress: defineTable(SubjectProgress)
    .index('byUser', ['userId'])
    .index('byUserSubject', ['userId', 'subjectId'])
    .index('bySubjectId', ['subjectId'])
    .index('byUserCompleted', ['userId', 'isCompleted'])
    .index('byLastAccessed', ['lastAccessed']),

  // -------------------- Gamification & Achievements --------------------
  userStreaks: defineTable(UserStreak)
    .index('byUser', ['userId'])
    .index('byCurrentStreak', ['currentStreak'])
    .index('byLongestStreak', ['longestStreak'])
    .index('byLastActivity', ['lastActivityDate']),

  achievements: defineTable(Achievement)
    .index('byType', ['type'])
    .index('byActive', ['isActive'])
    .index('byPoints', ['points'])
    .index('byCreatedAt', ['createdAt']),

  userAchievements: defineTable(UserAchievement)
    .index('byUser', ['userId'])
    .index('byAchievement', ['achievementId'])
    .index('byUserAchievement', ['userId', 'achievementId'])
    .index('byEarnedAt', ['earnedAt']),

  certificates: defineTable(Certificate)
    .index('byUser', ['userId'])
    .index('bySubject', ['subjectId'])
    .index('byUserSubject', ['userId', 'subjectId'])
    .index('byCertificateNumber', ['certificateNumber'])
    .index('byIssuedAt', ['issuedAt'])
    .index('byValid', ['isValid']),

  // -------------------- Analytics & Insights --------------------
  learningSessions: defineTable(LearningSession)
    .index('byUser', ['userId'])
    .index('bySkill', ['skillId'])
    .index('byUserSkill', ['userId', 'skillId'])
    .index('bySessionStart', ['sessionStart'])
    .index('byCreatedAt', ['createdAt']),

  userActivities: defineTable(UserActivity)
    .index('byUser', ['userId'])
    .index('byActivityType', ['activityType'])
    .index('byUserActivity', ['userId', 'activityType'])
    .index('byTimestamp', ['timestamp'])
    .index('byEntityId', ['entityId']),

  // -------------------- Content Management --------------------
  // Note: Skill content is now managed via contentFileName references
  // No separate skillContents table needed - reduces database calls
});

// ==================== TYPE EXPORTS FOR CONVEX FUNCTIONS ====================

// Export types for use in Convex functions
export type UserDoc = typeof User;
export type SubjectDoc = typeof Subject;
export type SkillProgressDoc = typeof SkillProgress;
export type TopicProgressDoc = typeof TopicProgress;
export type SubjectProgressDoc = typeof SubjectProgress;
export type UserStreakDoc = typeof UserStreak;
export type AchievementDoc = typeof Achievement;
export type UserAchievementDoc = typeof UserAchievement;
export type CertificateDoc = typeof Certificate;
export type LearningSessionDoc = typeof LearningSession;
export type UserActivityDoc = typeof UserActivity;
