import React, { useEffect } from 'react';
import { useLocalSearchParams } from 'expo-router';
import SkillPlayer from '@/features/skillPlayer/SkillPlayer';
import { useSkillStore } from '@/features/skillPlayer/store';
import { useUser } from '@clerk/clerk-expo';
import { useFreemium } from '@/features/freemium/FreemiumProvider';

export default function SkillPlayerScreen() {
  const { skillId } = useLocalSearchParams();
  const { user } = useUser();
  const { shouldSyncToCloud } = useFreemium();
  const loadSkill = useSkillStore((state) => state.loadSkill);
  const skill = useSkillStore((state) => state.skill);

  useEffect(() => {
    console.log('[SkillPlayerScreen] useEffect triggered', { 
      skillId, 
      user: user ? { id: user.id } : null, 
      shouldSyncToCloud 
    });
    
    if (skillId && user) {
      console.log('[SkillPlayerScreen] Loading skill with ID:', skillId);
      loadSkill(skillId as string, user.id, shouldSyncToCloud);
    } else {
      console.log('[SkillPlayerScreen] Missing requirements:', {
        hasSkillId: !!skillId,
        hasUser: !!user,
        skillId,
        userId: user?.id
      });
    }
  }, [skillId, user, shouldSyncToCloud, loadSkill]);

  console.log('[SkillPlayerScreen] Render state:', { 
    skillId, 
    hasUser: !!user, 
    hasSkill: !!skill,
    skillName: skill?.name 
  });

  return <SkillPlayer />;
}
