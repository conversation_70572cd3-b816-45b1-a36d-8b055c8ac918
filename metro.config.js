const { getDefaultConfig } = require("expo/metro-config");
const { withNativeWind } = require('nativewind/metro');
const { wrapWithReanimatedMetroConfig } = require('react-native-reanimated/metro-config');

let config = getDefaultConfig(__dirname);

config.resolver.sourceExts.push('ts', 'tsx');

// Apply NativeWind config
config = withNativeWind(config, { input: 'src/app/global.css' });

// Wrap with Reanimated config
config = wrapWithReanimatedMetroConfig(config);

module.exports = config;