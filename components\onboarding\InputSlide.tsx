import React from 'react';
import { View, Text, TextInput, StyleSheet } from 'react-native';

interface InputSlideProps {
  question: string;
  inputType: 'text' | 'number';
  value: string;
  onChange: (v: string) => void;
  placeholder?: string;
  validation?: { min?: number; max?: number };
  color: string;
  error?: string;
}

export function InputSlide({ question, inputType, value, onChange, placeholder, validation, color, error }: InputSlideProps) {
  return (
    <View style={{ width: '100%' }}>
      <Text style={styles.question}>{question}</Text>
      <TextInput
        style={[styles.input, { borderColor: color }]}
        value={value}
        onChangeText={onChange}
        placeholder={placeholder}
        keyboardType={inputType === 'number' ? 'numeric' : 'default'}
        maxLength={inputType === 'number' && validation?.max ? String(validation.max).length : undefined}
      />
      {error ? <Text style={styles.error}>{error}</Text> : null}
    </View>
  );
}

const styles = StyleSheet.create({
  question: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  input: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 14,
    fontSize: 18,
    marginBottom: 8,
    color: '#222',
    backgroundColor: '#f7f8fa',
  },
  error: {
    color: '#e53935',
    fontSize: 14,
    marginTop: 2,
  },
}); 