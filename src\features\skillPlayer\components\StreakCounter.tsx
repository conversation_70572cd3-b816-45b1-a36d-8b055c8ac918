import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useSettingsStore } from '../stores/SettingsStore';

interface Props {
  streak: number;
  isVisible: boolean;
  onStreakMilestone?: (streak: number) => void;
}

export default function StreakCounter({ streak, isVisible, onStreakMilestone }: Props) {
  const { streakCounterEnabled, reducedMotion } = useSettingsStore();
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const pulseScale = useSharedValue(1);
  const glowOpacity = useSharedValue(0);

  useEffect(() => {
    if (isVisible && streakCounterEnabled) {
      // Entrance animation
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
      opacity.value = withTiming(1, { duration: 300 });
    } else {
      // Exit animation
      scale.value = withTiming(0, { duration: 200 });
      opacity.value = withTiming(0, { duration: 200 });
    }
  }, [isVisible, streakCounterEnabled]);

  useEffect(() => {
    if (streak > 0 && !reducedMotion) {
      // Pulse animation when streak increases
      pulseScale.value = withSequence(
        withTiming(1.3, { duration: 150 }),
        withTiming(1, { duration: 150 })
      );

      // Glow effect for milestones
      if (streak % 5 === 0 && streak > 0) {
        glowOpacity.value = withSequence(
          withTiming(0.8, { duration: 200 }),
          withTiming(0, { duration: 800 })
        );
        
        if (onStreakMilestone) {
          onStreakMilestone(streak);
        }
      }
    }
  }, [streak, reducedMotion]);

  const containerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const streakStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseScale.value }],
  }));

  const glowStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));

  if (!streakCounterEnabled || streak === 0) {
    return null;
  }

  const getStreakColor = () => {
    if (streak >= 20) return '#FF6B35'; // Orange-red for high streaks
    if (streak >= 10) return '#FF8E53'; // Orange for good streaks
    if (streak >= 5) return '#FFB84D'; // Yellow-orange for decent streaks
    return '#58CC02'; // Green for starting streaks
  };

  const getStreakEmoji = () => {
    if (streak >= 20) return '🔥';
    if (streak >= 10) return '⚡';
    if (streak >= 5) return '✨';
    return '💫';
  };

  return (
    <Animated.View style={[styles.container, containerStyle]}>
      {/* Glow effect for milestones */}
      <Animated.View 
        style={[
          styles.glow, 
          glowStyle,
          { backgroundColor: getStreakColor() }
        ]} 
      />
      
      <View style={[styles.streakContainer, { borderColor: getStreakColor() }]}>
        <Animated.View style={[styles.streakContent, streakStyle]}>
          <Text style={styles.streakEmoji}>{getStreakEmoji()}</Text>
          <Text style={[styles.streakNumber, { color: getStreakColor() }]}>
            {streak}
          </Text>
        </Animated.View>
        
        <Text style={styles.streakLabel}>streak</Text>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 60,
    right: 20,
    zIndex: 100,
  },
  glow: {
    position: 'absolute',
    top: -5,
    left: -5,
    right: -5,
    bottom: -5,
    borderRadius: 25,
    opacity: 0.3,
  },
  streakContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    borderWidth: 2,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  streakContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  streakEmoji: {
    fontSize: 16,
    marginRight: 4,
  },
  streakNumber: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  streakLabel: {
    fontSize: 10,
    color: '#666',
    fontWeight: '600',
    textTransform: 'uppercase',
    marginTop: 2,
  },
});
