import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Pressable,
  Linking,
} from 'react-native';
import { useSignIn, useSignUp, useSSO, isClerkAPIResponseError } from '@clerk/clerk-expo';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Checkbox from 'expo-checkbox';
import { useSetAtom } from 'jotai';
import { emailAtom } from '@/store/login';
import { ChivoText, HeeboText } from '@/components/ui/CustomText';

const Login = () => {
  const { signIn, setActive } = useSignIn();
  const { signUp } = useSignUp();
  const { startSSOFlow } = useSSO();
  const [loading, setLoading] = useState<'google' | 'apple' | 'email' | false>(false);
  const [isTermsChecked, setTermsChecked] = useState(false);
  const [email, setEmail] = useState('');
  const setEmailAtom = useSetAtom(emailAtom);
  const router = useRouter();

  const handleSignInWithSSO = async (strategy: 'oauth_google' | 'oauth_apple') => {
    if (strategy === 'oauth_google' || strategy === 'oauth_apple') {
      setLoading(strategy.replace('oauth_', '') as 'google' | 'apple');
    } else {
      setLoading(false);
    }
    try {
      const { createdSessionId, setActive } = await startSSOFlow({
        strategy,  
      });

      if (createdSessionId) {
        setActive!({ session: createdSessionId });
      }
    } catch (err) {
      console.error('OAuth error', err);
      Alert.alert('OAuth Error', 'Something went wrong, please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEmailSignIn = async () => {
    if (!isTermsChecked) {
      Alert.alert('Terms Required', 'Please agree to the terms and privacy policy.');
      return;
    }
    if (!email.trim()) {
      Alert.alert('Email Required', 'Please enter your email address.');
      return;
    }

    setLoading('email');
    try {
      setEmailAtom(email);

      await signUp?.create({
        emailAddress: email,
      });
      await signUp!.prepareEmailAddressVerification({ strategy: 'email_code' });
      router.push('/verify');
    } catch (error) {
      if (isClerkAPIResponseError(error)) {
        if (error.status === 422) {
          handleSignInWithEmail();
        } else {
          Alert.alert('Error', 'Something went wrong');
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSignInWithEmail = async () => {
    try {
      const signInAttempt = await signIn?.create({
        strategy: 'email_code',
        identifier: email,
      });
      router.push('/verify?isLogin=true');
    } catch (error) {
      console.error('Error:', JSON.stringify(error, null, 2));
      Alert.alert('Error', 'Failed to send verification code');
    }
  };

  const handleLinkPress = (linkType: 'terms' | 'privacy') => {
    const url = linkType === 'terms'
      ? 'https://example.com/terms'
      : 'https://example.com/privacy';
    Linking.openURL(url);
  };

  return (
    <View className="flex-1 justify-center p-5 bg-[#111]">
      <ChivoText 
        fontWeight="BOLD" 
        className="text-3xl text-white text-center mb-2.5"
      >
        Welcome!
      </ChivoText>
      <HeeboText 
        fontWeight="REGULAR" 
        className="text-base text-gray-500 text-center mb-10"
      >
        Sign in or create an account
      </HeeboText>

      <TextInput
        className="my-2.5 h-[50px] border border-[#333] rounded-lg p-4 bg-[#222] text-white text-base"
        placeholder="Email"
        placeholderTextColor="#888"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />

      <View className="flex-row items-start my-5">
        <Checkbox
          value={isTermsChecked}
          onValueChange={setTermsChecked}
          color={isTermsChecked ? '#007bff' : undefined}
          className="mr-3 mt-0.5"
        />
        <HeeboText fontWeight="REGULAR" className="text-[#888] text-sm flex-1 leading-5">
          I agree to the{' '}
          <Text className="text-white underline" onPress={() => handleLinkPress('terms')}>
            Terms of Service
          </Text>{' '}
          and{' '}
          <Text className="text-white underline" onPress={() => handleLinkPress('privacy')}>
            Privacy Policy
          </Text>
        </HeeboText>
      </View>

      <TouchableOpacity
        className={`mt-2.5 h-[50px] rounded-lg items-center justify-center flex-row ${(!email || !isTermsChecked || loading === 'email') ? 'bg-[#555]' : 'bg-[#007bff]'}`}
        onPress={handleEmailSignIn}
        disabled={!email || !isTermsChecked || loading === 'email'}
      >
        {loading === 'email' ? (
          <ActivityIndicator color="white" />
        ) : (
          <ChivoText fontWeight="BOLD" className="text-white text-base">
            Continue
          </ChivoText>
        )}
      </TouchableOpacity>

      <View className="flex-row items-center my-7.5">
        <View className="flex-1 h-[1px] bg-[#333]" />
        <HeeboText fontWeight="REGULAR" className="text-gray-500 mx-2.5">OR</HeeboText>
        <View className="flex-1 h-[1px] bg-[#333]" />
      </View>

      <Pressable
        className="bg-[#4285F4] mt-2.5 h-[50px] rounded-lg items-center justify-center flex-row"
        onPress={() => handleSignInWithSSO('oauth_google')}
        disabled={!!loading}
      >
        {loading === 'google' ? (
          <ActivityIndicator color="white" />
        ) : (
          <>
            <Ionicons name="logo-google" size={24} color="white" />
            <ChivoText fontWeight="BOLD" className="text-white text-base ml-3">
              Continue with Google
            </ChivoText>
          </>
        )}
      </Pressable>
    </View>
  );
};

export default Login;
