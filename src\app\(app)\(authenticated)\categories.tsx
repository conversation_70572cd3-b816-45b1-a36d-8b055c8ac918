import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Stack, router } from 'expo-router';
import { SKILLS } from '@/fixtures';
import { Ionicons } from '@expo/vector-icons';
import { useFreemium, useUpgradePrompt } from '@/features/freemium/FreemiumProvider';

// --- Data Definition ---
// Manually grouping skills into categories
const CATEGORIES_DATA = [
  {
    title: 'Science & Technology',
    icon: '🔬',
    skills: SKILLS.filter(s => s.id.includes('calculus') || s.id.includes('quntum')),
  },
  {
    title: 'Productivity & Self-Improvement',
    icon: '🎯',
    skills: SKILLS.filter(s => s.id.includes('dummy') || s.id.includes('better')),
  },
  {
    title: 'Other Topics',
    icon: '📚',
    skills: SKILLS.filter(s => s.id.includes('NIC')),
  },
];

const COURSE_COLORS = ['#FF9051', '#6DBEFF', '#714E8C', '#4CAF50', '#C73E1D'];

// --- Reusable Components (Copied from index.tsx for consistency) ---

const PremiumBadge = () => (
  <View style={styles.premiumBadge}>
    <Ionicons name="diamond" size={12} color="#FFFFFF" />
    <Text style={styles.premiumBadgeText}>PRO</Text>
  </View>
);

const CourseCard = ({ course, index, isPremium, isFreeTier, onPress }: any) => (
  <TouchableOpacity
    key={course.id || index}
    onPress={onPress}
    style={[styles.courseCard, { backgroundColor: COURSE_COLORS[index % COURSE_COLORS.length] }]}
  >
    <View style={styles.courseCardContent}>
      <Text style={styles.courseCardTitle}>{course.name}</Text>
      <Text style={styles.courseCardDescription}>{course.description}</Text>
      <Text style={styles.courseCardLessons}>
        {course.levels?.[0]?.lessons?.length || 0} lessons
        {isPremium && isFreeTier && ' • Premium'}
      </Text>
    </View>
    <View style={styles.courseCardIconContainer}>
      <Text style={styles.courseCardIcon}>
        {index % 3 === 0 ? '📚' : index % 3 === 1 ? '📐' : '⚛️'}
      </Text>
    </View>
    {isPremium && <PremiumBadge />}
  </TouchableOpacity>
);


// --- Main Screen Component ---

export default function CategoriesScreen() {
  const { isFreeTier } = useFreemium();
  const { promptForFeature } = useUpgradePrompt();

  const handleCoursePress = (courseId: string, isPremium = false) => {
    if (isPremium && isFreeTier) {
      promptForFeature('Premium Course Access');
      return;
    }
    // Note: The path assumes the learn folder is at the same level as categories.tsx
    router.push(`/(app)/(authenticated)/(tabs)/learn/${courseId}`);
  };

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ title: 'All Categories' }} />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {CATEGORIES_DATA.map((category, catIndex) => (
          <View key={category.title} style={styles.categorySection}>
            <Text style={styles.categoryTitle}>{category.icon} {category.title}</Text>
            {category.skills.map((course, courseIndex) => {
              // Find the original index in SKILLS to determine premium status
              const originalIndex = SKILLS.findIndex(s => s.id === course.id);
              const isPremium = originalIndex > 0; // Assuming first skill is free
              return (
                <CourseCard
                  key={course.id}
                  course={course}
                  index={originalIndex}
                  isPremium={isPremium}
                  isFreeTier={isFreeTier}
                  onPress={() => handleCoursePress(course.id, isPremium)}
                />
              );
            })}
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

// --- Styles ---

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollContainer: {
    paddingHorizontal: 16,
    paddingVertical: 24,
  },
  categorySection: {
    marginBottom: 32,
  },
  categoryTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#343A40',
    marginBottom: 16,
  },
  // Course Card Styles
  courseCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  courseCardContent: {
    flex: 1,
  },
  courseCardTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  courseCardDescription: {
    color: '#FFFFFF',
    fontSize: 14,
    opacity: 0.9,
    marginBottom: 8,
  },
  courseCardLessons: {
    color: '#FFFFFF',
    fontSize: 12,
    opacity: 0.8,
  },
  courseCardIconContainer: {
    width: 64,
    height: 64,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 16,
  },
  courseCardIcon: {
    fontSize: 28,
  },
  // Premium Badge Styles
  premiumBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#FFC107',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  premiumBadgeText: {
    color: '#000000',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
});
