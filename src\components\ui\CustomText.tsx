import React from 'react';
import { Text, TextProps } from 'react-native';
import { FONT_FAMILIES } from '@/utils/fonts';

type FontFamily = keyof typeof FONT_FAMILIES;

interface CustomTextProps<T extends FontFamily> extends TextProps {
  fontFamily?: T;
  fontWeight?: keyof (typeof FONT_FAMILIES)[T];
  children: React.ReactNode;
}

export const CustomText = <T extends FontFamily>({
  fontFamily = 'CHIVO' as T,
  fontWeight = 'REGULAR' as any,
  style,
  children,
  ...props
}: CustomTextProps<T>) => {
  const fontFamilyObj = FONT_FAMILIES[fontFamily];
  const fontStyle = fontFamilyObj[fontWeight as keyof typeof fontFamilyObj];

  return (
    <Text
      style={[
        {
          fontFamily: fontStyle as string,
        },
        style,
      ]}
      {...props}
    >
      {children}
    </Text>
  );
};

type CambayTextProps = Omit<CustomTextProps<'CAMBAY'>, 'fontFamily'>;
export const CambayText: React.FC<CambayTextProps> = (props) => (
  <CustomText fontFamily="CAMBAY" {...props} />
);

type ChivoTextProps = Omit<CustomTextProps<'CHIVO'>, 'fontFamily'>;
export const ChivoText: React.FC<ChivoTextProps> = (props) => (
  <CustomText fontFamily="CHIVO" {...props} />
);

type HeeboTextProps = Omit<CustomTextProps<'HEEBO'>, 'fontFamily'>;
export const HeeboText: React.FC<HeeboTextProps> = (props) => (
  <CustomText fontFamily="HEEBO" {...props} />
); 