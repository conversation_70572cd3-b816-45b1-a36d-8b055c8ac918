import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { hapticService } from '../services/HapticService';
import { audioService } from '../services/AudioService';

interface SettingsState {
  // Audio settings
  audioEnabled: boolean;
  audioVolume: number;
  
  // Haptic settings
  hapticEnabled: boolean;
  
  // Animation settings
  animationsEnabled: boolean;
  reducedMotion: boolean;
  
  // Engagement settings
  particleEffectsEnabled: boolean;
  streakCounterEnabled: boolean;
  celebrationAnimationsEnabled: boolean;
  
  // Actions
  setAudioEnabled: (enabled: boolean) => void;
  setAudioVolume: (volume: number) => void;
  setHapticEnabled: (enabled: boolean) => void;
  setAnimationsEnabled: (enabled: boolean) => void;
  setReducedMotion: (enabled: boolean) => void;
  setParticleEffectsEnabled: (enabled: boolean) => void;
  setStreakCounterEnabled: (enabled: boolean) => void;
  setCelebrationAnimationsEnabled: (enabled: boolean) => void;
  
  // Bulk actions
  resetToDefaults: () => void;
  applyAccessibilitySettings: () => void;
}

const defaultSettings = {
  audioEnabled: true,
  audioVolume: 0.7,
  hapticEnabled: true,
  animationsEnabled: true,
  reducedMotion: false,
  particleEffectsEnabled: true,
  streakCounterEnabled: true,
  celebrationAnimationsEnabled: true,
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      ...defaultSettings,

      setAudioEnabled: (enabled: boolean) => {
        set({ audioEnabled: enabled });
        audioService.setEnabled(enabled);
      },

      setAudioVolume: (volume: number) => {
        set({ audioVolume: Math.max(0, Math.min(1, volume)) });
      },

      setHapticEnabled: (enabled: boolean) => {
        set({ hapticEnabled: enabled });
        hapticService.setEnabled(enabled);
      },

      setAnimationsEnabled: (enabled: boolean) => {
        set({ animationsEnabled: enabled });
      },

      setReducedMotion: (enabled: boolean) => {
        set({ 
          reducedMotion: enabled,
          // Automatically disable intensive animations if reduced motion is enabled
          particleEffectsEnabled: enabled ? false : get().particleEffectsEnabled,
          celebrationAnimationsEnabled: enabled ? false : get().celebrationAnimationsEnabled,
        });
      },

      setParticleEffectsEnabled: (enabled: boolean) => {
        set({ particleEffectsEnabled: enabled });
      },

      setStreakCounterEnabled: (enabled: boolean) => {
        set({ streakCounterEnabled: enabled });
      },

      setCelebrationAnimationsEnabled: (enabled: boolean) => {
        set({ celebrationAnimationsEnabled: enabled });
      },

      resetToDefaults: () => {
        set(defaultSettings);
        audioService.setEnabled(defaultSettings.audioEnabled);
        hapticService.setEnabled(defaultSettings.hapticEnabled);
      },

      applyAccessibilitySettings: () => {
        // Apply settings optimized for accessibility
        set({
          reducedMotion: true,
          particleEffectsEnabled: false,
          celebrationAnimationsEnabled: false,
          animationsEnabled: false,
          audioVolume: 0.8, // Slightly higher volume for accessibility
        });
        audioService.setEnabled(get().audioEnabled);
        hapticService.setEnabled(get().hapticEnabled);
      },
    }),
    {
      name: 'skill-player-settings',
      storage: createJSONStorage(() => AsyncStorage),
      onRehydrateStorage: () => (state) => {
        // Apply settings to services after rehydration
        if (state) {
          audioService.setEnabled(state.audioEnabled);
          hapticService.setEnabled(state.hapticEnabled);
        }
      },
    }
  )
);
