import React from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

interface Props {
  onPress: () => void;
  disabled?: boolean;
}

export default function ContinueButton({ onPress, disabled }: Props) {
  const insets = useSafeAreaInsets();
  return (
    <View style={[styles.wrapper, { paddingBottom: insets.bottom || 12 }]}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={onPress}
        disabled={disabled}
        style={[styles.btn, disabled ? styles.btnDisabled : styles.btnEnabled]}
      >
        <Text style={[styles.label, disabled ? styles.labelDisabled : styles.labelEnabled]}>CONTINUE</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    paddingHorizontal: 24,
    paddingTop: 12,
    borderTopWidth: 1,
    borderColor: "#E5E7EB",
    backgroundColor: '#FFFFFF',
  },
  btn: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 18,
    borderRadius: 99,
  },
  btnEnabled: {
    backgroundColor: "#FACC15",
  },
  btnDisabled: {
    backgroundColor: "#E5E7EB",
  },
  label: {
    fontSize: 16,
    fontWeight: "bold",
    letterSpacing: 0.5,
  },
  labelEnabled: {
    color: "#1F2937",
  },
  labelDisabled: {
    color: "#A1A1AA",
  },
}); 