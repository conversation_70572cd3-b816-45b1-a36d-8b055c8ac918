import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
// import LottieView from 'lottie-react-native'; // Uncomment if you have lottie-react-native installed

interface AnimatedSlideProps {
  title: string;
  description?: string;
  animationUrl?: string;
  color: string;
  onNext: () => void;
}

export function AnimatedSlide({ title, description, animationUrl, color, onNext }: AnimatedSlideProps) {
  return (
    <View style={{ width: '100%', alignItems: 'center' }}>
      {/* Uncomment below if you have lottie-react-native installed */}
      {/* {animationUrl && (
        <LottieView source={{ uri: animationUrl }} autoPlay loop style={{ width: 180, height: 180, marginBottom: 16 }} />
      )} */}
      <Text style={styles.title}>{title}</Text>
      {description && <Text style={styles.description}>{description}</Text>}
      <TouchableOpacity style={[styles.btn, { backgroundColor: color }]} onPress={onNext}>
        <Text style={styles.btnText}>Next</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  title: {
    fontSize: 26,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
    textAlign: 'center',
  },
  btn: {
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 12,
    marginTop: 16,
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
}); 