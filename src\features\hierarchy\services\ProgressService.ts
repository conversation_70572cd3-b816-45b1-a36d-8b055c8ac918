import { 
  UUID, 
  SkillProgress, 
  TopicProgress, 
  SubjectProgress,
  CompletionStats 
} from '@/types/skill';
import { localStorageService } from '@/features/freemium/services/LocalStorageService';
import { hierarchyService } from '../HierarchyService';

export class ProgressService {
  private static instance: ProgressService;
  private progressCache: Map<string, any> = new Map();

  static getInstance(): ProgressService {
    if (!ProgressService.instance) {
      ProgressService.instance = new ProgressService();
    }
    return ProgressService.instance;
  }

  // -------------------- Skill Progress --------------------

  async updateSkillProgress(
    skillId: UUID, 
    progress: Partial<SkillProgress>, 
    userId?: string
  ): Promise<void> {
    const key = `skill_progress_${skillId}${userId ? `_${userId}` : ''}`;
    
    try {
      // Get existing progress
      const existingProgress = await this.getSkillProgress(skillId, userId) || {
        skillId,
        lessonIndex: 0,
        exerciseIndex: 0,
        completedExercises: [],
        answers: {},
        isCompleted: false,
        lastAccessed: new Date().toISOString(),
      };

      // Merge with new progress
      const updatedProgress: SkillProgress = {
        ...existingProgress,
        ...progress,
        lastAccessed: new Date().toISOString(),
      };

      // Save to storage
      await localStorageService.setItem(key, updatedProgress);
      this.progressCache.set(key, updatedProgress);

      // Update topic progress
      const skill = await hierarchyService.getSkillById(skillId);
      if (skill?.topicId) {
        await this.updateTopicProgressFromSkills(skill.topicId, userId);
      }
    } catch (error) {
      console.error('Error updating skill progress:', error);
    }
  }

  async getSkillProgress(skillId: UUID, userId?: string): Promise<SkillProgress | null> {
    const key = `skill_progress_${skillId}${userId ? `_${userId}` : ''}`;
    
    if (this.progressCache.has(key)) {
      return this.progressCache.get(key);
    }

    try {
      const progress = await localStorageService.getItem<SkillProgress>(key);
      if (progress) {
        this.progressCache.set(key, progress);
      }
      return progress;
    } catch (error) {
      console.error('Error loading skill progress:', error);
      return null;
    }
  }

  async markSkillCompleted(skillId: UUID, userId?: string): Promise<void> {
    await this.updateSkillProgress(skillId, {
      isCompleted: true,
      completionDate: new Date().toISOString(),
    }, userId);
  }

  // -------------------- Topic Progress --------------------

  async updateTopicProgressFromSkills(topicId: UUID, userId?: string): Promise<void> {
    try {
      const skills = await hierarchyService.getSkillsByTopicId(topicId);
      const skillProgresses: Record<UUID, SkillProgress> = {};
      
      let completedSkills = 0;
      let totalScore = 0;
      let totalTime = 0;
      let lastAccessed = '';
      let hasAnyProgress = false;

      for (const skill of skills) {
        const progress = await this.getSkillProgress(skill.id, userId);
        if (progress) {
          skillProgresses[skill.id] = progress;
          hasAnyProgress = true;
          
          if (progress.isCompleted) {
            completedSkills++;
          }
          
          if (progress.score) {
            totalScore += progress.score;
          }
          
          if (progress.timeSpent) {
            totalTime += progress.timeSpent;
          }
          
          if (progress.lastAccessed > lastAccessed) {
            lastAccessed = progress.lastAccessed;
          }
        }
      }

      if (!hasAnyProgress) return;

      const isCompleted = completedSkills === skills.length && skills.length > 0;
      const overallScore = skills.length > 0 ? Math.round(totalScore / skills.length) : 0;

      const topicProgress: TopicProgress = {
        topicId,
        skillProgresses,
        isCompleted,
        completionDate: isCompleted ? lastAccessed : undefined,
        overallScore,
        totalTimeSpent: totalTime,
        lastAccessed: lastAccessed || new Date().toISOString(),
      };

      const key = `topic_progress_${topicId}${userId ? `_${userId}` : ''}`;
      await localStorageService.setItem(key, topicProgress);
      this.progressCache.set(key, topicProgress);

      // Update subject progress
      const topic = await hierarchyService.getTopicById(topicId);
      if (topic?.subjectId) {
        await this.updateSubjectProgressFromTopics(topic.subjectId, userId);
      }
    } catch (error) {
      console.error('Error updating topic progress:', error);
    }
  }

  async getTopicProgress(topicId: UUID, userId?: string): Promise<TopicProgress | null> {
    const key = `topic_progress_${topicId}${userId ? `_${userId}` : ''}`;
    
    if (this.progressCache.has(key)) {
      return this.progressCache.get(key);
    }

    try {
      const progress = await localStorageService.getItem<TopicProgress>(key);
      if (progress) {
        this.progressCache.set(key, progress);
        return progress;
      }

      // If no cached progress, calculate from skills
      await this.updateTopicProgressFromSkills(topicId, userId);
      return await localStorageService.getItem<TopicProgress>(key);
    } catch (error) {
      console.error('Error loading topic progress:', error);
      return null;
    }
  }

  // -------------------- Subject Progress --------------------

  async updateSubjectProgressFromTopics(subjectId: UUID, userId?: string): Promise<void> {
    try {
      const topics = await hierarchyService.getTopicsBySubjectId(subjectId);
      const topicProgresses: Record<UUID, TopicProgress> = {};
      
      let completedTopics = 0;
      let totalScore = 0;
      let totalTime = 0;
      let lastAccessed = '';
      let hasAnyProgress = false;

      for (const topic of topics) {
        const progress = await this.getTopicProgress(topic.id, userId);
        if (progress) {
          topicProgresses[topic.id] = progress;
          hasAnyProgress = true;
          
          if (progress.isCompleted) {
            completedTopics++;
          }
          
          if (progress.overallScore) {
            totalScore += progress.overallScore;
          }
          
          if (progress.totalTimeSpent) {
            totalTime += progress.totalTimeSpent;
          }
          
          if (progress.lastAccessed > lastAccessed) {
            lastAccessed = progress.lastAccessed;
          }
        }
      }

      if (!hasAnyProgress) return;

      const isCompleted = completedTopics === topics.length && topics.length > 0;
      const overallScore = topics.length > 0 ? Math.round(totalScore / topics.length) : 0;

      const subjectProgress: SubjectProgress = {
        subjectId,
        topicProgresses,
        isCompleted,
        completionDate: isCompleted ? lastAccessed : undefined,
        overallScore,
        totalTimeSpent: totalTime,
        lastAccessed: lastAccessed || new Date().toISOString(),
      };

      const key = `subject_progress_${subjectId}${userId ? `_${userId}` : ''}`;
      await localStorageService.setItem(key, subjectProgress);
      this.progressCache.set(key, subjectProgress);
    } catch (error) {
      console.error('Error updating subject progress:', error);
    }
  }

  async getSubjectProgress(subjectId: UUID, userId?: string): Promise<SubjectProgress | null> {
    const key = `subject_progress_${subjectId}${userId ? `_${userId}` : ''}`;
    
    if (this.progressCache.has(key)) {
      return this.progressCache.get(key);
    }

    try {
      const progress = await localStorageService.getItem<SubjectProgress>(key);
      if (progress) {
        this.progressCache.set(key, progress);
        return progress;
      }

      // If no cached progress, calculate from topics
      await this.updateSubjectProgressFromTopics(subjectId, userId);
      return await localStorageService.getItem<SubjectProgress>(key);
    } catch (error) {
      console.error('Error loading subject progress:', error);
      return null;
    }
  }

  // -------------------- Overall Statistics --------------------

  async calculateOverallStats(userId?: string): Promise<CompletionStats> {
    try {
      const subjects = await hierarchyService.getAllSubjects();
      let completedSubjects = 0;
      let totalTopics = 0;
      let completedTopics = 0;
      let totalSkills = 0;
      let completedSkills = 0;

      for (const subject of subjects) {
        const subjectProgress = await this.getSubjectProgress(subject.id, userId);
        if (subjectProgress?.isCompleted) {
          completedSubjects++;
        }

        const topics = await hierarchyService.getTopicsBySubjectId(subject.id);
        totalTopics += topics.length;

        for (const topic of topics) {
          const topicProgress = await this.getTopicProgress(topic.id, userId);
          if (topicProgress?.isCompleted) {
            completedTopics++;
          }

          const skills = await hierarchyService.getSkillsByTopicId(topic.id);
          totalSkills += skills.length;

          for (const skill of skills) {
            const skillProgress = await this.getSkillProgress(skill.id, userId);
            if (skillProgress?.isCompleted) {
              completedSkills++;
            }
          }
        }
      }

      const overallProgress = totalSkills > 0 ? Math.round((completedSkills / totalSkills) * 100) : 0;

      return {
        totalSubjects: subjects.length,
        completedSubjects,
        totalTopics,
        completedTopics,
        totalSkills,
        completedSkills,
        overallProgress,
      };
    } catch (error) {
      console.error('Error calculating overall stats:', error);
      return {
        totalSubjects: 0,
        completedSubjects: 0,
        totalTopics: 0,
        completedTopics: 0,
        totalSkills: 0,
        completedSkills: 0,
        overallProgress: 0,
      };
    }
  }

  // -------------------- Cache Management --------------------

  clearCache(): void {
    this.progressCache.clear();
  }

  async refreshAllProgress(userId?: string): Promise<void> {
    this.clearCache();
    // Recalculate all progress from bottom up
    const subjects = await hierarchyService.getAllSubjects();
    for (const subject of subjects) {
      await this.updateSubjectProgressFromTopics(subject.id, userId);
    }
  }
}

// Export singleton instance
export const progressService = ProgressService.getInstance();
