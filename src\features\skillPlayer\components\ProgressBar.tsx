import React, { useEffect } from "react";
import { View, StyleSheet } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  interpolate,
  Easing,
} from "react-native-reanimated";
import { useSettingsStore } from "../stores/SettingsStore";

interface Props {
  progress: number; // 0..1
  animated?: boolean;
}

export default function ProgressBar({ progress, animated = true }: Props) {
  const { animationsEnabled, reducedMotion } = useSettingsStore();
  const progressValue = useSharedValue(0);
  const shimmerValue = useSharedValue(0);
  const pulseValue = useSharedValue(1);
  const clamped = Math.min(Math.max(progress, 0), 1);

  useEffect(() => {
    if (animationsEnabled && !reducedMotion && animated) {
      // Smooth progress animation
      progressValue.value = withTiming(clamped, {
        duration: 800,
        easing: Easing.out(Easing.cubic),
      });

      // Shimmer effect when progress increases
      if (clamped > progressValue.value) {
        shimmerValue.value = withTiming(1, { duration: 600 }, () => {
          shimmerValue.value = withTiming(0, { duration: 400 });
        });
      }

      // Pulse effect for completion
      if (clamped === 1) {
        pulseValue.value = withSpring(1.05, { damping: 15 }, () => {
          pulseValue.value = withSpring(1, { damping: 15 });
        });
      }
    } else {
      // Instant update if animations disabled
      progressValue.value = clamped;
    }
  }, [clamped, animationsEnabled, reducedMotion, animated]);

  const fillStyle = useAnimatedStyle(() => ({
    width: `${progressValue.value * 100}%`,
    transform: [{ scale: pulseValue.value }],
  }));

  const shimmerStyle = useAnimatedStyle(() => {
    const translateX = interpolate(
      shimmerValue.value,
      [0, 1],
      [-100, 300]
    );

    return {
      transform: [{ translateX }],
      opacity: interpolate(shimmerValue.value, [0, 0.5, 1], [0, 0.6, 0]),
    };
  });

  const trackStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseValue.value }],
  }));

  return (
    <Animated.View style={[styles.track, trackStyle]}>
      <Animated.View style={[styles.fill, fillStyle]}>
        {/* Shimmer effect overlay */}
        <Animated.View style={[styles.shimmer, shimmerStyle]} />
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  track: {
    height: 12,
    backgroundColor: "#E5E7EB",
    borderRadius: 6,
    flex: 1,
    width: "100%",
    overflow: "hidden",
  },
  fill: {
    height: "100%",
    backgroundColor: "#FACC15",
    borderRadius: 6,
    position: "relative",
  },
  shimmer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(255, 255, 255, 0.4)",
    width: 100,
  },
});