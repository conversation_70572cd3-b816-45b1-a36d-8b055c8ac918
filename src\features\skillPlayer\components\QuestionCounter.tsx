import React from "react";
import { View, Text, StyleSheet } from "react-native";

interface Props {
  current: number;
  total: number;
}

export default function QuestionCounter({ current, total }: Props) {
  return (
    <View style={styles.container}>
      <Text style={styles.counterText}>
        {current}/{total}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: "#F3F4F6",
    borderRadius: 16,
    marginLeft: 12,
  },
  counterText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#6B7280",
  },
});
