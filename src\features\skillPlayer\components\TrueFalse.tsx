import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { TrueFalseExercise } from "@/types/skill";
import { AnswerFeedback } from "../store";
import { useTheme, useThemedStyles } from '~/lib/theme';
import { getThemedShadow } from '~/lib/themeUtils';

interface Props {
  exercise: TrueFalseExercise;
  onAnswer: (answer: boolean) => void;
  currentAnswer?: boolean;
  feedback?: AnswerFeedback;
}

export default function TrueFalse({ exercise, onAnswer, currentAnswer, feedback }: Props) {
  const { prompt, answer: correctAnswer } = exercise.payload;
  const { colors } = useTheme();

  const choices = [
    { label: "True", value: true, emoji: "👍" },
    { label: "False", value: false, emoji: "👎" },
  ];

  // Create theme-aware styles
  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    prompt: {
      fontSize: 28,
      fontWeight: "bold",
      marginBottom: 48,
      color: theme.text,
      textAlign: "center",
    },
    choicesContainer: {
      flexDirection: "row",
      justifyContent: "center",
      gap: 24,
    },
    optionContainer: {
      backgroundColor: theme.card,
      borderWidth: 2,
      borderColor: theme.border,
      borderRadius: 16,
      width: 140,
      height: 140,
      justifyContent: "center",
      alignItems: "center",
      ...getThemedShadow(theme, 'sm'),
    },
    selectedOptionContainer: {
      borderColor: theme.primaryButton,
      backgroundColor: isDark ? colors.primary[900] : colors.primary[50],
    },
    correctOptionContainer: {
      borderColor: colors.success[500],
      backgroundColor: colors.success[50],
    },
    incorrectOptionContainer: {
      borderColor: colors.error[500],
      backgroundColor: colors.error[50],
    },
    emoji: {
      fontSize: 40,
      marginBottom: 8,
    },
    optionText: {
      fontSize: 18,
      fontWeight: "500",
      color: theme.text,
    },
  }));

  const getOptionStyle = (value: boolean) => {
    const isSelected = currentAnswer === value;
    const isCorrect = value === correctAnswer;
    const showFeedback = feedback?.showFeedback && isSelected;

    if (showFeedback) {
      if (feedback.isCorrect) {
        return [styles.optionContainer, styles.correctOptionContainer];
      } else {
        return [styles.optionContainer, styles.incorrectOptionContainer];
      }
    }

    return [
      styles.optionContainer,
      isSelected && styles.selectedOptionContainer,
    ];
  };

  return (
    <View>
      <Text style={styles.prompt}>{prompt}</Text>
      <View style={styles.choicesContainer}>
        {choices.map((choice) => (
          <TouchableOpacity
            key={choice.label}
            activeOpacity={0.8}
            onPress={() => onAnswer(choice.value)}
            style={getOptionStyle(choice.value)}
          >
            <Text style={styles.emoji}>{choice.emoji}</Text>
            <Text style={styles.optionText}>{choice.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

// Styles are now created inside the component using useThemedStyles