import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface YesNoSlideProps {
  question: string;
  yesLabel?: string;
  noLabel?: string;
  value: boolean | null;
  onChange: (v: boolean) => void;
  color: string;
}

export function YesNoSlide({ question, yesLabel = 'Yes', noLabel = 'No', value, onChange, color }: YesNoSlideProps) {
  return (
    <View style={{ width: '100%' }}>
      <Text style={styles.question}>{question}</Text>
      <View style={styles.row}>
        <TouchableOpacity
          style={[styles.btn, value === true && { borderColor: color, backgroundColor: '#f0f0f0' }]}
          onPress={() => onChange(true)}
        >
          <Text style={[styles.btnText, value === true && { color }]}>{yesLabel}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.btn, value === false && { borderColor: color, backgroundColor: '#f0f0f0' }]}
          onPress={() => onChange(false)}
        >
          <Text style={[styles.btnText, value === false && { color }]}>{noLabel}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  question: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  btn: {
    flex: 1,
    borderWidth: 2,
    borderRadius: 12,
    padding: 18,
    marginHorizontal: 8,
    alignItems: 'center',
    backgroundColor: '#f7f8fa',
    borderColor: 'transparent',
  },
  btnText: {
    fontSize: 18,
    color: '#222',
    fontWeight: '600',
  },
}); 