import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserTier, UserSubscription, UserFeatures, getUserFeatures, canAccessFeature, FREE_USER_FEATURES } from '../types/UserTier';

interface SubscriptionState {
  // Current subscription state
  subscription: UserSubscription;
  isLoading: boolean;
  error: string | null;
  
  // Trial state
  trialStartedAt: Date | null;
  trialDaysRemaining: number;
  
  // Usage tracking for free users
  dailyLessonsCompleted: number;
  lastResetDate: string;
  
  // Actions
  setSubscription: (subscription: UserSubscription) => void;
  upgradeToTier: (tier: UserTier) => Promise<void>;
  startFreeTrial: () => void;
  cancelSubscription: () => void;
  checkFeatureAccess: (feature: keyof UserFeatures) => boolean;
  
  // Usage tracking
  incrementDailyLessons: () => void;
  resetDailyUsage: () => void;
  canCompleteMoreLessons: () => boolean;
  
  // Trial management
  getRemainingTrialDays: () => number;
  isTrialExpired: () => boolean;
  
  // Loading states
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

const createDefaultSubscription = (): UserSubscription => ({
  tier: UserTier.FREE,
  isActive: true,
  features: FREE_USER_FEATURES,
  isTrialActive: false,
});

export const useSubscriptionStore = create<SubscriptionState>()(
  persist(
    (set, get) => ({
      // Initial state
      subscription: createDefaultSubscription(),
      isLoading: false,
      error: null,
      trialStartedAt: null,
      trialDaysRemaining: 7,
      dailyLessonsCompleted: 0,
      lastResetDate: new Date().toISOString().split('T')[0],

      // Actions
      setSubscription: (subscription: UserSubscription) => {
        set({ subscription, error: null });
      },

      upgradeToTier: async (tier: UserTier) => {
        set({ isLoading: true, error: null });
        
        try {
          // In a real app, this would call your payment processor
          // For now, we'll simulate the upgrade
          const newSubscription: UserSubscription = {
            tier,
            isActive: true,
            features: getUserFeatures(tier),
            isTrialActive: false,
            expiresAt: tier !== UserTier.FREE ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : undefined, // 30 days from now
          };

          set({ 
            subscription: newSubscription,
            isLoading: false,
            trialStartedAt: null,
          });

          // TODO: Sync subscription status with backend
          console.log(`Upgraded to ${tier}`);
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Upgrade failed',
            isLoading: false 
          });
        }
      },

      startFreeTrial: () => {
        const trialSubscription: UserSubscription = {
          tier: UserTier.PREMIUM,
          isActive: true,
          features: getUserFeatures(UserTier.PREMIUM),
          isTrialActive: true,
          trialEndsAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        };

        set({ 
          subscription: trialSubscription,
          trialStartedAt: new Date(),
          trialDaysRemaining: 7,
        });
      },

      cancelSubscription: () => {
        const freeSubscription = createDefaultSubscription();
        set({ 
          subscription: freeSubscription,
          trialStartedAt: null,
          trialDaysRemaining: 7,
        });
      },

      checkFeatureAccess: (feature: keyof UserFeatures) => {
        const { subscription } = get();
        return canAccessFeature(subscription, feature);
      },

      // Usage tracking
      incrementDailyLessons: () => {
        const state = get();
        const today = new Date().toISOString().split('T')[0];
        
        if (state.lastResetDate !== today) {
          // Reset daily count for new day
          set({ 
            dailyLessonsCompleted: 1,
            lastResetDate: today,
          });
        } else {
          set({ 
            dailyLessonsCompleted: state.dailyLessonsCompleted + 1,
          });
        }
      },

      resetDailyUsage: () => {
        const today = new Date().toISOString().split('T')[0];
        set({ 
          dailyLessonsCompleted: 0,
          lastResetDate: today,
        });
      },

      canCompleteMoreLessons: () => {
        const { subscription, dailyLessonsCompleted } = get();
        
        // Premium users have unlimited lessons
        if (subscription.tier !== UserTier.FREE) {
          return true;
        }
        
        // Free users have daily limits
        const maxDailyLessons = 10; // This could be configurable
        return dailyLessonsCompleted < maxDailyLessons;
      },

      // Trial management
      getRemainingTrialDays: () => {
        const { subscription } = get();
        
        if (!subscription.isTrialActive || !subscription.trialEndsAt) {
          return 0;
        }
        
        const now = new Date();
        const trialEnd = new Date(subscription.trialEndsAt);
        const diffTime = trialEnd.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return Math.max(0, diffDays);
      },

      isTrialExpired: () => {
        const { subscription } = get();
        
        if (!subscription.isTrialActive || !subscription.trialEndsAt) {
          return false;
        }
        
        return new Date() > new Date(subscription.trialEndsAt);
      },

      // Loading states
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },
    }),
    {
      name: 'subscription-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        subscription: state.subscription,
        trialStartedAt: state.trialStartedAt,
        trialDaysRemaining: state.trialDaysRemaining,
        dailyLessonsCompleted: state.dailyLessonsCompleted,
        lastResetDate: state.lastResetDate,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Check if trial has expired on app startup
          if (state.isTrialExpired()) {
            state.cancelSubscription();
          }
          
          // Reset daily usage if it's a new day
          const today = new Date().toISOString().split('T')[0];
          if (state.lastResetDate !== today) {
            state.resetDailyUsage();
          }
        }
      },
    }
  )
);

// Convenience hooks for common subscription checks
export const useIsFreeTier = () => {
  const subscription = useSubscriptionStore(state => state.subscription);
  return subscription.tier === UserTier.FREE;
};

export const useIsPremium = () => {
  const subscription = useSubscriptionStore(state => state.subscription);
  return subscription.tier === UserTier.PREMIUM || subscription.tier === UserTier.PREMIUM_PLUS;
};

export const useCanAccessFeature = (feature: keyof UserFeatures) => {
  const checkFeatureAccess = useSubscriptionStore(state => state.checkFeatureAccess);
  return checkFeatureAccess(feature);
};

export const useTrialStatus = () => {
  const { subscription, getRemainingTrialDays, isTrialExpired } = useSubscriptionStore();
  
  return {
    isTrialActive: subscription.isTrialActive,
    daysRemaining: getRemainingTrialDays(),
    isExpired: isTrialExpired(),
  };
};

export const useDailyLimits = () => {
  const { dailyLessonsCompleted, canCompleteMoreLessons, subscription } = useSubscriptionStore();
  
  const maxDailyLessons = subscription.tier === UserTier.FREE ? 10 : -1; // -1 means unlimited
  
  return {
    completed: dailyLessonsCompleted,
    remaining: maxDailyLessons === -1 ? -1 : Math.max(0, maxDailyLessons - dailyLessonsCompleted),
    canCompleteMore: canCompleteMoreLessons(),
    isUnlimited: maxDailyLessons === -1,
  };
};
