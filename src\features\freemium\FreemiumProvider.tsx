import React, { createContext, useContext, useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-expo';
import { UserTier, UserSubscription, FREE_USER_FEATURES, PREMIUM_USER_FEATURES } from './types/UserTier';
import { localStorageService } from './services/LocalStorageService';
import UpgradeModal from './components/UpgradeModal';

interface FreemiumContextValue {
  // User tier info
  userTier: UserTier;
  subscription: UserSubscription;
  isFreeTier: boolean;
  isPremiumTier: boolean;
  
  // Feature access
  canAccessFeature: (feature: string) => boolean;
  
  // Upgrade modal
  showUpgradeModal: (feature?: string) => void;
  hideUpgradeModal: () => void;
  
  // Premium sync
  shouldSyncToCloud: boolean;
}

const FreemiumContext = createContext<FreemiumContextValue | null>(null);

interface FreemiumProviderProps {
  children: React.ReactNode;
}

export function FreemiumProvider({ children }: FreemiumProviderProps) {
  const { user } = useUser();
  const [subscription, setSubscription] = useState<UserSubscription>({
    tier: UserTier.FREE,
    isActive: true,
    features: FREE_USER_FEATURES,
    isTrialActive: false,
  });
  
  const [upgradeModal, setUpgradeModal] = useState({
    visible: false,
    feature: undefined as string | undefined,
  });

  // Initialize subscription state
  useEffect(() => {
    initializeSubscription();
  }, [user]);

  const initializeSubscription = async () => {
    try {
      const userId = user?.id || 'anonymous_user';
      
      // Load subscription from local storage first
      const storedSubscription = await localStorageService.getItem<UserSubscription>(`subscription_${userId}`);
      
      if (storedSubscription) {
        setSubscription(storedSubscription);
      }
      
      // For premium users, we could verify with backend here
      // For now, we'll just use local storage
      
    } catch (error) {
      console.error('Failed to initialize subscription:', error);
      // Fallback to free tier
      setSubscription({
        tier: UserTier.FREE,
        isActive: true,
        features: FREE_USER_FEATURES,
        isTrialActive: false,
      });
    }
  };

  const canAccessFeature = (feature: string): boolean => {
    if (!subscription.isActive && subscription.tier !== UserTier.FREE) {
      return FREE_USER_FEATURES[feature as keyof typeof FREE_USER_FEATURES] || false;
    }
    
    return subscription.features[feature as keyof typeof subscription.features] || false;
  };

  const showUpgradeModal = (feature?: string) => {
    setUpgradeModal({
      visible: true,
      feature,
    });
  };

  const hideUpgradeModal = () => {
    setUpgradeModal({
      visible: false,
      feature: undefined,
    });
  };

  const handleUpgrade = async () => {
    try {
      // Here you would integrate with your payment system
      // For now, we'll simulate upgrading to premium
      
      const newSubscription: UserSubscription = {
        tier: UserTier.PREMIUM,
        isActive: true,
        features: PREMIUM_USER_FEATURES,
        isTrialActive: true,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days trial
      };
      
      setSubscription(newSubscription);
      
      // Save to local storage
      const userId = user?.id || 'anonymous_user';
      await localStorageService.setItem(`subscription_${userId}`, newSubscription);
      
      hideUpgradeModal();
      
      // TODO: Integrate with actual payment system
      console.log('Upgrade initiated - integrate with payment system');
      
    } catch (error) {
      console.error('Failed to upgrade:', error);
    }
  };

  const contextValue: FreemiumContextValue = {
    userTier: subscription.tier,
    subscription,
    isFreeTier: subscription.tier === UserTier.FREE,
    isPremiumTier: subscription.tier !== UserTier.FREE,
    canAccessFeature,
    showUpgradeModal,
    hideUpgradeModal,
    shouldSyncToCloud: subscription.tier !== UserTier.FREE && subscription.isActive,
  };

  return (
    <FreemiumContext.Provider value={contextValue}>
      {children}
      
      <UpgradeModal
        visible={upgradeModal.visible}
        onClose={hideUpgradeModal}
        onUpgrade={handleUpgrade}
        feature={upgradeModal.feature}
      />
    </FreemiumContext.Provider>
  );
}

// Hook to use freemium context
export function useFreemium() {
  const context = useContext(FreemiumContext);
  if (!context) {
    throw new Error('useFreemium must be used within FreemiumProvider');
  }
  return context;
}

// Convenience hooks
export function useIsFreeTier() {
  const { isFreeTier } = useFreemium();
  return isFreeTier;
}

export function useIsPremiumTier() {
  const { isPremiumTier } = useFreemium();
  return isPremiumTier;
}

export function useFeatureAccess(feature: string) {
  const { canAccessFeature } = useFreemium();
  return canAccessFeature(feature);
}

export function useUpgradePrompt() {
  const { showUpgradeModal, isFreeTier } = useFreemium();
  
  return {
    showUpgradeModal,
    promptForFeature: (feature: string) => {
      if (isFreeTier) {
        showUpgradeModal(feature);
        return false; // Feature not accessible
      }
      return true; // Feature accessible
    },
  };
}
