import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

interface UpgradeModalProps {
  visible: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  feature?: string;
  title?: string;
  description?: string;
}

const { width, height } = Dimensions.get('window');

export default function UpgradeModal({
  visible,
  onClose,
  onUpgrade,
  feature = 'Premium Feature',
  title = 'Upgrade to Premium',
  description = 'Unlock unlimited access and premium features',
}: UpgradeModalProps) {
  const premiumFeatures = [
    {
      icon: 'infinite-outline',
      title: 'Unlimited Courses',
      description: 'Access to 100+ premium courses',
    },
    {
      icon: 'cloud-outline',
      title: 'Cloud Sync',
      description: 'Sync progress across all devices',
    },
    {
      icon: 'analytics-outline',
      title: 'Advanced Analytics',
      description: 'Detailed learning insights',
    },
    {
      icon: 'trophy-outline',
      title: 'Leaderboards',
      description: 'Compete with other learners',
    },
    {
      icon: 'headset-outline',
      title: 'Priority Support',
      description: '24/7 premium customer support',
    },
    {
      icon: 'sparkles-outline',
      title: 'AI Course Generation',
      description: 'Create custom courses with AI',
    },
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <BlurView intensity={20} style={styles.overlay}>
        <View style={styles.modalContainer}>
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Header */}
            <View style={styles.header}>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
              
              <View style={styles.headerContent}>
                <View style={styles.premiumBadge}>
                  <Ionicons name="diamond" size={24} color="#F59E0B" />
                  <Text style={styles.premiumText}>PREMIUM</Text>
                </View>
                
                <Text style={styles.title}>{title}</Text>
                <Text style={styles.description}>{description}</Text>
              </View>
            </View>

            {/* Feature Highlight */}
            {feature && (
              <View style={styles.featureHighlight}>
                <Ionicons name="lock-closed" size={20} color="#EF4444" />
                <Text style={styles.featureText}>
                  <Text style={styles.featureName}>{feature}</Text> requires Premium
                </Text>
              </View>
            )}

            {/* Premium Features List */}
            <View style={styles.featuresContainer}>
              <Text style={styles.featuresTitle}>What you'll get:</Text>
              
              {premiumFeatures.map((item, index) => (
                <View key={index} style={styles.featureItem}>
                  <View style={styles.featureIcon}>
                    <Ionicons name={item.icon as any} size={20} color="#10B981" />
                  </View>
                  <View style={styles.featureContent}>
                    <Text style={styles.featureItemTitle}>{item.title}</Text>
                    <Text style={styles.featureItemDescription}>{item.description}</Text>
                  </View>
                </View>
              ))}
            </View>

            {/* Pricing */}
            <View style={styles.pricingContainer}>
              <View style={styles.pricingCard}>
                <View style={styles.pricingHeader}>
                  <Text style={styles.pricingTitle}>Premium Monthly</Text>
                  <View style={styles.trialBadge}>
                    <Text style={styles.trialText}>7-day free trial</Text>
                  </View>
                </View>
                
                <View style={styles.pricingContent}>
                  <Text style={styles.price}>$9.99</Text>
                  <Text style={styles.pricePeriod}>/month</Text>
                </View>
                
                <Text style={styles.pricingDescription}>
                  Cancel anytime. No commitment.
                </Text>
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.actionContainer}>
              <TouchableOpacity
                style={styles.upgradeButton}
                onPress={onUpgrade}
                activeOpacity={0.8}
              >
                <Text style={styles.upgradeButtonText}>Start Free Trial</Text>
                <Ionicons name="arrow-forward" size={20} color="#FFFFFF" />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.laterButton}
                onPress={onClose}
                activeOpacity={0.7}
              >
                <Text style={styles.laterButtonText}>Maybe Later</Text>
              </TouchableOpacity>
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={styles.footerText}>
                By continuing, you agree to our Terms of Service and Privacy Policy
              </Text>
            </View>
          </ScrollView>
        </View>
      </BlurView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    maxHeight: height * 0.9,
    minHeight: height * 0.6,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  header: {
    paddingTop: 16,
    paddingHorizontal: 24,
  },
  closeButton: {
    alignSelf: 'flex-end',
    padding: 8,
  },
  headerContent: {
    alignItems: 'center',
    marginTop: 8,
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 16,
  },
  premiumText: {
    marginLeft: 6,
    fontSize: 12,
    fontWeight: '700',
    color: '#D97706',
    letterSpacing: 0.5,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  featureHighlight: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF2F2',
    marginHorizontal: 24,
    marginTop: 24,
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  featureText: {
    marginLeft: 12,
    fontSize: 14,
    color: '#374151',
  },
  featureName: {
    fontWeight: '600',
    color: '#EF4444',
  },
  featuresContainer: {
    paddingHorizontal: 24,
    marginTop: 32,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#ECFDF5',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  featureItemDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  pricingContainer: {
    paddingHorizontal: 24,
    marginTop: 32,
  },
  pricingCard: {
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: '#E2E8F0',
  },
  pricingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  pricingTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  trialBadge: {
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  trialText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  pricingContent: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  price: {
    fontSize: 32,
    fontWeight: '700',
    color: '#111827',
  },
  pricePeriod: {
    fontSize: 16,
    color: '#6B7280',
    marginLeft: 4,
  },
  pricingDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  actionContainer: {
    paddingHorizontal: 24,
    marginTop: 32,
  },
  upgradeButton: {
    backgroundColor: '#6366F1',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  upgradeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginRight: 8,
  },
  laterButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  laterButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
  },
  footer: {
    paddingHorizontal: 24,
    marginTop: 24,
  },
  footerText: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 18,
  },
});
