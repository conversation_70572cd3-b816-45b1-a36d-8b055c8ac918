import {
  useFonts as useExpoFonts,
} from '@expo-google-fonts/heebo';
import {
  Heebo_500Medium,
  Heebo_700Bold,
  Heebo_900Black,
} from '@expo-google-fonts/heebo';
import {

  Chivo_300Light,
  Chivo_300Light_Italic,
  Chivo_500Medium,
  Chivo_500Medium_Italic,
  Chivo_600SemiBold,
  Chivo_600SemiBold_Italic,
  Chivo_800ExtraBold,
  Chivo_800ExtraBold_Italic,
  Chivo_100Thin,
  Chivo_100Thin_Italic,
  Chivo_200ExtraLight,
  Chivo_200ExtraLight_Italic,
} from '@expo-google-fonts/chivo';
import {
  Heebo_100Thin,
  Heebo_200ExtraLight,
  Heebo_300Light,
  Heebo_600SemiBold,
  Heebo_800ExtraBold,
} from '@expo-google-fonts/heebo';

export const useFonts = () => {
  const [fontsLoaded, fontError] = useExpoFonts({
  
    
    // Chivo fonts
    'Chivo-Thin': Chivo_100Thin,
    'Chivo-ThinItalic': Chivo_100Thin_Italic,
    'Chivo-ExtraLight': Chivo_200ExtraLight,
    'Chivo-ExtraLightItalic': Chivo_200ExtraLight_Italic,
    'Chivo-Light': Chivo_300Light,
    'Chivo-LightItalic': Chivo_300Light_Italic,
    'Chivo-Regular': Chivo_300Light, // Changed to use available font
    'Chivo-Italic': Chivo_300Light_Italic, // Changed to use available font
    'Chivo-Medium': Chivo_500Medium,
    'Chivo-MediumItalic': Chivo_500Medium_Italic,
    'Chivo-SemiBold': Chivo_600SemiBold,
    'Chivo-SemiBoldItalic': Chivo_600SemiBold_Italic,
  
    'Chivo-ExtraBold': Chivo_800ExtraBold,
        'Chivo-ExtraBoldItalic': Chivo_800ExtraBold_Italic,
      
    
    // Heebo fonts
    'Heebo-Thin': Heebo_100Thin,
    'Heebo-ExtraLight': Heebo_200ExtraLight,
    'Heebo-Light': Heebo_300Light,

    'Heebo-Medium': Heebo_500Medium,
    'Heebo-SemiBold': Heebo_600SemiBold,
    'Heebo-Bold': Heebo_700Bold,
    'Heebo-ExtraBold': Heebo_800ExtraBold,
    'Heebo-Black': Heebo_900Black,
  });

  return { fontsLoaded, fontError };
};

// Font family constants for easy use
export const FONT_FAMILIES = {
 
  CAMBAY: {
    REGULAR: 'Cambay-Regular',
    ITALIC: 'Cambay-Italic',
    BOLD: 'Cambay-Bold',
    BOLD_ITALIC: 'Cambay-BoldItalic',
  },
  CHIVO: {
    THIN: 'Chivo-Thin',
    THIN_ITALIC: 'Chivo-ThinItalic',
    EXTRA_LIGHT: 'Chivo-ExtraLight',
    EXTRA_LIGHT_ITALIC: 'Chivo-ExtraLightItalic',
    LIGHT: 'Chivo-Light',
    LIGHT_ITALIC: 'Chivo-LightItalic',
    REGULAR: 'Chivo-Regular',
    ITALIC: 'Chivo-Italic',
    MEDIUM: 'Chivo-Medium',
    MEDIUM_ITALIC: 'Chivo-MediumItalic',
    SEMI_BOLD: 'Chivo-SemiBold',
    SEMI_BOLD_ITALIC: 'Chivo-SemiBoldItalic',
    BOLD: 'Chivo-Bold',
    BOLD_ITALIC: 'Chivo-BoldItalic',
    EXTRA_BOLD: 'Chivo-ExtraBold',
    EXTRA_BOLD_ITALIC: 'Chivo-ExtraBoldItalic',
    BLACK: 'Chivo-Black',
    BLACK_ITALIC: 'Chivo-BlackItalic',
  },
  HEEBO: {
    THIN: 'Heebo-Thin',
    EXTRA_LIGHT: 'Heebo-ExtraLight',
    LIGHT: 'Heebo-Light',
    REGULAR: 'Heebo-Regular',
    MEDIUM: 'Heebo-Medium',
    SEMI_BOLD: 'Heebo-SemiBold',
    BOLD: 'Heebo-Bold',
    EXTRA_BOLD: 'Heebo-ExtraBold',
    BLACK: 'Heebo-Black',
  },
} as const; 