# AI Learning Platform - Complete Codebase Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [Technology Stack](#technology-stack)
3. [Application Architecture](#application-architecture)
4. [File Structure](#file-structure)
5. [Core Features](#core-features)
6. [Screen-by-Screen Breakdown](#screen-by-screen-breakdown)
7. [Component System](#component-system)
8. [Data Flow & State Management](#data-flow--state-management)
9. [Offline-First Architecture](#offline-first-architecture)
10. [Theming System](#theming-system)
11. [Gamification & Progress Tracking](#gamification--progress-tracking)
12. [Developer Setup](#developer-setup)
13. [Testing Strategy](#testing-strategy)
14. [Key Workflows](#key-workflows)

---

## Project Overview

This is a **Duolingo-style AI education app** built with React Native and Expo. The application teaches AI tools and concepts to everyday users (not developers) through micro-learning courses with gamified progress tracking, streak systems, and educational UX patterns.

### Main Purpose

- **Educational Focus**: Teaching AI tools to non-technical users
- **Micro-Learning**: Bite-sized lessons with interactive exercises
- **Gamification**: Streaks, progress tracking, achievements, and certificates
- **Freemium Model**: Free courses with offline-first architecture, premium courses with cloud sync
- **Accessibility**: Dark/light mode, haptic feedback, audio cues, and inclusive design

### Target Audience

- Everyday users wanting to learn AI tools
- Non-developers seeking practical AI knowledge
- Users preferring gamified learning experiences
- Both free and premium subscribers

---

## Technology Stack

### Core Framework

- **React Native**: 0.79.4 - Cross-platform mobile development
- **Expo**: ~53.0.13 - Development platform and tooling
- **TypeScript**: ^5.3.3 - Type safety and developer experience

### Navigation & Routing

- **Expo Router**: ~5.1.1 - File-based routing system
- **React Navigation**: ^7.0.14 - Navigation primitives

### Authentication & Backend

- **Clerk**: ^2.9.6 - Authentication and user management
- **Convex**: ^1.22.0 - Real-time backend and database

### State Management

- **Zustand**: ^5.0.6 - Lightweight state management
- **Jotai**: ^2.12.5 - Atomic state management
- **AsyncStorage**: ^2.2.0 - Local storage

### UI & Styling

- **NativeWind**: ^4.1.23 - Tailwind CSS for React Native
- **Tailwind CSS**: ^3.4.17 - Utility-first CSS framework
- **React Native Reanimated**: 3.17.4 - Smooth animations
- **Moti**: ^0.30.0 - Animation library
- **Lottie**: 7.2.2 - Vector animations

### Engagement & Feedback

- **Expo Haptics**: ~14.1.4 - Tactile feedback
- **Expo AV**: ^15.1.7 - Audio playback
- **Expo Speech**: ~13.1.7 - Text-to-speech
- **Rive**: ^9.3.4 - Interactive animations

### Development Tools

- **Jest**: ^29.2.1 - Testing framework
- **Babel**: ^7.25.2 - JavaScript compiler
- **ESLint**: Built-in linting

---

## Application Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    App Entry Point                          │
│                   (src/app/_layout.tsx)                     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Provider Stack                              │
│  • ThemeProvider (Dark/Light mode)                         │
│  • ClerkProvider (Authentication)                          │
│  • FreemiumProvider (Subscription management)              │
│  • OnboardingStateProvider (User onboarding)               │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Route Structure                             │
│  /(app)                                                     │
│    ├── (authenticated)                                      │
│    │   ├── (tabs) - Main app navigation                     │
│    │   │   ├── index.tsx - Home/Course selection           │
│    │   │   ├── premium.tsx - Premium features              │
│    │   │   ├── profile.tsx - User profile                  │
│    │   │   └── learn/[skillId].tsx - Skill player          │
│    │   └── onboard.tsx - User onboarding                   │
│    └── (public)                                            │
│        ├── login.tsx - Authentication                      │
│        └── verify.tsx - Email verification                 │
└─────────────────────────────────────────────────────────────┘
```

### Data Flow Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Local Storage │◄──►│  Zustand Store  │◄──►│   Convex DB     │
│   (Offline)     │    │   (App State)   │    │   (Cloud)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Course Fixtures │    │ Skill Player    │    │ User Progress   │
│ (JSON Files)    │    │ State Machine   │    │ Sync Service    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## File Structure

### Root Directory Structure

```
expo-clerk-convex-google-auth/
├── src/                          # Main source code
│   ├── app/                      # Expo Router pages
│   ├── components/               # Reusable UI components
│   ├── features/                 # Feature-specific modules
│   ├── fixtures/                 # Course data (JSON)
│   ├── store/                    # Global state management
│   ├── types/                    # TypeScript type definitions
│   └── utils/                    # Utility functions
├── components/                   # Shared components (legacy)
├── lib/                         # Core libraries and providers
├── convex/                      # Backend schema and functions
├── docs/                        # Documentation files
└── assets/                      # Static assets
```

### Detailed Source Structure

```
src/
├── app/                         # Expo Router file-based routing
│   ├── _layout.tsx             # Root layout with providers
│   ├── index.tsx               # App entry redirect
│   ├── global.css              # Global styles
│   └── (app)/                  # App-specific routes
│       ├── _layout.tsx         # App layout
│       ├── (authenticated)/    # Protected routes
│       │   ├── _layout.tsx     # Auth layout
│       │   ├── (tabs)/         # Tab navigation
│       │   │   ├── _layout.tsx # Tab bar layout
│       │   │   ├── index.tsx   # Home screen
│       │   │   ├── premium.tsx # Premium features
│       │   │   ├── profile.tsx # User profile
│       │   │   └── learn/      # Learning module
│       │   │       └── [skillId].tsx # Skill player
│       │   └── onboard.tsx     # Onboarding flow
│       └── (public)/           # Public routes
│           ├── login.tsx       # Authentication
│           └── verify.tsx      # Email verification
├── features/                   # Feature modules
│   ├── freemium/              # Subscription management
│   │   ├── FreemiumProvider.tsx
│   │   ├── components/        # Freemium UI components
│   │   ├── services/          # Local storage & sync
│   │   ├── stores/            # Subscription state
│   │   └── types/             # Freemium types
│   └── skillPlayer/           # Learning engine
│       ├── SkillPlayer.tsx    # Main player component
│       ├── ExerciseRenderer.tsx # Exercise type handler
│       ├── components/        # Player UI components
│       ├── services/          # Audio & haptic services
│       ├── stores/            # Player state management
│       └── ui/                # Player-specific UI
├── fixtures/                  # Course content (JSON)
│   ├── index.js              # Course exports
│   ├── dummySkill.json       # Sample course
│   ├── calculous.json        # Calculus course
│   ├── quntumphysicas.json   # Quantum physics course
│   ├── better.json           # Additional course
│   └── NIC.json              # Another course
└── components/ui/             # Shared UI components
    ├── CustomText.tsx         # Typography component
    └── TabBarBackground.ios.tsx # iOS-specific styling
```

### Key Configuration Files

```
├── package.json               # Dependencies and scripts
├── app.json                   # Expo configuration
├── babel.config.js           # Babel configuration
├── tailwind.config.js        # Tailwind CSS configuration
├── tsconfig.json             # TypeScript configuration
├── metro.config.js           # Metro bundler configuration
├── eas.json                  # Expo Application Services
└── convex/
    ├── schema.ts             # Database schema
    ├── auth.config.js        # Authentication configuration
    └── tsconfig.json         # Convex TypeScript config
```

---

## Core Features

### 1. **Course Management System**

- **JSON-based Course Structure**: Courses stored as JSON files in `src/fixtures/`
- **Hierarchical Organization**: Skills → Levels → Lessons → Exercises
- **Exercise Types**: Multiple choice, true/false, fill-in-blank, drag-and-drop, matching pairs
- **Progress Tracking**: Local storage for free users, cloud sync for premium

### 2. **Interactive Learning Engine**

- **Skill Player**: Core learning component (`src/features/skillPlayer/SkillPlayer.tsx`)
- **Exercise Renderer**: Dynamic exercise type handling
- **Answer Validation**: Immediate feedback with correct/incorrect responses
- **Progress Persistence**: Automatic saving of user progress

### 3. **Gamification System**

- **Streak Tracking**: Daily activity streaks with freeze functionality
- **Progress Bars**: Visual progress indicators for lessons and courses
- **Achievement System**: Milestone celebrations and certificates
- **Engagement Feedback**: Haptic feedback, audio cues, and visual animations

### 4. **Freemium Architecture**

- **Offline-First Free Tier**: Local storage for course progress
- **Premium Cloud Sync**: Real-time synchronization across devices
- **Feature Gating**: Premium-only courses and advanced features
- **Upgrade Prompts**: Contextual upgrade suggestions

### 5. **Accessibility & UX**

- **Dark/Light Mode**: System-aware theming with manual override
- **Responsive Design**: Adaptive layouts for different screen sizes
- **Haptic Feedback**: Tactile responses for interactions
- **Audio Support**: Sound effects and text-to-speech capabilities

---

## Screen-by-Screen Breakdown

### 1. **Home Screen** (`src/app/(app)/(authenticated)/(tabs)/index.tsx`)

**Purpose**: Main course selection and discovery interface

**Key Features**:

- **Animated Header**: Collapsible header with blur effect using `@codeherence/react-native-header`
- **Search Functionality**: Real-time course search using Fuse.js fuzzy search
- **Course Categories**: Visual category selection with icons and animations
- **Course Cards**: Staggered grid layout with premium/free badges
- **Theme Integration**: Full dark/light mode support with theme-aware colors

**Components Used**:

- `SearchBar`: Real-time search with cancel functionality
- `CategoryItem`: Animated category selection buttons
- `CourseCard`: Interactive course cards with premium indicators
- `LessonCard`: Staggered grid layout for visual appeal

**State Management**:

- Search query state for filtering courses
- Theme-aware course colors and styling
- Freemium feature access checking

### 2. **Skill Player** (`src/app/(app)/(authenticated)/(tabs)/learn/[skillId].tsx`)

**Purpose**: Core learning experience with interactive exercises

**Key Features**:

- **Exercise Rendering**: Dynamic component rendering based on exercise type
- **Progress Tracking**: Visual progress bar and question counter
- **Answer Validation**: CHECK/CONTINUE button system for user control
- **Feedback System**: Immediate visual and audio feedback
- **Completion Handling**: Lesson completion screens with statistics

**Exercise Types Supported**:

- `text-info`: Informational content with markdown support
- `single-choice`: Multiple choice with single selection
- `true-false`: Binary choice questions
- `fill-blank`: Text input with validation
- `multi-choice`: Multiple selection questions
- `drag-order`: Drag and drop ordering
- `matching-pairs`: Match related items

**State Management**:

- Skill progress state via Zustand store
- Answer tracking and validation
- Lesson completion statistics
- Local storage persistence

### 3. **Premium Screen** (`src/app/(app)/(authenticated)/(tabs)/premium.tsx`)

**Purpose**: Premium feature showcase and subscription management

**Key Features**:

- Premium course access
- Subscription status display
- Feature comparison tables
- Upgrade prompts and payment integration

### 4. **Profile Screen** (`src/app/(app)/(authenticated)/(tabs)/profile.tsx`)

**Purpose**: User profile, settings, and progress overview

**Key Features**:

- User information display
- Progress statistics and achievements
- Settings and preferences
- Theme toggle and accessibility options

### 5. **Onboarding Flow** (`src/app/(app)/(authenticated)/onboard.tsx`)

**Purpose**: User onboarding and preference collection

**Key Features**:

- **Multi-step Flow**: Progress bar with step indicators
- **Dynamic Slides**: Various input types (slider, multiple choice, etc.)
- **Preference Collection**: Learning goals, experience level, interests
- **Completion Persistence**: Save onboarding state to local storage

**Slide Types**:

- `FeatureSlide`: App feature introduction
- `MultipleChoiceSlide`: Single selection questions
- `MultipleSelectSlide`: Multi-selection questions
- `SliderSlide`: Numeric input with range validation
- `InputSlide`: Text input with validation
- `YesNoSlide`: Binary choice questions
- `ImageSelectSlide`: Visual selection options
- `ToggleGroupSlide`: Multiple toggle switches
- `RankingSlide`: Drag-to-rank interface
- `PermissionSlide`: System permission requests

---

## Component System

### 1. **UI Components** (`components/ui/`)

#### **CustomText Component** (`components/ui/CustomText.tsx`)

- **Purpose**: Typography system with custom font integration
- **Fonts Supported**: Heebo, Chivo, Poppins, Andika, Cambay
- **Features**: Font weight variants, theme-aware colors, accessibility support

#### **Theme Components**

- **ThemeToggle**: Manual theme switching
- **StatusBarManager**: Platform-specific status bar handling
- **AppWrapper**: Root app container with theme context

### 2. **Skill Player Components** (`src/features/skillPlayer/components/`)

#### **Core Player Components**

- **ProgressBar**: Animated progress indicator with smooth transitions
- **QuestionCounter**: Current question / total questions display
- **AnswerFeedback**: Success/error feedback with animations
- **LessonCompletionScreen**: End-of-lesson summary with statistics

#### **Exercise Components**

- **SingleChoice**: Radio button selection with animations
- **MultiChoice**: Checkbox selection with validation
- **TrueFalse**: Binary choice with large touch targets
- **FillBlank**: Text input with real-time validation
- **DragOrder**: Drag-and-drop reordering interface
- **MatchingPairs**: Connect related items interface
- **TextInfo**: Markdown content display with character dialogue

#### **Engagement Components**

- **StreakCounter**: Daily streak display with milestone celebrations
- **ParticleEffects**: Celebration animations for correct answers
- **EngagementDemo**: Interactive demo for engagement features

#### **UI Components**

- **CheckContinueButton**: Educational UX pattern for answer validation
- **ContinueButton**: Simple continue navigation
- **SettingsScreen**: Player settings and preferences

### 3. **Onboarding Components** (`components/onboarding/`)

#### **Core Onboarding**

- **ProgressBar**: Step progress indicator
- **BackButton**: Navigation with state preservation
- **AnimatedSlide**: Smooth slide transitions

#### **Input Components**

- **SliderSlide**: Numeric range input with validation
- **InputSlide**: Text input with real-time validation
- **YesNoSlide**: Binary choice with large touch targets
- **ImageSelectSlide**: Visual selection grid
- **ToggleGroupSlide**: Multiple toggle switches
- **RankingSlide**: Drag-to-rank interface
- **MultipleChoiceSlide**: Single selection from options
- **MultipleSelectSlide**: Multiple selection interface

#### **Utility Components**

- **OptionCard**: Reusable selection card with animations
- **FeatureSlide**: App feature introduction
- **PermissionSlide**: System permission requests

### 4. **Freemium Components** (`src/features/freemium/components/`)

#### **UpgradeModal** (`UpgradeModal.tsx`)

- **Purpose**: Contextual upgrade prompts for premium features
- **Features**: Feature-specific messaging, pricing display, payment integration
- **UX Pattern**: Non-intrusive upgrade suggestions

---

## Data Flow & State Management

### 1. **Global State Architecture**

#### **Zustand Stores**

```typescript
// Skill Player Store (src/features/skillPlayer/store.ts)
interface SkillStore {
  skill: Skill | null;
  lessonIndex: number;
  exerciseIndex: number;
  answers: Record<string, unknown>;
  answerFeedback: Record<string, FeedbackState>;
  buttonStates: Record<string, ButtonState>;
  showCompletion: CompletionType | null;
  currentLessonStats: LessonStats;
}

// Settings Store (src/features/skillPlayer/stores/SettingsStore.ts)
interface SettingsStore {
  soundEnabled: boolean;
  hapticsEnabled: boolean;
  animationsEnabled: boolean;
  autoAdvance: boolean;
}

// Subscription Store (src/features/freemium/stores/SubscriptionStore.ts)
interface SubscriptionStore {
  userTier: UserTier;
  subscription: UserSubscription;
  features: FeatureSet;
}
```

#### **Context Providers**

```typescript
// Theme Provider (lib/theme.tsx)
interface ThemeContextType {
  isDarkMode: boolean;
  theme: AppTheme;
  colors: ColorPalette;
  themeMode: 'light' | 'dark' | 'system';
  toggleTheme: () => void;
  setTheme: (mode: ThemeMode) => void;
}

// Freemium Provider (src/features/freemium/FreemiumProvider.tsx)
interface FreemiumContextValue {
  userTier: UserTier;
  isFreeTier: boolean;
  isPremiumTier: boolean;
  canAccessFeature: (feature: string) => boolean;
  showUpgradeModal: (feature?: string) => void;
  shouldSyncToCloud: boolean;
}

// Onboarding State Provider (lib/onboardingState.tsx)
interface OnboardingContextType {
  currentStep: number;
  answers: Record<string, unknown>;
  isCompleted: boolean;
  saveAnswer: (stepId: string, answer: unknown) => void;
  nextStep: () => void;
  previousStep: () => void;
  completeOnboarding: () => void;
}
```

### 2. **Data Persistence Strategy**

#### **Local Storage Service** (`src/features/freemium/services/LocalStorageService.ts`)

```typescript
class LocalStorageService {
  async setItem<T>(key: string, value: T): Promise<void>
  async getItem<T>(key: string): Promise<T | null>
  async removeItem(key: string): Promise<void>
  async clear(): Promise<void>
}
```

**Storage Keys**:

- `skill_progress_${skillId}`: Individual skill progress
- `user_settings`: App settings and preferences
- `onboarding_state`: Onboarding completion status
- `subscription_${userId}`: User subscription information
- `streak_data_${userId}`: Streak tracking data

#### **Smart Sync Service** (`src/features/freemium/services/SmartSyncService.ts`)

- **Purpose**: Intelligent synchronization between local storage and cloud
- **Strategy**: Offline-first with background sync for premium users
- **Conflict Resolution**: Last-write-wins with user notification

### 3. **Course Data Structure**

#### **JSON Course Format** (`src/fixtures/*.json`)

```typescript
interface Skill {
  id: string;
  name: string;
  description: string;
  category: string;
  version: number;
  levels: Level[];
}

interface Level {
  name: string;
  lessons: Lesson[];
}

interface Lesson {
  name: string;
  objective: string;
  exercises: Exercise[];
}

interface Exercise {
  id: string;
  type: ExerciseType;
  payload: ExercisePayload;
}

type ExerciseType =
  | 'text-info'
  | 'single-choice'
  | 'true-false'
  | 'fill-blank'
  | 'multi-choice'
  | 'drag-order'
  | 'matching-pairs';
```

#### **Exercise Payload Examples**

```typescript
// Single Choice Exercise
interface SingleChoicePayload {
  prompt: string;
  choices: string[];
  answerIndex: number;
  feedback_correct: string;
  feedback_incorrect: string;
}

// Text Info Exercise
interface TextInfoPayload {
  character?: string;
  dialogue?: string;
  markdown?: string;
}

// Fill Blank Exercise
interface FillBlankPayload {
  promptWithBlank: string;
  answer: string;
  caseSensitive?: boolean;
  feedback_correct?: string;
  feedback_incorrect?: string;
}
```

---

## Offline-First Architecture

### 1. **Architecture Overview**

The app implements a sophisticated offline-first architecture that prioritizes local storage for free users while providing cloud synchronization for premium subscribers.

#### **Free User Flow**

```
User Action → Local Storage → UI Update
     ↓
Background Sync (Disabled)
```

#### **Premium User Flow**

```
User Action → Local Storage → UI Update
     ↓
Background Sync → Convex DB → Cross-Device Sync
```

### 2. **Local Storage Strategy**

#### **Course Content Storage**

- **Static Courses**: JSON files bundled with app (`src/fixtures/`)
- **Progress Data**: AsyncStorage with structured keys
- **User Preferences**: Local settings persistence
- **Offline Capability**: Full functionality without internet

#### **Data Structure**

```typescript
// Progress Storage Format
interface ProgressData {
  skillId: string;
  lessonIndex: number;
  exerciseIndex: number;
  completedExercises: string[];
  answers: Record<string, unknown>;
  lastUpdated: string;
  syncStatus: 'local' | 'synced' | 'pending';
}

// Settings Storage Format
interface UserSettings {
  soundEnabled: boolean;
  hapticsEnabled: boolean;
  animationsEnabled: boolean;
  theme: 'light' | 'dark' | 'system';
  lastUpdated: string;
}
```

### 3. **Cloud Sync Implementation**

#### **Sync Service** (`src/features/freemium/services/SmartSyncService.ts`)

```typescript
class SmartSyncService {
  // Sync user progress to cloud
  async syncProgressToCloud(progressData: ProgressData): Promise<void>

  // Pull latest progress from cloud
  async pullProgressFromCloud(skillId: string): Promise<ProgressData | null>

  // Resolve conflicts between local and cloud data
  async resolveConflicts(local: ProgressData, cloud: ProgressData): Promise<ProgressData>

  // Background sync for premium users
  async backgroundSync(): Promise<void>
}
```

#### **Sync Triggers**

- **Lesson Completion**: Immediate sync for premium users
- **App Background**: Background sync when app goes to background
- **Network Recovery**: Sync when network connection is restored
- **Manual Sync**: User-initiated sync from settings

### 4. **Conflict Resolution**

- **Strategy**: Last-write-wins with user notification
- **Backup**: Local backup before cloud overwrite
- **User Choice**: Option to choose between local and cloud data
- **Merge Logic**: Intelligent merging for non-conflicting changes

---

## Theming System

### 1. **Theme Architecture** (`lib/theme.tsx`)

#### **Theme Provider Implementation**

```typescript
interface ThemeContextType {
  isDarkMode: boolean;
  theme: AppTheme;
  colors: ColorPalette;
  themeMode: 'light' | 'dark' | 'system';
  toggleTheme: () => void;
  setTheme: (mode: ThemeMode) => void;
  isLoading: boolean;
}
```

#### **Theme Modes**

- **System**: Follows device dark/light mode preference
- **Light**: Force light mode regardless of system setting
- **Dark**: Force dark mode regardless of system setting
- **Persistence**: Theme preference saved to AsyncStorage

### 2. **Color System** (`constants/colors.ts`)

#### **Color Palette Structure**

```typescript
interface ColorPalette {
  // Primary brand colors
  primary: {
    50: string;   // Lightest
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;  // Base color
    600: string;
    700: string;
    800: string;
    900: string;  // Darkest
  };

  // Semantic colors
  success: ColorScale;
  warning: ColorScale;
  error: ColorScale;
  info: ColorScale;

  // Neutral colors
  gray: ColorScale;

  // Special colors
  white: string;
  black: string;
  transparent: string;
}

interface AppTheme {
  // Background colors
  background: string;
  card: string;
  muted: string;

  // Text colors
  text: string;
  secondaryText: string;
  mutedText: string;

  // Interactive colors
  primary: string;
  primaryButton: string;
  border: string;

  // System colors
  shadow: string;
  overlay: string;
}
```

### 3. **Theme Usage Patterns**

#### **useThemedStyles Hook**

```typescript
const useThemedStyles = (
  styleFactory: (theme: AppTheme, colors: ColorPalette, isDark: boolean) => StyleSheet.NamedStyles<any>
) => {
  const { theme, colors, isDarkMode } = useTheme();
  return useMemo(() => styleFactory(theme, colors, isDarkMode), [theme, colors, isDarkMode]);
};

// Usage Example
const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
  container: {
    backgroundColor: theme.background,
    borderColor: theme.border,
  },
  text: {
    color: theme.text,
    fontSize: 16,
  },
}));
```

#### **Theme-Aware Components**

```typescript
// Component with theme integration
const ThemedButton = ({ title, onPress }) => {
  const { theme, colors } = useTheme();

  return (
    <TouchableOpacity
      style={{
        backgroundColor: theme.primaryButton,
        borderRadius: 8,
        padding: 12,
      }}
      onPress={onPress}
    >
      <Text style={{ color: colors.white, fontWeight: 'bold' }}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};
```

### 4. **Platform-Specific Theming**

#### **Status Bar Management**

```typescript
// Automatic status bar styling based on theme
const AppContent = () => {
  const { theme, isDarkMode } = useTheme();

  return (
    <>
      <StatusBar
        style={isDarkMode ? 'light' : 'dark'}
        backgroundColor={theme.background}
      />
      {/* App content */}
    </>
  );
};
```

#### **Navigation Bar Theming**

- **Tab Bar**: Theme-aware colors and blur effects
- **Header**: Dynamic background and text colors
- **Blur Views**: Platform-specific blur tinting

---

## Gamification & Progress Tracking

### 1. **Streak System**

#### **Streak Tracking Logic**

```typescript
interface StreakData {
  currentStreak: number;
  longestStreak: number;
  lastActivityDate: string;
  freezesUsed: number;
  freezesAvailable: number;
  milestones: StreakMilestone[];
}

interface StreakMilestone {
  days: number;
  achieved: boolean;
  achievedDate?: string;
  reward?: string;
}
```

#### **Daily Activity Definition**

- **Criteria**: Complete 1+ exercises per day (UTC timezone)
- **Streak Freeze**: 2 freezes per month for missed days
- **Milestone Rewards**: Celebrations at 7, 30, 100+ day streaks
- **Reset Logic**: Streak resets after missing 2+ consecutive days without freezes

### 2. **Progress Tracking System**

#### **Lesson Progress**

```typescript
interface LessonProgress {
  lessonId: string;
  exercisesCompleted: number;
  totalExercises: number;
  correctAnswers: number;
  incorrectAnswers: number;
  timeSpent: number; // in seconds
  completionDate?: string;
  score: number; // percentage
}

interface SkillProgress {
  skillId: string;
  lessonsCompleted: number;
  totalLessons: number;
  overallScore: number;
  timeSpent: number;
  lastAccessed: string;
  isCompleted: boolean;
  certificate?: Certificate;
}
```

#### **Achievement System**

```typescript
interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'streak' | 'completion' | 'score' | 'time';
  criteria: AchievementCriteria;
  reward?: string;
  unlockedDate?: string;
}

interface Certificate {
  skillId: string;
  skillName: string;
  completionDate: string;
  score: number;
  timeSpent: number;
  certificateId: string;
}
```

### 3. **Engagement Features**

#### **Haptic Feedback** (`src/features/skillPlayer/services/HapticService.ts`)

```typescript
class HapticService {
  // Answer selection feedback
  onAnswerSelect(): void {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }

  // Correct answer celebration
  onCorrectAnswer(): void {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  }

  // Incorrect answer feedback
  onIncorrectAnswer(): void {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
  }

  // Lesson completion celebration
  onLessonComplete(): void {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
  }
}
```

#### **Audio Feedback** (`src/features/skillPlayer/services/AudioService.ts`)

```typescript
class AudioService {
  // Sound effects for different actions
  async playCorrectSound(): Promise<void>
  async playIncorrectSound(): Promise<void>
  async playLessonCompleteSound(): Promise<void>
  async playStreakMilestoneSound(): Promise<void>

  // Settings integration
  async initialize(): Promise<void>
  async cleanup(): Promise<void>
  setEnabled(enabled: boolean): void
}
```

#### **Visual Animations**

- **Particle Effects**: Celebration animations for correct answers
- **Progress Animations**: Smooth progress bar transitions
- **Micro-interactions**: Button press animations and state changes
- **Milestone Celebrations**: Special animations for achievements

---

## Developer Setup

### 1. **Prerequisites**

- **Node.js**: Version 18+ recommended
- **npm/yarn/pnpm**: Package manager (project uses pnpm)
- **Expo CLI**: `npm install -g @expo/cli`
- **Git**: Version control
- **iOS Simulator** (macOS) or **Android Studio** (for device testing)

### 2. **Environment Setup**

#### **Clone and Install**

```bash
# Clone the repository
git clone <repository-url>
cd expo-clerk-convex-google-auth

# Install dependencies
pnpm install

# Start development server
pnpm start
```

#### **Environment Variables**

Create a `.env` file in the root directory:

```env
# Clerk Authentication
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_key

# Convex Backend
EXPO_PUBLIC_CONVEX_URL=https://your-convex-deployment.convex.cloud

# Optional: Sentry (Error Tracking)
SENTRY_DSN=your_sentry_dsn
```

### 3. **Development Scripts**

#### **Available Commands**

```json
{
  "scripts": {
    "start": "expo start",           // Start development server
    "android": "expo run:android",   // Run on Android device/emulator
    "ios": "expo run:ios",          // Run on iOS simulator/device
    "web": "expo start --web",      // Run web version
    "test": "jest --watchAll",      // Run tests in watch mode
    "lint": "expo lint"             // Run ESLint
  }
}
```

#### **Development Workflow**

```bash
# Start development server
pnpm start

# Choose platform:
# - Press 'i' for iOS simulator
# - Press 'a' for Android emulator
# - Press 'w' for web browser
# - Scan QR code with Expo Go app for physical device

# For production builds
expo build:android
expo build:ios
```

### 4. **Project Configuration**

#### **Expo Configuration** (`app.json`)

```json
{
  "expo": {
    "name": "AI Learning Platform",
    "slug": "ai-learning-platform",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "automatic",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#ffffff"
    },
    "plugins": [
      "expo-router",
      "expo-font",
      "expo-secure-store"
    ]
  }
}
```

#### **TypeScript Configuration** (`tsconfig.json`)

```json
{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "~/*": ["./*"]
    }
  }
}
```

### 5. **Code Style and Conventions**

#### **File Naming Conventions**

- **Components**: PascalCase (`UserProfile.tsx`)
- **Hooks**: camelCase with 'use' prefix (`useTheme.tsx`)
- **Utilities**: camelCase (`formatDuration.ts`)
- **Constants**: UPPER_SNAKE_CASE (`API_ENDPOINTS.ts`)
- **Types**: PascalCase (`UserTypes.ts`)

#### **Import Organization**

```typescript
// 1. React and React Native imports
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';

// 2. Third-party library imports
import { router } from 'expo-router';
import { useUser } from '@clerk/clerk-expo';

// 3. Internal imports (absolute paths)
import { useTheme } from '@/lib/theme';
import { Button } from '@/components/ui/Button';

// 4. Relative imports
import './styles.css';
```

#### **Component Structure**

```typescript
// Component props interface
interface ComponentProps {
  title: string;
  onPress?: () => void;
  disabled?: boolean;
}

// Main component
export default function Component({ title, onPress, disabled = false }: ComponentProps) {
  // 1. Hooks
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);

  // 2. Effects
  useEffect(() => {
    // Effect logic
  }, []);

  // 3. Event handlers
  const handlePress = () => {
    if (onPress) onPress();
  };

  // 4. Render
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: theme.text }]}>
        {title}
      </Text>
    </View>
  );
}

// 5. Styles
const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});
```

---

## Testing Strategy

### 1. **Testing Framework Setup**

- **Jest**: Primary testing framework
- **React Native Testing Library**: Component testing
- **Expo Jest Preset**: Expo-specific configurations

### 2. **Test Categories**

#### **Unit Tests**

- **Utility Functions**: Pure function testing
- **Hooks**: Custom hook behavior testing
- **Services**: Audio, haptic, and storage services
- **State Management**: Zustand store testing

#### **Component Tests**

- **UI Components**: Rendering and interaction testing
- **Exercise Components**: Answer validation and feedback
- **Navigation**: Route handling and parameter passing
- **Theme Integration**: Dark/light mode switching

#### **Integration Tests**

- **Skill Player Flow**: Complete lesson progression
- **Onboarding Flow**: Multi-step form completion
- **Freemium Features**: Premium access and upgrade prompts
- **Offline Functionality**: Local storage and sync

### 3. **Test Examples**

#### **Component Test Example**

```typescript
import { render, fireEvent } from '@testing-library/react-native';
import { ThemeProvider } from '@/lib/theme';
import SingleChoice from '@/features/skillPlayer/components/SingleChoice';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

describe('SingleChoice Component', () => {
  const mockProps = {
    prompt: 'What is 2 + 2?',
    choices: ['3', '4', '5', '6'],
    selectedIndex: null,
    onSelect: jest.fn(),
    showFeedback: false,
    correctIndex: 1,
  };

  it('renders prompt and choices correctly', () => {
    const { getByText } = renderWithTheme(<SingleChoice {...mockProps} />);

    expect(getByText('What is 2 + 2?')).toBeTruthy();
    expect(getByText('4')).toBeTruthy();
  });

  it('calls onSelect when choice is pressed', () => {
    const { getByText } = renderWithTheme(<SingleChoice {...mockProps} />);

    fireEvent.press(getByText('4'));
    expect(mockProps.onSelect).toHaveBeenCalledWith(1);
  });
});
```

#### **Hook Test Example**

```typescript
import { renderHook, act } from '@testing-library/react-hooks';
import { useSkillStore } from '@/features/skillPlayer/store';

describe('useSkillStore', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useSkillStore());

    expect(result.current.skill).toBeNull();
    expect(result.current.lessonIndex).toBe(0);
    expect(result.current.exerciseIndex).toBe(0);
  });

  it('should update answer when answer is called', () => {
    const { result } = renderHook(() => useSkillStore());

    act(() => {
      result.current.answer('exercise-1', 'test-answer');
    });

    expect(result.current.answers['exercise-1']).toBe('test-answer');
  });
});
```

### 4. **Testing Commands**

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test --watch

# Run tests with coverage
pnpm test --coverage

# Run specific test file
pnpm test SingleChoice.test.tsx

# Run tests matching pattern
pnpm test --testNamePattern="should render"
```

---

## Key Workflows

### 1. **User Onboarding Flow**

#### **Step-by-Step Process**

```
App Launch → Check Onboarding Status → Show Onboarding OR Main App
     ↓
Onboarding Slides (Feature Introduction, Preferences, Permissions)
     ↓
Save Onboarding Data → Mark as Completed → Navigate to Main App
```

#### **Implementation Details**

- **Progress Persistence**: Each step saved to local storage
- **Back Navigation**: Users can go back and modify previous answers
- **Validation**: Input validation with error messages
- **Completion State**: Onboarding marked as completed in AsyncStorage

### 2. **Learning Session Workflow**

#### **Skill Player State Machine**

```
Course Selection → Load Skill Data → Initialize Progress
     ↓
Display Exercise → User Interaction → Answer Validation
     ↓
Show Feedback → Continue/Check Button → Next Exercise
     ↓
Lesson Completion → Statistics Display → Next Lesson OR Course Complete
```

#### **Exercise Interaction Pattern**

```typescript
// Educational UX Pattern: CHECK → CONTINUE
1. User selects answer
2. User presses CHECK button
3. Show immediate feedback (correct/incorrect)
4. User presses CONTINUE button
5. Advance to next exercise

// Benefits:
// - User control over pacing
// - Time to process feedback
// - Reduced anxiety from automatic advancement
```

### 3. **Progress Synchronization Workflow**

#### **Free User (Offline-First)**

```
User Progress → Local Storage → UI Update
     ↓
No Cloud Sync (Offline-only)
```

#### **Premium User (Cloud Sync)**

```
User Progress → Local Storage → UI Update
     ↓
Background Sync → Convex Database → Cross-Device Sync
     ↓
Conflict Resolution (if needed) → User Notification
```

### 4. **Theme Switching Workflow**

#### **System Theme Detection**

```
App Launch → Detect System Theme → Load User Preference
     ↓
Apply Theme (System/Light/Dark) → Update UI Components
     ↓
Save Preference → Persist to AsyncStorage
```

#### **Manual Theme Override**

```
User Toggles Theme → Update Theme Context → Re-render Components
     ↓
Update Status Bar → Save Preference → Apply Animations
```

### 5. **Freemium Feature Access Workflow**

#### **Feature Access Check**

```typescript
// Feature access pattern
const handlePremiumFeature = () => {
  if (isFreeTier && !canAccessFeature('premium-courses')) {
    promptForFeature('Premium Course Access');
    return;
  }

  // Proceed with feature
  navigateToPremiumCourse();
};
```

#### **Upgrade Prompt Flow**

```
Feature Access Attempt → Check User Tier → Show Upgrade Modal
     ↓
User Decision (Upgrade/Cancel) → Payment Processing OR Dismiss
     ↓
Update Subscription Status → Unlock Features → Continue Workflow
```

---

## Additional Resources

### 1. **Documentation Files** (`docs/`)

- `freemium-architecture.md`: Detailed freemium implementation
- `component-migration-example.md`: Component migration patterns
- `dark-mode-testing-checklist.md`: Theme testing guidelines
- `engagement-enhancements.md`: Gamification features
- `lesson-completion-system.md`: Progress tracking details

### 2. **Key Dependencies Documentation**

- **Expo Router**: [File-based routing](https://docs.expo.dev/router/introduction/)
- **Clerk**: [Authentication](https://clerk.com/docs)
- **Convex**: [Real-time backend](https://docs.convex.dev/)
- **NativeWind**: [Tailwind for React Native](https://www.nativewind.dev/)
- **Zustand**: [State management](https://github.com/pmndrs/zustand)

### 3. **Performance Considerations**

- **Image Optimization**: Use Expo Image for better performance
- **List Rendering**: FlashList for large course lists
- **Animation Performance**: 60fps target with Reanimated
- **Bundle Size**: Code splitting and lazy loading
- **Memory Management**: Proper cleanup of audio/haptic services

### 4. **Security Best Practices**

- **API Keys**: Environment variables for sensitive data
- **User Data**: Encrypted local storage for sensitive information
- **Authentication**: Secure token handling with Clerk
- **Network**: HTTPS-only communication with backend

### 5. **Deployment Considerations**

- **EAS Build**: Expo Application Services for production builds
- **App Store Guidelines**: Educational app compliance
- **Privacy Policy**: Data collection and usage transparency
- **Accessibility**: WCAG compliance for inclusive design

---

## Conclusion

This AI Learning Platform represents a comprehensive educational application built with modern React Native technologies. The codebase demonstrates:

- **Scalable Architecture**: Modular feature organization with clear separation of concerns
- **User-Centric Design**: Educational UX patterns optimized for learning
- **Offline-First Approach**: Robust local storage with intelligent cloud synchronization
- **Accessibility Focus**: Dark/light mode, haptic feedback, and inclusive design
- **Performance Optimization**: Smooth animations and efficient state management
- **Developer Experience**: TypeScript, comprehensive testing, and clear documentation

The application successfully balances educational effectiveness with technical excellence, providing a solid foundation for AI education at scale.

For questions or contributions, please refer to the individual documentation files in the `docs/` directory or contact the development team.
