import { Subject, Topic, Skill, HierarchyData } from '@/types/skill';

// Import existing skills from fixtures
import DUMMY_SKILL from './dummySkill.json';
import CALCULUS from './calculous.json';
import BETTER from './better.json';

// Convert existing skills to new format and add new skills
const existingSkills: Skill[] = [
  // Productivity Skills
  {
    ...DUMMY_SKILL,
    topicId: 'topic-productivity-basics',
    order: 1,
    estimatedDuration: 30,
    difficulty: 'beginner'
  },
  // AI Fundamentals Skills
  {
    id: 'skill-ai-intro-001',
    name: 'What is AI?',
    description: 'Learn the fundamental concepts of artificial intelligence',
    version: 1,
    topicId: 'topic-ai-basics',
    order: 1,
    estimatedDuration: 20,
    difficulty: 'beginner',
    levels: [
      {
        name: 'Level 1: AI Basics',
        lessons: [
          {
            name: 'Introduction to AI',
            objective: 'Understand what artificial intelligence is and its applications',
            exercises: [
              {
                id: 'ai-intro-1',
                type: 'text-info',
                payload: {
                  markdown: '# Welcome to AI Fundamentals\n\nArtificial Intelligence (AI) is the simulation of human intelligence in machines that are programmed to think and learn like humans.'
                }
              },
              {
                id: 'ai-intro-2',
                type: 'single-choice',
                payload: {
                  prompt: 'Which of the following is an example of AI?',
                  choices: ['Voice assistants like Siri', 'A calculator', 'A microwave', 'A bicycle'],
                  answerIndex: 0
                }
              }
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'skill-ai-types-002',
    name: 'Types of AI',
    description: 'Explore different categories and types of artificial intelligence',
    version: 1,
    topicId: 'topic-ai-basics',
    order: 2,
    estimatedDuration: 25,
    difficulty: 'beginner',
    levels: [
      {
        name: 'Level 1: AI Categories',
        lessons: [
          {
            name: 'Narrow vs General AI',
            objective: 'Distinguish between different types of AI systems',
            exercises: [
              {
                id: 'ai-types-1',
                type: 'text-info',
                payload: {
                  markdown: '# Types of AI\n\n**Narrow AI** (Weak AI): AI designed for specific tasks\n**General AI** (Strong AI): AI with human-level intelligence across all domains'
                }
              },
              {
                id: 'ai-types-2',
                type: 'true-false',
                payload: {
                  prompt: 'Current AI systems like ChatGPT are examples of General AI.',
                  answer: false
                }
              }
            ]
          }
        ]
      }
    ]
  },
  // AI Tools Skills
  {
    id: 'skill-chatgpt-001',
    name: 'Mastering ChatGPT',
    description: 'Learn to use ChatGPT effectively for various tasks',
    version: 1,
    topicId: 'topic-ai-tools',
    order: 1,
    estimatedDuration: 35,
    difficulty: 'beginner',
    levels: [
      {
        name: 'Level 1: ChatGPT Basics',
        lessons: [
          {
            name: 'Getting Started with ChatGPT',
            objective: 'Learn the basics of interacting with ChatGPT',
            exercises: [
              {
                id: 'chatgpt-1',
                type: 'text-info',
                payload: {
                  markdown: '# ChatGPT Fundamentals\n\nChatGPT is a conversational AI that can help with writing, analysis, coding, and creative tasks.'
                }
              },
              {
                id: 'chatgpt-2',
                type: 'single-choice',
                payload: {
                  prompt: 'What makes a good ChatGPT prompt?',
                  choices: ['Being specific and clear', 'Using complex jargon', 'Keeping it very short', 'Using all caps'],
                  answerIndex: 0
                }
              }
            ]
          }
        ]
      }
    ]
  },
  // Time Management Skills
  {
    id: 'skill-pomodoro-001',
    name: 'The Pomodoro Technique',
    description: 'Master the popular time management method',
    version: 1,
    topicId: 'topic-time-management',
    order: 1,
    estimatedDuration: 25,
    difficulty: 'beginner',
    levels: [
      {
        name: 'Level 1: Pomodoro Basics',
        lessons: [
          {
            name: 'Understanding Pomodoro',
            objective: 'Learn the principles of the Pomodoro Technique',
            exercises: [
              {
                id: 'pomodoro-1',
                type: 'text-info',
                payload: {
                  markdown: '# The Pomodoro Technique\n\nWork for 25 minutes, then take a 5-minute break. After 4 cycles, take a longer 15-30 minute break.'
                }
              },
              {
                id: 'pomodoro-2',
                type: 'single-choice',
                payload: {
                  prompt: 'How long is a standard Pomodoro work session?',
                  choices: ['15 minutes', '25 minutes', '30 minutes', '45 minutes'],
                  answerIndex: 1
                }
              }
            ]
          }
        ]
      }
    ]
  }
];

// Define comprehensive hierarchy data
export const HIERARCHY_DATA: HierarchyData = {
  subjects: [
    {
      id: 'subject-ai-fundamentals',
      name: 'AI Fundamentals',
      description: 'Master the core concepts and tools of artificial intelligence',
      category: 'Technology',
      order: 1,
      topics: [],
      icon: '🤖',
      color: '#3B82F6',
      estimatedDuration: 180,
      difficulty: 'beginner',
      isPremium: false
    },
    {
      id: 'subject-machine-learning',
      name: 'Machine Learning',
      description: 'Dive deep into machine learning algorithms and applications',
      category: 'Technology',
      order: 2,
      topics: [],
      icon: '🧠',
      color: '#8B5CF6',
      estimatedDuration: 240,
      difficulty: 'intermediate',
      isPremium: true
    },
    {
      id: 'subject-productivity',
      name: 'Productivity & Focus',
      description: 'Learn proven techniques to boost your productivity and focus',
      category: 'Personal Development',
      order: 3,
      topics: [],
      icon: '⚡',
      color: '#10B981',
      estimatedDuration: 120,
      difficulty: 'beginner',
      isPremium: false
    },
    {
      id: 'subject-data-science',
      name: 'Data Science',
      description: 'Explore data analysis, visualization, and statistical methods',
      category: 'Technology',
      order: 4,
      topics: [],
      icon: '📊',
      color: '#F59E0B',
      estimatedDuration: 300,
      difficulty: 'intermediate',
      isPremium: true
    }
  ],
  topics: [
    // AI Fundamentals Topics
    {
      id: 'topic-ai-basics',
      name: 'AI Basics',
      description: 'Introduction to artificial intelligence concepts and terminology',
      subjectId: 'subject-ai-fundamentals',
      order: 1,
      skills: [],
      estimatedDuration: 60,
      difficulty: 'beginner'
    },
    {
      id: 'topic-ai-tools',
      name: 'AI Tools & Applications',
      description: 'Practical AI tools for everyday use',
      subjectId: 'subject-ai-fundamentals',
      order: 2,
      skills: [],
      estimatedDuration: 90,
      difficulty: 'beginner',
      prerequisites: ['topic-ai-basics']
    },
    {
      id: 'topic-ai-ethics',
      name: 'AI Ethics & Society',
      description: 'Understanding the ethical implications of AI technology',
      subjectId: 'subject-ai-fundamentals',
      order: 3,
      skills: [],
      estimatedDuration: 30,
      difficulty: 'beginner',
      prerequisites: ['topic-ai-basics']
    },
    // Machine Learning Topics
    {
      id: 'topic-ml-foundations',
      name: 'ML Foundations',
      description: 'Core machine learning concepts and algorithms',
      subjectId: 'subject-machine-learning',
      order: 1,
      skills: [],
      estimatedDuration: 120,
      difficulty: 'intermediate'
    },
    {
      id: 'topic-neural-networks',
      name: 'Neural Networks',
      description: 'Deep dive into neural networks and deep learning',
      subjectId: 'subject-machine-learning',
      order: 2,
      skills: [],
      estimatedDuration: 120,
      difficulty: 'advanced',
      prerequisites: ['topic-ml-foundations']
    },
    // Productivity Topics
    {
      id: 'topic-productivity-basics',
      name: 'Focus Fundamentals',
      description: 'Master the basics of focus and attention management',
      subjectId: 'subject-productivity',
      order: 1,
      skills: [],
      estimatedDuration: 60,
      difficulty: 'beginner'
    },
    {
      id: 'topic-time-management',
      name: 'Time Management',
      description: 'Advanced techniques for managing your time effectively',
      subjectId: 'subject-productivity',
      order: 2,
      skills: [],
      estimatedDuration: 60,
      difficulty: 'intermediate',
      prerequisites: ['topic-productivity-basics']
    },
    // Data Science Topics
    {
      id: 'topic-data-analysis',
      name: 'Data Analysis',
      description: 'Learn to analyze and interpret data effectively',
      subjectId: 'subject-data-science',
      order: 1,
      skills: [],
      estimatedDuration: 150,
      difficulty: 'intermediate'
    },
    {
      id: 'topic-data-visualization',
      name: 'Data Visualization',
      description: 'Create compelling visualizations from your data',
      subjectId: 'subject-data-science',
      order: 2,
      skills: [],
      estimatedDuration: 150,
      difficulty: 'intermediate',
      prerequisites: ['topic-data-analysis']
    }
  ],
  skills: existingSkills
};

// Build the complete hierarchy with proper relationships
const buildHierarchy = (): HierarchyData => {
  const subjects = HIERARCHY_DATA.subjects.map(subject => ({
    ...subject,
    topics: getTopicsBySubjectId(subject.id)
  }));

  const topics = HIERARCHY_DATA.topics.map(topic => ({
    ...topic,
    skills: getSkillsByTopicId(topic.id)
  }));

  return {
    subjects,
    topics,
    skills: existingSkills
  };
};

// Helper functions for working with hierarchy data
export const getSubjectById = (id: string): Subject | undefined => {
  return HIERARCHY_DATA.subjects.find(subject => subject.id === id);
};

export const getTopicById = (id: string): Topic | undefined => {
  return HIERARCHY_DATA.topics.find(topic => topic.id === id);
};

export const getSkillById = (id: string): Skill | undefined => {
  return HIERARCHY_DATA.skills.find(skill => skill.id === id);
};

export const getTopicsBySubjectId = (subjectId: string): Topic[] => {
  return HIERARCHY_DATA.topics
    .filter(topic => topic.subjectId === subjectId)
    .sort((a, b) => a.order - b.order);
};

export const getSkillsByTopicId = (topicId: string): Skill[] => {
  return HIERARCHY_DATA.skills
    .filter(skill => skill.topicId === topicId)
    .sort((a, b) => (a.order || 0) - (b.order || 0));
};

export const getAllSubjects = (): Subject[] => {
  return HIERARCHY_DATA.subjects.sort((a, b) => a.order - b.order);
};

// Export the built hierarchy
export const COMPLETE_HIERARCHY = buildHierarchy();

// Calculate completion statistics
export const calculateSubjectProgress = (subjectId: string, skillProgresses: Record<string, any>): number => {
  const topics = getTopicsBySubjectId(subjectId);
  if (topics.length === 0) return 0;
  
  let totalSkills = 0;
  let completedSkills = 0;
  
  topics.forEach(topic => {
    const skills = getSkillsByTopicId(topic.id);
    totalSkills += skills.length;
    
    skills.forEach(skill => {
      if (skillProgresses[skill.id]?.isCompleted) {
        completedSkills++;
      }
    });
  });
  
  return totalSkills > 0 ? Math.round((completedSkills / totalSkills) * 100) : 0;
};

export const calculateTopicProgress = (topicId: string, skillProgresses: Record<string, any>): number => {
  const skills = getSkillsByTopicId(topicId);
  if (skills.length === 0) return 0;
  
  const completedSkills = skills.filter(skill => skillProgresses[skill.id]?.isCompleted).length;
  return Math.round((completedSkills / skills.length) * 100);
};
