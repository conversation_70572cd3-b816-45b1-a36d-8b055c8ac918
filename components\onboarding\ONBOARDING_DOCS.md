# Onboarding Flow Documentation

This document explains how to configure and extend the onboarding flow in this project. You can easily add, remove, or modify onboarding screens by editing the `surveyData` array in `components/onboarding/config.tsx`.

---

## How It Works

- The onboarding flow is driven by the `surveyData` array.
- Each object in the array represents a slide.
- The `type` property determines which UI component is rendered.
- You can add, remove, or reorder slides as needed.

---

## Slide Types & Config Examples

### 1. **Feature Slide**

A simple info or welcome slide.

```js
{
  id: 'welcome',
  type: 'feature',
  step: 'WELCOME',
  icon: '🌱',
  title: 'Unlock Your Health Potential.',
  description: 'Answer a few questions to generate your personalized health plan. It only takes a minute.',
}
```

### 2. **Multiple Choice**

Single-select options.

```js
{
  id: 'workout-frequency',
  type: 'multiple-choice',
  question: 'How often do you work out?',
  options: [
    { id: 'newbie', text: 'Now and then', icon: '🚶' },
    { id: 'regular', text: '2-3 times a week', icon: '🏃' },
    { id: 'dedicated', text: 'Dedicated athlete', icon: '🏋️' },
  ],
}
```

### 3. **Multiple Select**

Multi-select options.

```js
{
  id: 'motivation',
  type: 'multiple-select',
  question: 'What are your core motivations?',
  description: "What's driving you? Select all that apply.",
  options: [
    { id: 'energy', text: 'Boost energy', icon: '⚡' },
    { id: 'motivation', text: 'Stay motivated', icon: '🎯' },
    { id: 'feel-better', text: 'Feel better', icon: '😊' },
    { id: 'lose-weight', text: 'Lose weight', icon: '📉' },
  ],
}
```

### 4. **Slider**

Range selection (uses a row of buttons for each value).

```js
{
  id: 'sleep-duration',
  type: 'slider',
  question: 'How many hours do you sleep on average?',
  min: 0,
  max: 12,
  step: 1,
  unit: 'hours',
  default: 7,
}
```

### 5. **Input**

Text or number input.

```js
{
  id: 'age',
  type: 'input',
  inputType: 'number', // or 'text'
  question: 'What is your age?',
  placeholder: 'Enter your age',
  validation: {
    min: 12,
    max: 100,
  },
}
```

### 6. **Yes/No**

Binary choice.

```js
{
  id: 'smoke',
  type: 'yes-no',
  question: 'Do you smoke?',
  yesLabel: 'Yes 🚬',
  noLabel: 'No 🙅',
}
```

### 7. **Date Picker**

Date entry (uses a text input for date in YYYY-MM-DD format).

```js
{
  id: 'birthdate',
  type: 'date-picker',
  question: 'When is your birthday?',
  mode: 'date',
}
```

### 8. **Image Select**

Visual choices (must include `imageUrl`).

```js
{
  id: 'body-goal',
  type: 'image-select',
  question: 'What body goal do you aim for?',
  options: [
    { id: 'lean', text: 'Lean', imageUrl: 'https://via.placeholder.com/48x48?text=Lean' },
    { id: 'muscular', text: 'Muscular', imageUrl: 'https://via.placeholder.com/48x48?text=Muscular' },
    { id: 'balanced', text: 'Balanced', imageUrl: 'https://via.placeholder.com/48x48?text=Balanced' },
  ],
}
```

### 9. **Toggle Group**

Multiple toggles (switches).

```js
{
  id: 'notifications',
  type: 'toggle-group',
  question: 'Which notifications do you want to receive?',
  options: [
    { id: 'reminders', text: 'Daily reminders', default: true },
    { id: 'tips', text: 'Health tips', default: false },
    { id: 'checkins', text: 'Weekly check-ins', default: true },
  ],
}
```

### 10. **Ranking**

Drag-and-drop or up/down ranking.

```js
{
  id: 'health-priorities',
  type: 'ranking',
  question: 'Rank your top health priorities:',
  options: [
    { id: 'strength', text: 'Strength' },
    { id: 'flexibility', text: 'Flexibility' },
    { id: 'endurance', text: 'Endurance' },
    { id: 'balance', text: 'Balance' },
  ],
}
```

### 11. **Animated Slide**

Show a Lottie animation or illustration.

```js
{
  id: 'fun-animation',
  type: 'animated',
  animationUrl: 'https://assets9.lottiefiles.com/packages/lf20_5ngs2ksb.json',
  title: 'Stay motivated!',
  description: 'Our mascot will cheer you on every day.',
}
```

### 12. **Permission Request Slide**

Ask for permissions (notifications, etc.).

```js
{
  id: 'notifications-permission',
  type: 'permission',
  permissionType: 'notifications',
  question: 'Enable notifications to stay on track!',
  description: "We'll remind you about your goals and progress.",
  allowLabel: 'Allow',
  denyLabel: 'Not now',
  illustration: '🔔',
}
```

---

## How to Add a New Slide

1. Open `components/onboarding/config.tsx`.
2. Add a new object to the `surveyData` array, following the examples above.
3. Use the correct `type` and required fields for your slide.
4. Save and reload the app to see your new slide in the onboarding flow.

---

## How to Add a New Slide Type

1. Create a new component in `components/onboarding/` (e.g., `MyCustomSlide.tsx`).
2. Add a new case in the main onboarding file to render your component when `slide.type` matches.
3. Document the config structure for your new type in this file.

---

## Notes

- All slides are theme-aware and support dark mode.
- You can reorder slides by changing their order in the array.
- You can remove a slide by deleting its object from the array.
- For advanced slides (e.g., animated, permission), you may need to handle side effects (like requesting permissions) in the slide component.

---

## Extending Further

- You can add more slide types (e.g., video, checklist, tutorial) by following the same pattern.
- For Lottie animations, install and use `lottie-react-native`.
- For permissions, use Expo or React Native APIs in the `onAllow` handler.

---

**Happy onboarding building!**
