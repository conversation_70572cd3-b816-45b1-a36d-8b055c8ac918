import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { MatchingPairsExercise } from "@/types/skill";
import { AnswerFeedback } from "../store";
import { shuffle } from 'lodash';
// @ts-ignore
// Fix for missing type declaration for lodash.shuffle

interface Props {
  exercise: MatchingPairsExercise;
  onAnswer: (answer: [number, number][]) => void;
  currentAnswer?: [number, number][];
  feedback?: AnswerFeedback;
}

export default function MatchingPairs({ exercise, onAnswer }: Props) {
  const { prompt, left, right, pairs } = exercise.payload;
  
  const [shuffledRight, setShuffledRight] = useState<{ text: string; index: number }[]>([]);
  const [selectedLeft, setSelectedLeft] = useState<number | null>(null);
  const [matchedPairs, setMatchedPairs] = useState<[number, number][]>([]);
  const [incorrectAttempt, setIncorrectAttempt] = useState<{ left: number, right: number } | null>(null);

  useEffect(() => {
    setShuffledRight(shuffle(right.map((text, index) => ({ text, index }))));
  }, [right]);
  
  const isLeftSelected = (index: number) => selectedLeft === index;
  const isLeftMatched = (index: number) => matchedPairs.some(p => p[0] === index);
  const isRightMatched = (index: number) => matchedPairs.some(p => p[1] === index);
  const isIncorrect = (side: 'left' | 'right', index: number) => {
    if (!incorrectAttempt) return false;
    return (side === 'left' && incorrectAttempt.left === index) || (side === 'right' && incorrectAttempt.right === index);
  };

  const handleSelect = (side: 'left' | 'right', index: number) => {
    if (incorrectAttempt) return;

    if (side === 'left') {
      if (!isLeftMatched(index)) {
        setSelectedLeft(index);
      }
    } else {
      if (selectedLeft !== null && !isRightMatched(index)) {
        const isCorrect = pairs.some(p => p[0] === selectedLeft && p[1] === index);
        if (isCorrect) {
          const newPairs = [...matchedPairs, [selectedLeft, index] as [number, number]];
          setMatchedPairs(newPairs);
          if (newPairs.length === pairs.length) {
            onAnswer(newPairs);
          }
        } else {
          setIncorrectAttempt({ left: selectedLeft, right: index });
          setTimeout(() => setIncorrectAttempt(null), 500);
        }
        setSelectedLeft(null);
      }
    }
  };

  return (
    <View>
      <Text style={styles.prompt}>{prompt}</Text>
      <View style={styles.columnsContainer}>
        <View style={styles.column}>
          {left.map((text, i) => (
            <TouchableOpacity
              key={`left-${i}`}
              style={[
                styles.card,
                isLeftSelected(i) && styles.selectedCard,
                isLeftMatched(i) && styles.matchedCard,
                isIncorrect('left', i) && styles.incorrectCard,
              ]}
              onPress={() => handleSelect('left', i)}
              disabled={isLeftMatched(i) || incorrectAttempt !== null}
            >
              <Text style={styles.cardText}>{text}</Text>
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.column}>
          {shuffledRight.map((item) => (
            <TouchableOpacity
              key={`right-${item.index}`}
              style={[
                styles.card,
                isRightMatched(item.index) && styles.matchedCard,
                isIncorrect('right', item.index) && styles.incorrectCard,
              ]}
              onPress={() => handleSelect('right', item.index)}
              disabled={isRightMatched(item.index) || selectedLeft === null || incorrectAttempt !== null}
            >
              <Text style={styles.cardText}>{item.text}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  prompt: { fontSize: 28, fontWeight: 'bold', marginBottom: 24, textAlign: 'center' },
  columnsContainer: { flexDirection: 'row', justifyContent: 'space-around' },
  column: { width: '45%', gap: 12 },
  card: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedCard: { borderColor: '#3B82F6', backgroundColor: '#EFF6FF' },
  matchedCard: { borderColor: '#10B981', backgroundColor: '#D1FAE5', opacity: 0.7 },
  incorrectCard: { borderColor: '#EF4444', backgroundColor: '#FEE2E2' },
  cardText: { fontSize: 18, fontWeight: '500' },
}); 