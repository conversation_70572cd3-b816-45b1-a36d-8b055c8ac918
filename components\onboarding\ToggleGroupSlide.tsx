import React from 'react';
import { View, Text, Switch, StyleSheet } from 'react-native';

interface ToggleOption {
  id: string;
  text: string;
  default?: boolean;
}

interface ToggleGroupSlideProps {
  question: string;
  options: ToggleOption[];
  value: Record<string, boolean>;
  onChange: (v: Record<string, boolean>) => void;
  color: string;
}

export function ToggleGroupSlide({ question, options, value, onChange, color }: ToggleGroupSlideProps) {
  return (
    <View style={{ width: '100%' }}>
      <Text style={styles.question}>{question}</Text>
      {options.map(option => (
        <View key={option.id} style={styles.row}>
          <Text style={styles.text}>{option.text}</Text>
          <Switch
            value={!!value[option.id]}
            onValueChange={v => onChange({ ...value, [option.id]: v })}
            trackColor={{ true: color, false: '#ccc' }}
            thumbColor={!!value[option.id] ? color : '#eee'}
          />
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  question: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  text: {
    fontSize: 18,
    color: '#222',
  },
}); 