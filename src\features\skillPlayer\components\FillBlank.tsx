import React, { useState } from "react";
import { View, TextInput, Text, TouchableOpacity, StyleSheet } from "react-native";
import { FillBlankExercise } from "@/types/skill";
import { AnswerFeedback } from "../store";

interface Props {
  exercise: FillBlankExercise;
  onAnswer: (value: string) => void;
  currentAnswer?: string;
  feedback?: AnswerFeedback;
}

export default function FillBlank({ exercise, onAnswer, currentAnswer = "", feedback }: Props) {
  const [text, setText] = useState(currentAnswer);
  const { promptWithBlank, choices } = exercise.payload;

  const submit = (val: string) => {
    setText(val);
    onAnswer(val);
  };

  const textParts = promptWithBlank.split("___");

  const getInputStyle = () => {
    if (feedback?.showFeedback) {
      if (feedback.isCorrect) {
        return [styles.textInput, styles.correctInput];
      } else {
        return [styles.textInput, styles.incorrectInput];
      }
    }
    return styles.textInput;
  };

  return (
    <View style={styles.container}>
      <View style={styles.promptContainer}>
        <Text style={styles.promptText}>{textParts[0]}</Text>
        <TextInput
          value={text}
          onChangeText={submit}
          style={getInputStyle()}
          textAlign="center"
          placeholder="?"
        />
        <Text style={styles.promptText}>{textParts[1]}</Text>
      </View>

      {choices && (
        <View style={styles.choicesContainer}>
          {choices.map((c, idx) => (
            <TouchableOpacity key={idx} onPress={() => submit(c)} style={styles.choiceChip}>
              <Text style={styles.choiceText}>{c || "(blank)"}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "space-between",
  },
  promptContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 40,
  },
  promptText: {
    fontSize: 24,
    fontWeight: "500",
  },
  textInput: {
    fontSize: 20,
    fontWeight: "bold",
    minWidth: 80,
    paddingVertical: 8,
    borderBottomWidth: 2,
    borderColor: "#E5E7EB",
    marginHorizontal: 8,
  },
  correctInput: {
    borderColor: "#10B981",
    backgroundColor: "#ECFDF5",
  },
  incorrectInput: {
    borderColor: "#EF4444",
    backgroundColor: "#FEF2F2",
  },
  choicesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    paddingVertical: 24,
    gap: 12,
  },
  choiceChip: {
    backgroundColor: "#FFFFFF",
    borderWidth: 2,
    borderColor: "#E5E7EB",
    borderRadius: 16,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  choiceText: {
    fontSize: 18,
    fontWeight: "500",
  },
});
