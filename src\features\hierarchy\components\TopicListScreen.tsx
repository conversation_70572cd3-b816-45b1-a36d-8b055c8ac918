import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { useUser } from '@clerk/clerk-expo';
import { useTheme } from '@/lib/theme';
import { Subject, Topic, TopicProgress } from '@/types/skill';
import { hierarchyService } from '../HierarchyService';
import ProgressBar from '@/features/skillPlayer/components/ProgressBar';

const { width } = Dimensions.get('window');

interface TopicCardProps {
  topic: Topic;
  progress: number;
  index: number;
  onPress: () => void;
  isLocked: boolean;
  isCompleted: boolean;
}

const TopicCard: React.FC<TopicCardProps> = ({
  topic,
  progress,
  index,
  onPress,
  isLocked,
  isCompleted,
}) => {
  const { theme, colors } = useTheme();
  const scale = useSharedValue(1);
  const elevation = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      shadowOpacity: interpolate(elevation.value, [1, 1.5], [0.1, 0.25]),
      shadowRadius: interpolate(elevation.value, [1, 1.5], [4, 8]),
      elevation: interpolate(elevation.value, [1, 1.5], [2, 6]),
    };
  });

  const handlePressIn = () => {
    if (!isLocked) {
      scale.value = withSpring(0.97, { damping: 15, stiffness: 150 });
      elevation.value = withTiming(1.5, { duration: 150 });
    }
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 150 });
    elevation.value = withTiming(1, { duration: 150 });
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'beginner':
        return colors.success[500];
      case 'intermediate':
        return colors.warning[500];
      case 'advanced':
        return colors.error[500];
      default:
        return theme.secondaryText;
    }
  };

  const getTopicIcon = (index: number) => {
    const icons = ['📖', '🔬', '🎯', '🚀', '💡', '🔧', '📊', '🎨'];
    return icons[index % icons.length];
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={isLocked}
      activeOpacity={0.9}
    >
      <Animated.View
        style={[
          styles.topicCard,
          {
            backgroundColor: theme.card,
            borderColor: isCompleted ? colors.success[500] : theme.border,
            shadowColor: theme.shadow,
          },
          animatedStyle,
          isLocked && { opacity: 0.6 },
          isCompleted && { borderWidth: 2 },
        ]}
      >
        {/* Topic Header */}
        <View style={styles.cardHeader}>
          <View
            style={[
              styles.iconContainer,
              {
                backgroundColor: isCompleted
                  ? colors.success[500]
                  : colors.primary[100],
              },
            ]}
          >
            <Text style={styles.topicIcon}>
              {isCompleted ? '✅' : getTopicIcon(index)}
            </Text>
          </View>
          
          {isLocked && (
            <View style={styles.lockIcon}>
              <Ionicons name="lock-closed" size={16} color={theme.secondaryText} />
            </View>
          )}
          
          {isCompleted && (
            <View style={styles.completedBadge}>
              <Ionicons name="checkmark-circle" size={20} color={colors.success[500]} />
            </View>
          )}
        </View>

        {/* Topic Content */}
        <View style={styles.cardContent}>
          <Text style={[styles.topicTitle, { color: theme.text }]} numberOfLines={2}>
            {topic.name}
          </Text>
          <Text style={[styles.topicDescription, { color: theme.secondaryText }]} numberOfLines={3}>
            {topic.description}
          </Text>

          {/* Meta Information */}
          <View style={styles.metaInfo}>
            {topic.difficulty && (
              <View style={styles.difficultyContainer}>
                <Ionicons
                  name="trending-up-outline"
                  size={14}
                  color={getDifficultyColor(topic.difficulty)}
                />
                <Text
                  style={[
                    styles.difficultyText,
                    { color: getDifficultyColor(topic.difficulty) },
                  ]}
                >
                  {topic.difficulty}
                </Text>
              </View>
            )}
            
            {topic.estimatedDuration && (
              <View style={styles.durationContainer}>
                <Ionicons name="time-outline" size={14} color={theme.secondaryText} />
                <Text style={[styles.durationText, { color: theme.secondaryText }]}>
                  {topic.estimatedDuration}m
                </Text>
              </View>
            )}
          </View>

          {/* Skills Count */}
          <Text style={[styles.skillsCount, { color: theme.secondaryText }]}>
            {topic.skills?.length || 0} skills
          </Text>

          {/* Progress Bar */}
          <View style={styles.progressContainer}>
            <ProgressBar progress={progress / 100} animated={true} />
            <Text style={[styles.progressText, { color: theme.secondaryText }]}>
              {progress}% complete
            </Text>
          </View>
        </View>

        {/* Continue Arrow */}
        {!isLocked && (
          <View style={styles.arrowContainer}>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={isCompleted ? colors.success[500] : colors.primary[500]}
            />
          </View>
        )}
      </Animated.View>
    </TouchableOpacity>
  );
};

interface TopicListScreenProps {
  subjectId: string;
  onTopicSelect: (topicId: string) => void;
  onBack: () => void;
}

export default function TopicListScreen({ subjectId, onTopicSelect, onBack }: TopicListScreenProps) {
  const { user } = useUser();
  const { theme, colors } = useTheme();

  const [subject, setSubject] = useState<Subject | null>(null);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [topicProgresses, setTopicProgresses] = useState<Record<string, number>>({});
  const [completedTopics, setCompletedTopics] = useState<Set<string>>(new Set());
  const [lockedTopics, setLockedTopics] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTopics();
  }, [subjectId, user]);

  const loadTopics = async () => {
    try {
      setLoading(true);
      
      // Load subject info
      const subjectData = await hierarchyService.getSubjectById(subjectId);
      setSubject(subjectData || null);

      // Load topics for this subject
      const topicsData = await hierarchyService.getTopicsBySubjectId(subjectId);
      setTopics(topicsData);

      // Load progress and completion status for each topic
      const progresses: Record<string, number> = {};
      const completed = new Set<string>();
      const locked = new Set<string>();

      for (const topic of topicsData) {
        // Check if topic is unlocked
        const isUnlocked = await hierarchyService.isTopicUnlocked(topic.id, user?.id);
        if (!isUnlocked) {
          locked.add(topic.id);
        }

        // Load progress
        const progress = await hierarchyService.getTopicProgress(topic.id, user?.id);
        if (progress) {
          progresses[topic.id] = calculateProgressPercentage(progress);
          if (progress.isCompleted) {
            completed.add(topic.id);
          }
        } else {
          progresses[topic.id] = 0;
        }
      }

      setTopicProgresses(progresses);
      setCompletedTopics(completed);
      setLockedTopics(locked);
    } catch (error) {
      console.error('Error loading topics:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateProgressPercentage = (progress: TopicProgress): number => {
    if (!progress || !progress.skillProgresses) return 0;
    
    const skillIds = Object.keys(progress.skillProgresses);
    if (skillIds.length === 0) return 0;
    
    const completedSkills = skillIds.filter(
      id => progress.skillProgresses[id]?.isCompleted
    ).length;
    
    return Math.round((completedSkills / skillIds.length) * 100);
  };

  const handleTopicPress = (topic: Topic) => {
    if (lockedTopics.has(topic.id)) {
      // Show locked message or navigate to prerequisites
      return;
    }
    onTopicSelect(topic.id);
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.text }]}>Loading topics...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color={theme.text} />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.text }]} numberOfLines={2}>
            {subject?.name || 'Topics'}
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.secondaryText }]}>
            Choose a topic to explore
          </Text>
        </View>
      </View>

      {/* Topics List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {topics.map((topic, index) => (
          <TopicCard
            key={topic.id}
            topic={topic}
            progress={topicProgresses[topic.id] || 0}
            index={index}
            onPress={() => handleTopicPress(topic)}
            isLocked={lockedTopics.has(topic.id)}
            isCompleted={completedTopics.has(topic.id)}
          />
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  backButton: {
    marginRight: 16,
    padding: 4,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  topicCard: {
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 16,
    padding: 20,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  topicIcon: {
    fontSize: 20,
  },
  lockIcon: {
    marginLeft: 'auto',
  },
  completedBadge: {
    marginLeft: 'auto',
  },
  cardContent: {
    flex: 1,
  },
  topicTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    lineHeight: 22,
  },
  topicDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  metaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  difficultyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  durationText: {
    fontSize: 12,
    marginLeft: 4,
  },
  skillsCount: {
    fontSize: 12,
    marginBottom: 12,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressText: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'right',
  },
  arrowContainer: {
    position: 'absolute',
    right: 20,
    top: '50%',
    transform: [{ translateY: -10 }],
  },
});
