import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { hapticService } from '../services/HapticService';
import { audioService } from '../services/AudioService';
import ParticleEffects from './ParticleEffects';
import StreakCounter from './StreakCounter';
import ProgressBar from './ProgressBar';
import SettingsScreen from './SettingsScreen';

export default function EngagementDemo() {
  const [showParticles, setShowParticles] = useState(false);
  const [particleType, setParticleType] = useState<'correct' | 'celebration' | 'milestone'>('correct');
  const [streak, setStreak] = useState(0);
  const [progress, setProgress] = useState(0.3);
  const [showSettings, setShowSettings] = useState(false);

  React.useEffect(() => {
    audioService.initialize();
    return () => audioService.cleanup();
  }, []);

  const triggerHaptic = async (type: string) => {
    switch (type) {
      case 'selection':
        await hapticService.onAnswerSelect();
        break;
      case 'correct':
        await hapticService.onCorrectAnswer();
        break;
      case 'incorrect':
        await hapticService.onIncorrectAnswer();
        break;
      case 'celebration':
        await hapticService.onExerciseComplete();
        break;
      case 'milestone':
        await hapticService.onStreakAchievement();
        break;
    }
  };

  const triggerAudio = async (type: string) => {
    switch (type) {
      case 'correct':
        await audioService.playCorrect();
        break;
      case 'incorrect':
        await audioService.playIncorrect();
        break;
      case 'selection':
        await audioService.playSelection();
        break;
      case 'button':
        await audioService.playButtonPress();
        break;
      case 'exercise':
        await audioService.playExerciseComplete();
        break;
      case 'lesson':
        await audioService.playLessonComplete();
        break;
      case 'streak':
        await audioService.playStreak();
        break;
      case 'milestone':
        await audioService.playMilestone();
        break;
    }
  };

  const triggerParticles = (type: 'correct' | 'celebration' | 'milestone') => {
    setParticleType(type);
    setShowParticles(true);
  };

  const incrementStreak = () => {
    setStreak(prev => prev + 1);
    triggerHaptic('correct');
    triggerAudio('correct');
    triggerParticles('correct');
  };

  const resetStreak = () => {
    setStreak(0);
    triggerHaptic('incorrect');
    triggerAudio('incorrect');
  };

  const updateProgress = () => {
    setProgress(prev => {
      const newProgress = prev + 0.1;
      if (newProgress >= 1) {
        triggerParticles('celebration');
        triggerHaptic('celebration');
        triggerAudio('lesson');
        return 0;
      }
      return newProgress;
    });
  };

  if (showSettings) {
    return <SettingsScreen onClose={() => setShowSettings(false)} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ParticleEffects
        trigger={showParticles}
        type={particleType}
        onComplete={() => setShowParticles(false)}
      />

      <StreakCounter
        streak={streak}
        isVisible={streak > 0}
        onStreakMilestone={(streak) => {
          triggerParticles('milestone');
          triggerHaptic('milestone');
          triggerAudio('milestone');
        }}
      />

      <View style={styles.header}>
        <Text style={styles.title}>Engagement Demo</Text>
        <TouchableOpacity onPress={() => setShowSettings(true)} style={styles.settingsButton}>
          <Ionicons name="settings" size={24} color="#6366F1" />
        </TouchableOpacity>
      </View>

      <View style={styles.progressSection}>
        <Text style={styles.sectionTitle}>Progress Bar</Text>
        <ProgressBar progress={progress} animated={true} />
        <TouchableOpacity style={styles.button} onPress={updateProgress}>
          <Text style={styles.buttonText}>Update Progress</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Haptic Feedback</Text>
          <View style={styles.buttonGrid}>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerHaptic('selection')}>
              <Text style={styles.demoButtonText}>Selection</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerHaptic('correct')}>
              <Text style={styles.demoButtonText}>Correct</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerHaptic('incorrect')}>
              <Text style={styles.demoButtonText}>Incorrect</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerHaptic('celebration')}>
              <Text style={styles.demoButtonText}>Celebration</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Audio Feedback</Text>
          <View style={styles.buttonGrid}>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerAudio('correct')}>
              <Text style={styles.demoButtonText}>Correct</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerAudio('incorrect')}>
              <Text style={styles.demoButtonText}>Incorrect</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerAudio('button')}>
              <Text style={styles.demoButtonText}>Button</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerAudio('streak')}>
              <Text style={styles.demoButtonText}>Streak</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Particle Effects</Text>
          <View style={styles.buttonGrid}>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerParticles('correct')}>
              <Text style={styles.demoButtonText}>Correct</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerParticles('celebration')}>
              <Text style={styles.demoButtonText}>Celebration</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.demoButton} onPress={() => triggerParticles('milestone')}>
              <Text style={styles.demoButtonText}>Milestone</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Streak Counter</Text>
          <View style={styles.streakControls}>
            <TouchableOpacity style={styles.streakButton} onPress={incrementStreak}>
              <Text style={styles.buttonText}>Correct Answer (+1)</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.streakButton, styles.resetButton]} onPress={resetStreak}>
              <Text style={[styles.buttonText, styles.resetButtonText]}>Reset Streak</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  settingsButton: {
    padding: 8,
  },
  progressSection: {
    backgroundColor: 'white',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginTop: 16,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  buttonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  demoButton: {
    backgroundColor: '#6366F1',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 80,
  },
  demoButtonText: {
    color: 'white',
    fontWeight: '500',
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#6366F1',
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    textAlign: 'center',
  },
  streakControls: {
    gap: 12,
  },
  streakButton: {
    backgroundColor: '#10B981',
    paddingVertical: 16,
    borderRadius: 8,
  },
  resetButton: {
    backgroundColor: '#EF4444',
  },
  resetButtonText: {
    color: 'white',
  },
});
