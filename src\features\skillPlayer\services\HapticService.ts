import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

export enum HapticType {
  SELECTION = 'selection',
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
  LIGHT = 'light',
  MEDIUM = 'medium',
  HEAVY = 'heavy',
  CELEBRATION = 'celebration',
  MILESTONE = 'milestone'
}

class HapticService {
  private isEnabled: boolean = true;

  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  getEnabled(): boolean {
    return this.isEnabled;
  }

  async trigger(type: HapticType) {
    if (!this.isEnabled) return;

    try {
      switch (type) {
        case HapticType.SELECTION:
          await Haptics.selectionAsync();
          break;

        case HapticType.SUCCESS:
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;

        case HapticType.ERROR:
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;

        case HapticType.WARNING:
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;

        case HapticType.LIGHT:
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;

        case HapticType.MEDIUM:
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;

        case HapticType.HEAVY:
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;

        case HapticType.CELEBRATION:
          // Custom celebration pattern
          await this.celebrationPattern();
          break;

        case HapticType.MILESTONE:
          // Custom milestone pattern
          await this.milestonePattern();
          break;

        default:
          await Haptics.selectionAsync();
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  private async celebrationPattern() {
    // Success burst pattern
    await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    await new Promise(resolve => setTimeout(resolve, 100));
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    await new Promise(resolve => setTimeout(resolve, 50));
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }

  private async milestonePattern() {
    // Achievement unlock pattern
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    await new Promise(resolve => setTimeout(resolve, 150));
    await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    await new Promise(resolve => setTimeout(resolve, 100));
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    await new Promise(resolve => setTimeout(resolve, 50));
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }

  // Convenience methods for common patterns
  async onAnswerSelect() {
    await this.trigger(HapticType.SELECTION);
  }

  async onCorrectAnswer() {
    await this.trigger(HapticType.SUCCESS);
  }

  async onIncorrectAnswer() {
    await this.trigger(HapticType.ERROR);
  }

  async onExerciseComplete() {
    await this.trigger(HapticType.CELEBRATION);
  }

  async onLessonComplete() {
    await this.trigger(HapticType.MILESTONE);
  }

  async onButtonPress() {
    await this.trigger(HapticType.LIGHT);
  }

  async onStreakAchievement() {
    await this.trigger(HapticType.CELEBRATION);
  }
}

export const hapticService = new HapticService();
