import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { OptionCard } from './OptionCard';

export const MultipleSelectSlide = ({ slide, answer, setAnswer, theme }: { slide: any, answer: any, setAnswer: any, theme: any }) => (
  <View style={{ flex: 1, width: '100%' }}>
    <Text style={[styles.question, { color: theme.text }]}>{slide.question}</Text>
    {slide.description && (
      <Text style={[styles.subDescription, { color: theme.secondaryText }]}>{slide.description}</Text>
    )}
    <View style={{ marginTop: 40 }}>
      {Array.isArray(slide.options) && slide.options.map((option: any) => {
        const isSelected = Array.isArray(answer) && answer.includes(option.id);
        return (
          <OptionCard
            key={option.id}
            text={option.text}
            selected={isSelected}
            onPress={() => {
              if (!Array.isArray(answer)) setAnswer([option.id]);
              else if (isSelected) setAnswer(answer.filter((id: string) => id !== option.id));
              else setAnswer([...answer, option.id]);
            }}
            color={theme.primaryButton}
          />
        );
      })}
    </View>
  </View>
);

const styles = StyleSheet.create({
    question: {
        fontSize: 26,
        fontWeight: 'bold',
        color: '#222',
        marginBottom: 8,
        marginTop: 12,
    },
    subDescription: {
        fontSize: 16,
        color: '#888',
        marginBottom: 16,
    },
});
