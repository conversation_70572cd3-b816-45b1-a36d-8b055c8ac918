import React, { useEffect, useState } from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  withTiming,
  interpolate,
  Extrapolate,
} from "react-native-reanimated";
import { ButtonState } from "../store";
import { hapticService } from "../services/HapticService";
import { audioService } from "../services/AudioService";
import { useSettingsStore } from "../stores/SettingsStore";
import { Svg, Path } from "react-native-svg";
import { useTheme, useThemedStyles } from '~/lib/theme';
import { getThemedShadow } from '~/lib/themeUtils';

interface Props {
  buttonState: ButtonState;
  onCheck: () => void;
  onContinue: () => void;
  disabled?: boolean;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedSvg = Animated.createAnimatedComponent(Svg);

export default function CheckContinueButton({ buttonState, onCheck, onContinue, disabled }: Props) {
  const insets = useSafeAreaInsets();
  const { animationsEnabled, reducedMotion } = useSettingsStore();
  const { colors } = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  // Animation values
  const scale = useSharedValue(1);
  const buttonScale = useSharedValue(1);
  const svgPosition = useSharedValue(0);
  const textOpacity = useSharedValue(1);

  useEffect(() => {
    // Button state change animation
    if (animationsEnabled && !reducedMotion) {
      buttonScale.value = withSequence(
        withTiming(0.95, { duration: 100 }),
        withSpring(1, { damping: 15, stiffness: 300 })
      );
    }
  }, [buttonState, animationsEnabled, reducedMotion]);

  const handlePress = async () => {
    // Haptic feedback
    await hapticService.onButtonPress();

    // Audio feedback
    await audioService.playButtonPress();

    // Press animation
    if (animationsEnabled && !reducedMotion) {
      scale.value = withSequence(
        withTiming(0.95, { duration: 100 }),
        withSpring(1, { damping: 15, stiffness: 300 })
      );

      // Simulate active state with transform
      buttonScale.value = withSequence(
        withTiming(0.98, { duration: 100 }),
        withTiming(1, { duration: 300 })
      );
    }

    // Execute the actual action
    if (buttonState === 'check') {
      onCheck();
    } else {
      onContinue();
    }
  };

  // Simulate hover effect for demo purposes
  const handlePressIn = () => {
    setIsHovered(true);
    textOpacity.value = withTiming(0, { duration: 300 });
    svgPosition.value = withTiming(1, { duration: 300 });
  };

  const handlePressOut = () => {
    setIsHovered(false);
    textOpacity.value = withTiming(1, { duration: 300 });
    svgPosition.value = withTiming(0, { duration: 300 });
  };

  const getButtonText = () => {
    return buttonState === 'check' ? 'CHECK' : 'CONTINUE';
  };

  // Create theme-aware styles
  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    wrapper: {
      paddingHorizontal: 24,
      paddingTop: 12,
      borderTopWidth: 1,
      borderColor: theme.border,
      backgroundColor: theme.card,
    },
    btn: {
      position: 'relative',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: 40,
      paddingHorizontal: 20,
      backgroundColor: theme.primaryButton,
      borderRadius: 10,
      ...getThemedShadow(theme, 'md'),
    },
    btnDisabled: {
      backgroundColor: theme.muted,
      shadowColor: colors.gray[400],
    },
    label: {
      fontSize: 16,
      fontWeight: "bold",
      color: colors.white,
      letterSpacing: 0.5,
    },
    svg: {
      position: 'absolute',
      right: 20,
      width: 16,
      height: 16,
    },
  }));

  const animatedButtonStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value * buttonScale.value },
      { translateY: isHovered ? 3 : 0 },
      { translateX: isHovered ? 3 : 0 },
    ],
    // Use theme-aware shadow colors
    boxShadow: isHovered
      ? `2px 2px 10px ${colors.primary[600]}`
      : `5px 5px 10px ${colors.primary[600]}`,
  }));

  const animatedTextStyle = useAnimatedStyle(() => ({
    opacity: textOpacity.value,
  }));

  const animatedSvgStyle = useAnimatedStyle(() => ({
    transform: [
      { 
        translateX: interpolate(
          svgPosition.value, 
          [0, 1], 
          [0, -50], 
          Extrapolate.CLAMP
        ) 
      }
    ],
    opacity: interpolate(
      svgPosition.value,
      [0, 0.5, 1],
      [1, 0.7, 1],
      Extrapolate.CLAMP
    ),
  }));

  return (
    <View style={[styles.wrapper, { paddingBottom: insets.bottom || 12 }]}>
      <AnimatedTouchableOpacity
        activeOpacity={0.8}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        style={[styles.btn, animatedButtonStyle, disabled && styles.btnDisabled]}
      >
        <Animated.Text style={[styles.label, animatedTextStyle]}>
          {getButtonText()}
        </Animated.Text>
        
        <AnimatedSvg
          style={[styles.svg, animatedSvgStyle]}
          width={16}
          height={16}
          viewBox="0 0 512 512"
        >
          <Path
            d="M410.3 231l11.3-11.3-33.9-33.9-62.1-2.1L291.7 89.8l-11.3 11.3-22.6 22.6L58.6 322.9c-10.4 10.4-18 23.3-22.2 37.4L1 480.7c-2.5 8.4-.2 17.5 6.1 23.7s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L387.7 253.7 410.3 231zM160 399.4l-9.1 22.7c-4 3.1-8.5 5.4-13.3 6.9L59.4 452l23-78.1c1.4-4.9 3.8-9.4 6.9-13.3l22.7-9.1v32c0 8.8 7.2 16 16 16h32zM362.7 18.7L348.3 33.2 325.7 55.8 314.3 67.1l33.9 33.9 62.1 62.1 33.9 33.9 11.3-11.3 22.6-22.6 14.5-14.5c25-25 25-65.5 0-90.5L453.3 18.7c-25-25-65.5-25-90.5 0zm-47.4 168l-144 144c-6.2 6.2-16.4 6.2-22.6 0s-6.2-16.4 0-22.6l144-144c6.2-6.2 16.4-6.2 22.6 0s6.2 16.4 0 22.6z"
            fill={colors.white}
          />
        </AnimatedSvg>
      </AnimatedTouchableOpacity>
    </View>
  );
}

// Styles are now created inside the component using useThemedStyles
