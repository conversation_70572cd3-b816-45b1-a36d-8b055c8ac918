import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withSequence,
  withDelay,
  runOnJS,
  Easing,
} from 'react-native-reanimated';
import { useSettingsStore } from '../stores/SettingsStore';

interface Particle {
  id: number;
  x: number;
  y: number;
  emoji: string;
  scale: Animated.SharedValue<number>;
  opacity: Animated.SharedValue<number>;
  translateX: Animated.SharedValue<number>;
  translateY: Animated.SharedValue<number>;
  rotation: Animated.SharedValue<number>;
}

interface Props {
  trigger: boolean;
  type: 'correct' | 'celebration' | 'milestone';
  onComplete?: () => void;
}

const { width, height } = Dimensions.get('window');

const PARTICLE_EMOJIS = {
  correct: ['✨', '⭐', '💫', '🌟'],
  celebration: ['🎉', '🎊', '✨', '🎈', '🌟', '💫', '⭐'],
  milestone: ['🏆', '👑', '💎', '🌟', '✨', '🎯', '🎖️'],
};

export default function ParticleEffects({ trigger, type, onComplete }: Props) {
  const { particleEffectsEnabled, reducedMotion } = useSettingsStore();
  const particles = useRef<Particle[]>([]);
  const animationRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    if (trigger && particleEffectsEnabled && !reducedMotion) {
      createParticles();
      startAnimation();
    }
  }, [trigger, particleEffectsEnabled, reducedMotion, onComplete, type]);

  const createParticles = () => {
    const particleCount = type === 'milestone' ? 15 : type === 'celebration' ? 12 : 8;
    const emojis = PARTICLE_EMOJIS[type];
    
    particles.current = Array.from({ length: particleCount }, (_, index) => ({
      id: index,
      x: Math.random() * width,
      y: Math.random() * height * 0.6 + height * 0.2, // Focus on middle area
      emoji: emojis[Math.floor(Math.random() * emojis.length)],
      scale: useSharedValue(0),
      opacity: useSharedValue(0),
      translateX: useSharedValue(0),
      translateY: useSharedValue(0),
      rotation: useSharedValue(0),
    }));
  };

  const startAnimation = () => {
    particles.current.forEach((particle, index) => {
      const delay = index * 50; // Stagger the animations
      
      // Initial appearance
      particle.scale.value = withDelay(
        delay,
        withSpring(1, { damping: 15, stiffness: 300 })
      );
      particle.opacity.value = withDelay(delay, withTiming(1, { duration: 200 }));
      
      // Movement animation
      const moveDistance = 100 + Math.random() * 100;
      const angle = Math.random() * Math.PI * 2;
      
      particle.translateX.value = withDelay(
        delay,
        withTiming(Math.cos(angle) * moveDistance, {
          duration: 2000,
          easing: Easing.out(Easing.quad),
        })
      );
      
      particle.translateY.value = withDelay(
        delay,
        withTiming(Math.sin(angle) * moveDistance - 50, {
          duration: 2000,
          easing: Easing.out(Easing.quad),
        })
      );
      
      // Rotation animation
      particle.rotation.value = withDelay(
        delay,
        withTiming(Math.random() * 360, {
          duration: 2000,
          easing: Easing.out(Easing.quad),
        })
      );
      
      // Fade out
      particle.opacity.value = withDelay(
        delay + 1500,
        withTiming(0, { duration: 500 }, () => {
          if (index === particles.current.length - 1 && onComplete) {
            runOnJS(onComplete)();
          }
        })
      );
    });

    // Cleanup after animation
    animationRef.current = setTimeout(() => {
      particles.current = [];
    }, 3000);
  };

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    };
  }, []);

  if (!particleEffectsEnabled || reducedMotion || !trigger) {
    return null;
  }

  return (
    <View style={styles.container} pointerEvents="none">
      {particles.current.map((particle) => (
        <AnimatedParticle key={particle.id} particle={particle} />
      ))}
    </View>
  );
}

interface AnimatedParticleProps {
  particle: Particle;
}

function AnimatedParticle({ particle }: AnimatedParticleProps) {
  const animatedStyle = useAnimatedStyle(() => ({
    position: 'absolute',
    left: particle.x,
    top: particle.y,
    transform: [
      { translateX: particle.translateX.value },
      { translateY: particle.translateY.value },
      { scale: particle.scale.value },
      { rotate: `${particle.rotation.value}deg` },
    ],
    opacity: particle.opacity.value,
  }));

  return (
    <Animated.Text style={[styles.particle, animatedStyle]}>
      {particle.emoji}
    </Animated.Text>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  particle: {
    fontSize: 24,
    textAlign: 'center',
  },
});
