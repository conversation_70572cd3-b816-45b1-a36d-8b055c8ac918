import React from 'react';
import { View, Text, StyleSheet, ScrollView, Switch, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { useSettingsStore } from '../stores/SettingsStore';

interface Props {
  onClose: () => void;
}

export default function SettingsScreen({ onClose }: Props) {
  const {
    audioEnabled,
    audioVolume,
    hapticEnabled,
    animationsEnabled,
    reducedMotion,
    particleEffectsEnabled,
    streakCounterEnabled,
    celebrationAnimationsEnabled,
    setAudioEnabled,
    setAudioVolume,
    setHapticEnabled,
    setAnimationsEnabled,
    setReducedMotion,
    setParticleEffectsEnabled,
    setStreakCounterEnabled,
    setCelebrationAnimationsEnabled,
    resetToDefaults,
    applyAccessibilitySettings,
  } = useSettingsStore();

  const SettingRow = ({ 
    title, 
    description, 
    value, 
    onValueChange, 
    icon 
  }: {
    title: string;
    description?: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
    icon: string;
  }) => (
    <View style={styles.settingRow}>
      <View style={styles.settingInfo}>
        <View style={styles.settingHeader}>
          <Ionicons name={icon as any} size={20} color="#6366F1" style={styles.settingIcon} />
          <Text style={styles.settingTitle}>{title}</Text>
        </View>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#E5E7EB', true: '#6366F1' }}
        thumbColor={value ? '#FFFFFF' : '#9CA3AF'}
      />
    </View>
  );

  const SliderRow = ({
    title,
    description,
    value,
    onValueChange,
    icon,
    minimumValue = 0,
    maximumValue = 1,
    step = 0.1,
  }: {
    title: string;
    description?: string;
    value: number;
    onValueChange: (value: number) => void;
    icon: string;
    minimumValue?: number;
    maximumValue?: number;
    step?: number;
  }) => (
    <View style={styles.settingRow}>
      <View style={styles.settingInfo}>
        <View style={styles.settingHeader}>
          <Ionicons name={icon as any} size={20} color="#6366F1" style={styles.settingIcon} />
          <Text style={styles.settingTitle}>{title}</Text>
        </View>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
        <View style={styles.sliderContainer}>
          <Slider
            style={styles.slider}
            minimumValue={minimumValue}
            maximumValue={maximumValue}
            value={value}
            onValueChange={onValueChange}
            step={step}
            minimumTrackTintColor="#6366F1"
            maximumTrackTintColor="#E5E7EB"
            thumbStyle={styles.sliderThumb}
          />
          <Text style={styles.sliderValue}>{Math.round(value * 100)}%</Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Feedback Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Audio Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Audio Feedback</Text>
          
          <SettingRow
            title="Sound Effects"
            description="Play sounds for correct/incorrect answers and interactions"
            value={audioEnabled}
            onValueChange={setAudioEnabled}
            icon="volume-high"
          />

          {audioEnabled && (
            <SliderRow
              title="Volume Level"
              description="Adjust the volume of sound effects"
              value={audioVolume}
              onValueChange={setAudioVolume}
              icon="volume-medium"
            />
          )}
        </View>

        {/* Haptic Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Haptic Feedback</Text>
          
          <SettingRow
            title="Vibration"
            description="Feel vibrations for interactions and feedback"
            value={hapticEnabled}
            onValueChange={setHapticEnabled}
            icon="phone-portrait"
          />
        </View>

        {/* Animation Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Visual Effects</Text>
          
          <SettingRow
            title="Animations"
            description="Enable smooth transitions and micro-interactions"
            value={animationsEnabled}
            onValueChange={setAnimationsEnabled}
            icon="play"
          />

          <SettingRow
            title="Particle Effects"
            description="Show celebratory particles for correct answers"
            value={particleEffectsEnabled}
            onValueChange={setParticleEffectsEnabled}
            icon="sparkles"
          />

          <SettingRow
            title="Celebration Animations"
            description="Enhanced animations for achievements and milestones"
            value={celebrationAnimationsEnabled}
            onValueChange={setCelebrationAnimationsEnabled}
            icon="trophy"
          />
        </View>

        {/* Engagement Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Engagement Features</Text>
          
          <SettingRow
            title="Streak Counter"
            description="Track consecutive correct answers within lessons"
            value={streakCounterEnabled}
            onValueChange={setStreakCounterEnabled}
            icon="flame"
          />
        </View>

        {/* Accessibility Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Accessibility</Text>
          
          <SettingRow
            title="Reduced Motion"
            description="Minimize animations for motion sensitivity"
            value={reducedMotion}
            onValueChange={setReducedMotion}
            icon="accessibility"
          />
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <TouchableOpacity style={styles.actionButton} onPress={applyAccessibilitySettings}>
            <Ionicons name="accessibility" size={20} color="#6366F1" />
            <Text style={styles.actionButtonText}>Apply Accessibility Settings</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={resetToDefaults}>
            <Ionicons name="refresh" size={20} color="#6366F1" />
            <Text style={styles.actionButtonText}>Reset to Defaults</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginTop: 16,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  settingIcon: {
    marginRight: 12,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 32,
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 32,
    marginTop: 8,
  },
  slider: {
    flex: 1,
    height: 40,
  },
  sliderThumb: {
    backgroundColor: '#6366F1',
  },
  sliderValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6366F1',
    marginLeft: 12,
    minWidth: 40,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6366F1',
    marginLeft: 12,
  },
});
