import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { MultiChoiceExercise } from "@/types/skill";
import { AnswerFeedback } from "../store";
import { useTheme, useThemedStyles } from '~/lib/theme';
import { getThemedShadow } from '~/lib/themeUtils';

interface Props {
  exercise: MultiChoiceExercise;
  onAnswer: (indices: number[]) => void;
  currentAnswer?: number[];
  feedback?: AnswerFeedback;
}

export default function MultiChoice({ exercise, onAnswer, currentAnswer = [], feedback }: Props) {
  const { prompt, choices, answerIndices } = exercise.payload;
  const [selected, setSelected] = useState<number[]>(currentAnswer);
  const { colors } = useTheme();

  const toggle = (idx: number) => {
    setSelected(prev => {
      const exists = prev.includes(idx);
      const updated = exists ? prev.filter(i => i !== idx) : [...prev, idx];
      onAnswer(updated);
      return updated;
    });
  };

  // Create theme-aware styles
  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    prompt: {
      fontSize: 28,
      fontWeight: "bold",
      marginBottom: 24,
      color: theme.text,
    },
    optionContainer: {
      backgroundColor: theme.card,
      borderWidth: 2,
      borderColor: theme.border,
      borderRadius: 16,
      padding: 20,
      marginVertical: 8,
      ...getThemedShadow(theme, 'sm'),
    },
    selectedOptionContainer: {
      borderColor: theme.primaryButton,
      backgroundColor: isDark ? colors.primary[900] : colors.primary[50],
    },
    correctOptionContainer: {
      borderColor: colors.success[500],
      backgroundColor: colors.success[50],
    },
    incorrectOptionContainer: {
      borderColor: colors.error[500],
      backgroundColor: colors.error[50],
    },
    optionText: {
      fontSize: 18,
      color: theme.text,
      fontWeight: '600',
    },
  }));

  const getOptionStyle = (idx: number) => {
    const isSelected = selected.includes(idx);
    const isCorrectOption = answerIndices.includes(idx);
    const showFeedback = feedback?.showFeedback;

    if (showFeedback && isSelected) {
      if (feedback.isCorrect && isCorrectOption) {
        return [styles.optionContainer, styles.correctOptionContainer];
      } else if (!feedback.isCorrect) {
        return [styles.optionContainer, styles.incorrectOptionContainer];
      }
    }

    return [
      styles.optionContainer,
      isSelected && styles.selectedOptionContainer,
    ];
  };

  return (
    <View>
      <Text style={styles.prompt}>{prompt}</Text>
      <View>
        {choices.map((choice, idx) => (
          <TouchableOpacity
            key={idx}
            activeOpacity={0.8}
            onPress={() => toggle(idx)}
            style={getOptionStyle(idx)}
          >
            <Text style={styles.optionText}>{choice}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

// Styles are now created inside the component using useThemedStyles
