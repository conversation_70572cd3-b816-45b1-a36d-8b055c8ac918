import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { useUser } from '@clerk/clerk-expo';
import { useTheme } from '@/lib/theme';
import { useFreemium } from '@/features/freemium/FreemiumProvider';
import { useUpgradePrompt } from '@/features/freemium/hooks/useUpgradePrompt';
import { Subject, CompletionStats } from '@/types/skill';
import { hierarchyService } from '../HierarchyService';
import ProgressBar from '@/features/skillPlayer/components/ProgressBar';

const { width } = Dimensions.get('window');

interface SubjectCardProps {
  subject: Subject;
  progress: number;
  index: number;
  onPress: () => void;
  isLocked: boolean;
}

const SubjectCard: React.FC<SubjectCardProps> = ({
  subject,
  progress,
  index,
  onPress,
  isLocked,
}) => {
  const { theme, colors } = useTheme();
  const scale = useSharedValue(1);
  const elevation = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      shadowOpacity: interpolate(elevation.value, [1, 1.5], [0.1, 0.25]),
      shadowRadius: interpolate(elevation.value, [1, 1.5], [4, 8]),
      elevation: interpolate(elevation.value, [1, 1.5], [2, 6]),
    };
  });

  const handlePressIn = () => {
    if (!isLocked) {
      scale.value = withSpring(0.97, { damping: 15, stiffness: 150 });
      elevation.value = withTiming(1.5, { duration: 150 });
    }
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 150 });
    elevation.value = withTiming(1, { duration: 150 });
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'beginner':
        return colors.success[500];
      case 'intermediate':
        return colors.warning[500];
      case 'advanced':
        return colors.error[500];
      default:
        return theme.secondaryText;
    }
  };

  const getDifficultyIcon = (difficulty?: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'leaf-outline';
      case 'intermediate':
        return 'flash-outline';
      case 'advanced':
        return 'rocket-outline';
      default:
        return 'help-circle-outline';
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={isLocked}
      activeOpacity={0.9}
    >
      <Animated.View
        style={[
          styles.subjectCard,
          {
            backgroundColor: theme.card,
            borderColor: theme.border,
            shadowColor: theme.shadow,
          },
          animatedStyle,
          isLocked && { opacity: 0.6 },
        ]}
      >
        {/* Subject Icon and Premium Badge */}
        <View style={styles.cardHeader}>
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: subject.color || colors.primary[500] },
            ]}
          >
            <Text style={styles.subjectIcon}>{subject.icon || '📚'}</Text>
          </View>
          {subject.isPremium && (
            <View style={[styles.premiumBadge, { backgroundColor: colors.warning[500] }]}>
              <Ionicons name="star" size={12} color={colors.white} />
              <Text style={[styles.premiumText, { color: colors.white }]}>Premium</Text>
            </View>
          )}
          {isLocked && (
            <View style={styles.lockIcon}>
              <Ionicons name="lock-closed" size={16} color={theme.secondaryText} />
            </View>
          )}
        </View>

        {/* Subject Info */}
        <View style={styles.cardContent}>
          <Text style={[styles.subjectTitle, { color: theme.text }]} numberOfLines={2}>
            {subject.name}
          </Text>
          <Text style={[styles.subjectDescription, { color: theme.secondaryText }]} numberOfLines={3}>
            {subject.description}
          </Text>

          {/* Difficulty and Duration */}
          <View style={styles.metaInfo}>
            <View style={styles.difficultyContainer}>
              <Ionicons
                name={getDifficultyIcon(subject.difficulty) as any}
                size={14}
                color={getDifficultyColor(subject.difficulty)}
              />
              <Text
                style={[
                  styles.difficultyText,
                  { color: getDifficultyColor(subject.difficulty) },
                ]}
              >
                {subject.difficulty || 'Beginner'}
              </Text>
            </View>
            {subject.estimatedDuration && (
              <View style={styles.durationContainer}>
                <Ionicons name="time-outline" size={14} color={theme.secondaryText} />
                <Text style={[styles.durationText, { color: theme.secondaryText }]}>
                  {subject.estimatedDuration}m
                </Text>
              </View>
            )}
          </View>

          {/* Progress Bar */}
          <View style={styles.progressContainer}>
            <ProgressBar progress={progress / 100} animated={true} />
            <Text style={[styles.progressText, { color: theme.secondaryText }]}>
              {progress}% complete
            </Text>
          </View>
        </View>

        {/* Continue Arrow */}
        {!isLocked && (
          <View style={styles.arrowContainer}>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={subject.color || colors.primary[500]}
            />
          </View>
        )}
      </Animated.View>
    </TouchableOpacity>
  );
};

interface SubjectListScreenProps {
  onSubjectSelect: (subjectId: string) => void;
}

export default function SubjectListScreen({ onSubjectSelect }: SubjectListScreenProps) {
  const { user } = useUser();
  const { theme, colors } = useTheme();
  const { isFreeTier } = useFreemium();
  const { promptForFeature } = useUpgradePrompt();

  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [subjectProgresses, setSubjectProgresses] = useState<Record<string, number>>({});
  const [completionStats, setCompletionStats] = useState<CompletionStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSubjects();
  }, [user]);

  const loadSubjects = async () => {
    try {
      setLoading(true);
      const allSubjects = await hierarchyService.getAllSubjects();
      setSubjects(allSubjects);

      // Load progress for each subject
      const progresses: Record<string, number> = {};
      for (const subject of allSubjects) {
        const progress = await hierarchyService.getSubjectProgress(subject.id, user?.id);
        progresses[subject.id] = progress ? calculateProgressPercentage(progress) : 0;
      }
      setSubjectProgresses(progresses);

      // Load overall completion stats
      const stats = await hierarchyService.calculateCompletionStats(user?.id);
      setCompletionStats(stats);
    } catch (error) {
      console.error('Error loading subjects:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateProgressPercentage = (progress: any): number => {
    if (!progress || !progress.topicProgresses) return 0;
    
    const topicIds = Object.keys(progress.topicProgresses);
    if (topicIds.length === 0) return 0;
    
    const completedTopics = topicIds.filter(
      id => progress.topicProgresses[id]?.isCompleted
    ).length;
    
    return Math.round((completedTopics / topicIds.length) * 100);
  };

  const handleSubjectPress = (subject: Subject) => {
    if (subject.isPremium && isFreeTier) {
      promptForFeature('Premium Subject Access');
      return;
    }
    onSubjectSelect(subject.id);
  };

  const isSubjectLocked = (subject: Subject): boolean => {
    return subject.isPremium && isFreeTier;
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.text }]}>Loading subjects...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.text }]}>Choose a Subject</Text>
        <Text style={[styles.headerSubtitle, { color: theme.secondaryText }]}>
          Start your learning journey
        </Text>
        
        {/* Overall Progress */}
        {completionStats && (
          <View style={[styles.overallProgressCard, { backgroundColor: theme.card, borderColor: theme.border }]}>
            <Text style={[styles.overallProgressTitle, { color: theme.text }]}>
              Your Progress
            </Text>
            <Text style={[styles.overallProgressStats, { color: theme.secondaryText }]}>
              {completionStats.completedSkills} of {completionStats.totalSkills} skills completed
            </Text>
            <ProgressBar progress={completionStats.overallProgress / 100} animated={true} />
          </View>
        )}
      </View>

      {/* Subject List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {subjects.map((subject, index) => (
          <SubjectCard
            key={subject.id}
            subject={subject}
            progress={subjectProgresses[subject.id] || 0}
            index={index}
            onPress={() => handleSubjectPress(subject)}
            isLocked={isSubjectLocked(subject)}
          />
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    marginBottom: 20,
  },
  overallProgressCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  overallProgressTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  overallProgressStats: {
    fontSize: 14,
    marginBottom: 12,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  subjectCard: {
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 16,
    padding: 20,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  subjectIcon: {
    fontSize: 24,
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 'auto',
  },
  premiumText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  lockIcon: {
    marginLeft: 'auto',
  },
  cardContent: {
    flex: 1,
  },
  subjectTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    lineHeight: 24,
  },
  subjectDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  metaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  difficultyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  durationText: {
    fontSize: 12,
    marginLeft: 4,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressText: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'right',
  },
  arrowContainer: {
    position: 'absolute',
    right: 20,
    top: '50%',
    transform: [{ translateY: -10 }],
  },
});
