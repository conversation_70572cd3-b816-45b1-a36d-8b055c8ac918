import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { hierarchyDataService } from '../services/HierarchyDataService';
import { hierarchyService } from '../HierarchyService';
import { Subject, Topic } from '@/types/skill';
import { SkillRecord } from '../services/HierarchyDataService';

export default function HierarchyTest() {
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [skills, setSkills] = useState<SkillRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadSubjects();
  }, []);

  const loadSubjects = async () => {
    try {
      setLoading(true);
      setError(null);
      const subjectsData = await hierarchyDataService.getSubjects();
      setSubjects(subjectsData);
      console.log('[HierarchyTest] Loaded subjects:', subjectsData.length);
    } catch (err) {
      setError('Failed to load subjects');
      console.error('[HierarchyTest] Error loading subjects:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadTopics = async (subjectId: string) => {
    try {
      setLoading(true);
      setError(null);
      const topicsData = await hierarchyDataService.getTopicsBySubjectId(subjectId);
      setTopics(topicsData);
      setSelectedTopic(null);
      setSkills([]);
      console.log('[HierarchyTest] Loaded topics:', topicsData.length);
    } catch (err) {
      setError('Failed to load topics');
      console.error('[HierarchyTest] Error loading topics:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadSkills = async (topicId: string) => {
    try {
      setLoading(true);
      setError(null);
      const skillsData = await hierarchyDataService.getSkillsByTopicId(topicId);
      setSkills(skillsData);
      console.log('[HierarchyTest] Loaded skills:', skillsData.length);
    } catch (err) {
      setError('Failed to load skills');
      console.error('[HierarchyTest] Error loading skills:', err);
    } finally {
      setLoading(false);
    }
  };

  const testSkillContent = async (skillId: string) => {
    try {
      setLoading(true);
      setError(null);
      const skillContent = await hierarchyDataService.getSkillContent(skillId);
      if (skillContent) {
        console.log('[HierarchyTest] Loaded skill content:', skillContent.name);
        console.log('[HierarchyTest] Skill levels:', skillContent.levels?.length || 0);
        console.log('[HierarchyTest] First level lessons:', skillContent.levels?.[0]?.lessons?.length || 0);
      } else {
        console.log('[HierarchyTest] No skill content found');
      }
    } catch (err) {
      setError('Failed to load skill content');
      console.error('[HierarchyTest] Error loading skill content:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubjectSelect = (subject: Subject) => {
    setSelectedSubject(subject);
    loadTopics(subject.id);
  };

  const handleTopicSelect = (topic: Topic) => {
    setSelectedTopic(topic);
    loadSkills(topic.id);
  };

  const handleSkillSelect = (skill: SkillRecord) => {
    testSkillContent(skill.id);
  };

  const goBack = () => {
    if (selectedTopic) {
      setSelectedTopic(null);
      setSkills([]);
    } else if (selectedSubject) {
      setSelectedSubject(null);
      setTopics([]);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Hierarchy Test</Text>
      
      {loading && <Text style={styles.loading}>Loading...</Text>}
      {error && <Text style={styles.error}>{error}</Text>}

      {/* Navigation breadcrumb */}
      <View style={styles.breadcrumb}>
        <TouchableOpacity onPress={() => {
          setSelectedSubject(null);
          setSelectedTopic(null);
          setTopics([]);
          setSkills([]);
        }}>
          <Text style={styles.breadcrumbItem}>Subjects</Text>
        </TouchableOpacity>
        {selectedSubject && (
          <>
            <Text style={styles.breadcrumbSeparator}> > </Text>
            <TouchableOpacity onPress={() => {
              setSelectedTopic(null);
              setSkills([]);
            }}>
              <Text style={styles.breadcrumbItem}>{selectedSubject.name}</Text>
            </TouchableOpacity>
          </>
        )}
        {selectedTopic && (
          <>
            <Text style={styles.breadcrumbSeparator}> > </Text>
            <Text style={styles.breadcrumbItem}>{selectedTopic.name}</Text>
          </>
        )}
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Show subjects */}
        {!selectedSubject && (
          <View>
            <Text style={styles.sectionTitle}>Subjects ({subjects.length})</Text>
            {subjects.map((subject) => (
              <TouchableOpacity
                key={subject.id}
                style={styles.item}
                onPress={() => handleSubjectSelect(subject)}
              >
                <Text style={styles.itemIcon}>{subject.icon}</Text>
                <View style={styles.itemContent}>
                  <Text style={styles.itemTitle}>{subject.name}</Text>
                  <Text style={styles.itemDescription}>{subject.description}</Text>
                  <Text style={styles.itemMeta}>
                    {subject.difficulty} • {subject.estimatedDuration}m
                    {subject.isPremium && ' • Premium'}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Show topics */}
        {selectedSubject && !selectedTopic && (
          <View>
            <Text style={styles.sectionTitle}>Topics in {selectedSubject.name} ({topics.length})</Text>
            {topics.map((topic) => (
              <TouchableOpacity
                key={topic.id}
                style={styles.item}
                onPress={() => handleTopicSelect(topic)}
              >
                <Text style={styles.itemIcon}>📖</Text>
                <View style={styles.itemContent}>
                  <Text style={styles.itemTitle}>{topic.name}</Text>
                  <Text style={styles.itemDescription}>{topic.description}</Text>
                  <Text style={styles.itemMeta}>
                    {topic.difficulty} • {topic.estimatedDuration}m
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Show skills */}
        {selectedTopic && (
          <View>
            <Text style={styles.sectionTitle}>Skills in {selectedTopic.name} ({skills.length})</Text>
            {skills.map((skill) => (
              <TouchableOpacity
                key={skill.id}
                style={styles.item}
                onPress={() => handleSkillSelect(skill)}
              >
                <Text style={styles.itemIcon}>🎯</Text>
                <View style={styles.itemContent}>
                  <Text style={styles.itemTitle}>{skill.name}</Text>
                  <Text style={styles.itemDescription}>{skill.description}</Text>
                  <Text style={styles.itemMeta}>
                    {skill.difficulty} • {skill.estimatedDuration}m • File: {skill.fileName}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  loading: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
  },
  error: {
    textAlign: 'center',
    fontSize: 16,
    color: '#ff0000',
    marginBottom: 10,
  },
  breadcrumb: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  breadcrumbItem: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  breadcrumbSeparator: {
    fontSize: 16,
    color: '#666',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  scrollView: {
    flex: 1,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  itemMeta: {
    fontSize: 12,
    color: '#999',
  },
});
