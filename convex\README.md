# Convex Database Schema for Educational App

## Overview

This directory contains a comprehensive Convex database schema designed for our three-tier educational hierarchy system. The schema supports subjects, topics, skills, user progress tracking, gamification, and analytics.

## Schema Architecture

### Core Tables

#### **Educational Content Hierarchy**
```
subjects → topics → skills → skillContents
    ↓        ↓        ↓
subjectProgress → topicProgress → skillProgress
```

#### **User Management & Gamification**
```
users → userStreaks → userAchievements → certificates
     → learningSessions → userActivities
```

### Table Relationships

1. **subjects** (Primary content categories)
   - Referenced by: `topics.subjectId`, `subjectProgress.subjectId`

2. **topics** (Content subcategories within subjects)
   - References: `subjects.subjectId`
   - Referenced by: `skills.topicId`, `topicProgress.topicId`

3. **skills** (Individual learning units)
   - References: `topics.topicId`
   - Referenced by: `skillProgress.skillId`, `skillContents.skillId`

4. **skillContents** (Versioned skill content)
   - References: `skills.skillId`

## File Structure

```
convex/
├── schema.ts           # Complete database schema definition
├── subjects.ts         # Subject CRUD operations and queries
├── topics.ts          # Topic CRUD operations and queries
├── skills.ts          # Skill CRUD operations and queries
├── progress.ts        # Progress tracking and calculations
├── migration.ts       # Data seeding and migration utilities
└── README.md          # This documentation
```

## Key Features

### 1. **Normalized Database Design**
- Foreign key relationships using Convex document references
- No data duplication across tables
- Efficient querying with proper indexes

### 2. **Comprehensive Indexing**
- Performance-optimized indexes for all common query patterns
- User-specific indexes for progress tracking
- Search-friendly indexes for content discovery

### 3. **Progress Tracking**
- Hierarchical progress calculation (skill → topic → subject)
- Automatic progress updates with triggers
- Comprehensive analytics and statistics

### 4. **Content Versioning**
- Skill content versioning system
- Active/inactive content management
- Rollback capabilities

### 5. **Gamification Support**
- User streaks with freeze mechanics
- Achievement system with flexible criteria
- Certificate generation for completed subjects

## Getting Started

### 1. **Initialize Convex**
```bash
npx convex dev
```

### 2. **Seed Development Data**
```typescript
// In Convex dashboard or via mutation
await convex.mutation('migration:seedDevelopmentData');
```

### 3. **Check Data**
```typescript
// Verify data was seeded correctly
const stats = await convex.query('migration:checkDataExists');
console.log(stats); // { subjects: 4, topics: 5, skills: 3, hasData: true }
```

## Usage Examples

### **Basic Queries**

```typescript
// Get all subjects
const subjects = await convex.query('subjects:getAllSubjects');

// Get topics for a subject
const topics = await convex.query('topics:getTopicsBySubjectId', {
  subjectId: 'subject-ai-fundamentals'
});

// Get skills for a topic
const skills = await convex.query('skills:getSkillsByTopicId', {
  topicId: 'topic-ai-basics'
});

// Get skill content
const skillContent = await convex.query('skills:getSkillContent', {
  skillId: 'skill-better-001'
});
```

### **Progress Tracking**

```typescript
// Update skill progress
await convex.mutation('progress:updateSkillProgress', {
  userId: 'user-123',
  skillId: 'skill-better-001',
  lessonIndex: 2,
  exerciseIndex: 5,
  completedExercises: ['ex-1', 'ex-2', 'ex-3'],
  score: 85,
  timeSpent: 1200, // seconds
  isCompleted: false
});

// Get user's overall statistics
const stats = await convex.query('progress:getUserOverallStats', {
  userId: 'user-123'
});
```

### **Search and Filtering**

```typescript
// Search skills
const searchResults = await convex.query('skills:searchSkills', {
  query: 'productivity focus'
});

// Get skills by tag
const focusSkills = await convex.query('skills:getSkillsByTag', {
  tag: 'focus'
});

// Get premium subjects
const premiumSubjects = await convex.query('subjects:getPremiumSubjects');
```

## Data Migration

### **From Development Data**

The migration system can seed the database with development data:

```typescript
// Clear all existing data (CAUTION!)
await convex.mutation('migration:clearAllData');

// Seed with development data
const result = await convex.mutation('migration:seedDevelopmentData');
console.log(result); // { subjects: [...], topics: [...], skills: [...] }
```

### **Backup and Restore**

```typescript
// Export all data
const backup = await convex.query('migration:exportAllData');

// Import data from backup
await convex.mutation('migration:importData', { data: backup });
```

## Integration with React App

### **Development Mode**
- Uses local dummy data from `src/fixtures/devHierarchyData.ts`
- No Convex connection required
- Seamless offline development

### **Production Mode**
- Connects to Convex cloud database
- Automatic fallback to development data if Convex unavailable
- Real-time updates and synchronization

### **Setup Integration**

```typescript
// In your app's root component
import { ConvexProvider, ConvexReactClient } from 'convex/react';
import { createHierarchyDataService } from '@/features/hierarchy/services/HierarchyDataService';

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Create hierarchy service with Convex client
const hierarchyService = createHierarchyDataService(convex);
```

## Best Practices

1. **Always use indexes** for query performance
2. **Batch operations** for multiple updates
3. **Cache results** in the client when appropriate
4. **Use soft deletes** (isActive: false) instead of hard deletes
5. **Version content** for rollback capabilities
6. **Monitor query performance** in Convex dashboard
