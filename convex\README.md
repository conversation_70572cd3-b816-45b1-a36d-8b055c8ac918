# Convex Database Schema for Educational App - Single Table Design

## Overview

This directory contains a **cost-optimized, single-table Convex database schema** designed for our three-tier educational hierarchy system. The schema uses embedded/nested data structures to minimize database calls and reduce costs while maintaining all functionality.

## Schema Architecture - Single Table Design

### **Cost Optimization Strategy**

Instead of normalized tables requiring multiple database calls:
```
❌ OLD: subjects → topics → skills (3+ database calls)
✅ NEW: subjects (with embedded topics and skills) (1 database call)
```

### Core Table Structure

#### **Single Educational Content Table**
```
subjects (contains embedded topics and skills)
    ↓
subjectProgress → topicProgress → skillProgress
```

#### **User Management & Gamification**
```
users → userStreaks → userAchievements → certificates
     → learningSessions → userActivities
```

### Embedded Data Structure

1. **subjects** (Contains all hierarchy data)
   - Embedded: `topics[]` array containing topic data
   - Each topic contains: `skills[]` array containing skill data
   - Referenced by: `subjectProgress.subjectId`, `skillProgress.subjectId`

2. **Progress Tracking** (References embedded content via UUIDs)
   - `skillProgress` references: `subjectId` (document ID) + `skillId` (UUID) + `topicId` (UUID)
   - `topicProgress` references: `subjectId` (document ID) + `topicId` (UUID)
   - `subjectProgress` references: `subjectId` (document ID)

## File Structure

```
convex/
├── schema.ts           # Single table schema with embedded structures
├── subjects.ts         # All content operations (subjects, topics, skills)
├── progress.ts        # Progress tracking with UUID references
├── migration.ts       # Data seeding and migration utilities
└── README.md          # This documentation
```

## Key Features

### 1. **Cost-Optimized Single Table Design**
- All educational content in one `subjects` table
- Embedded topics and skills arrays eliminate JOIN operations
- Reduces database calls from 3+ to 1 per subject
- Significant cost savings for high-traffic applications

### 2. **Embedded Data Structure**
- Topics embedded as arrays within subjects
- Skills embedded as arrays within topics
- UUID-based references for progress tracking
- Maintains referential integrity without foreign keys

### 3. **Comprehensive Search**
- Search across all embedded content in single queries
- Full-text search through subjects, topics, and skills
- Tag-based filtering across the entire hierarchy
- No expensive JOIN operations required

### 4. **Progress Tracking**
- Hierarchical progress calculation (skill → topic → subject)
- UUID-based references to embedded content
- Automatic progress updates with triggers
- Comprehensive analytics and statistics

### 5. **Performance Benefits**
- Single database call fetches entire subject hierarchy
- Reduced network round trips
- Faster query execution
- Better caching efficiency

### 6. **Gamification Support**
- User streaks with freeze mechanics
- Achievement system with flexible criteria
- Certificate generation for completed subjects

## Getting Started

### 1. **Initialize Convex**
```bash
npx convex dev
```

### 2. **Seed Development Data**
```typescript
// In Convex dashboard or via mutation
await convex.mutation('migration:seedDevelopmentData');
```

### 3. **Check Data**
```typescript
// Verify data was seeded correctly
const stats = await convex.query('migration:checkDataExists');
console.log(stats); // { subjects: 4, topics: 5, skills: 3, hasData: true }
```

## Usage Examples

### **Basic Queries (Single Table)**

```typescript
// Get all subjects (with embedded topics and skills)
const subjects = await convex.query('subjects:getAllSubjects');
// Returns: [{ _id, name, topics: [{ id, name, skills: [...] }] }]

// Get topics for a subject (extracted from embedded data)
const topics = await convex.query('subjects:getTopicsBySubjectId', {
  subjectId: 'subject-doc-id' // Convex document ID
});

// Get skills for a topic (extracted from embedded data)
const skills = await convex.query('subjects:getSkillsByTopicId', {
  subjectId: 'subject-doc-id', // Convex document ID
  topicId: 'topic-ai-basics'   // UUID within subject
});

// Find skill across all subjects
const skillData = await convex.query('subjects:findSkillByUUID', {
  skillId: 'skill-better-001'
});
// Returns: { skill, topic, subject, subjectId, topicId }
```

### **Progress Tracking (UUID-Based)**

```typescript
// Update skill progress (requires subject and topic context)
await convex.mutation('progress:updateSkillProgress', {
  userId: 'user-123',
  skillId: 'skill-better-001',      // Skill UUID
  subjectId: 'subject-doc-id',      // Convex document ID
  topicId: 'topic-ai-basics',       // Topic UUID
  lessonIndex: 2,
  exerciseIndex: 5,
  completedExercises: ['ex-1', 'ex-2', 'ex-3'],
  score: 85,
  timeSpent: 1200, // seconds
  isCompleted: false
});

// Update progress hierarchy automatically
await convex.mutation('progress:updateProgressHierarchy', {
  userId: 'user-123',
  skillId: 'skill-better-001',
  subjectId: 'subject-doc-id',
  topicId: 'topic-ai-basics'
});

// Get user's overall statistics
const stats = await convex.query('progress:getUserOverallStats', {
  userId: 'user-123'
});
```

### **Search and Filtering (Embedded Data)**

```typescript
// Search skills across all subjects (single query)
const searchResults = await convex.query('subjects:searchSkills', {
  query: 'productivity focus'
});
// Returns: [{ ...skill, subjectId, subjectName, topicId, topicName }]

// Search topics across all subjects
const topicResults = await convex.query('subjects:searchTopics', {
  query: 'AI tools'
});

// Get skills by tag across all subjects
const focusSkills = await convex.query('subjects:getSkillsByTag', {
  tag: 'focus'
});

// Get premium subjects
const premiumSubjects = await convex.query('subjects:getPremiumSubjects');
```

## Data Migration

### **From Development Data**

The migration system can seed the database with development data:

```typescript
// Clear all existing data (CAUTION!)
await convex.mutation('migration:clearAllData');

// Seed with development data
const result = await convex.mutation('migration:seedDevelopmentData');
console.log(result); // { subjects: [...], topics: [...], skills: [...] }
```

### **Backup and Restore**

```typescript
// Export all data
const backup = await convex.query('migration:exportAllData');

// Import data from backup
await convex.mutation('migration:importData', { data: backup });
```

## Integration with React App

### **Development Mode**
- Uses local dummy data from `src/fixtures/devHierarchyData.ts`
- No Convex connection required
- Seamless offline development

### **Production Mode**
- Connects to Convex cloud database
- Automatic fallback to development data if Convex unavailable
- Real-time updates and synchronization

### **Setup Integration**

```typescript
// In your app's root component
import { ConvexProvider, ConvexReactClient } from 'convex/react';
import { createHierarchyDataService } from '@/features/hierarchy/services/HierarchyDataService';

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Create hierarchy service with Convex client
const hierarchyService = createHierarchyDataService(convex);
```

## Best Practices

1. **Always use indexes** for query performance
2. **Batch operations** for multiple updates
3. **Cache results** in the client when appropriate
4. **Use soft deletes** (isActive: false) instead of hard deletes
5. **Version content** for rollback capabilities
6. **Monitor query performance** in Convex dashboard
