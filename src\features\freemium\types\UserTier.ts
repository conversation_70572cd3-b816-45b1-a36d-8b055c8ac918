export enum UserTier {
  FREE = 'free',
  PREMIUM = 'premium',
  PREMIUM_PLUS = 'premium_plus'
}

export interface UserSubscription {
  tier: UserTier;
  isActive: boolean;
  expiresAt?: Date;
  features: UserFeatures;
  trialEndsAt?: Date;
  isTrialActive: boolean;
}

export interface UserFeatures {
  // Course Access
  offlineCoursesOnly: boolean;
  unlimitedOnlineCourses: boolean;
  customCourseGeneration: boolean;
  
  // Progress & Sync
  localProgressOnly: boolean;
  cloudProgressSync: boolean;
  crossDeviceSync: boolean;
  progressBackup: boolean;
  
  // Learning Features
  basicEngagementFeatures: boolean;
  advancedAnalytics: boolean;
  personalizedRecommendations: boolean;
  adaptiveDifficulty: boolean;
  
  // Social & Gamification
  basicStreaks: boolean;
  leaderboards: boolean;
  achievements: boolean;
  socialFeatures: boolean;
  
  // Content & Quality
  basicExerciseTypes: boolean;
  advancedExerciseTypes: boolean;
  audioContent: boolean;
  offlineDownloads: boolean;
  
  // Support & Experience
  communitySupport: boolean;
  prioritySupport: boolean;
  adFree: boolean;
  earlyAccess: boolean;
}

export const FREE_USER_FEATURES: UserFeatures = {
  // Course Access
  offlineCoursesOnly: true,
  unlimitedOnlineCourses: false,
  customCourseGeneration: false,
  
  // Progress & Sync
  localProgressOnly: true,
  cloudProgressSync: false,
  crossDeviceSync: false,
  progressBackup: false,
  
  // Learning Features
  basicEngagementFeatures: true,
  advancedAnalytics: false,
  personalizedRecommendations: false,
  adaptiveDifficulty: false,
  
  // Social & Gamification
  basicStreaks: true,
  leaderboards: false,
  achievements: true,
  socialFeatures: false,
  
  // Content & Quality
  basicExerciseTypes: true,
  advancedExerciseTypes: false,
  audioContent: true,
  offlineDownloads: true,
  
  // Support & Experience
  communitySupport: true,
  prioritySupport: false,
  adFree: false,
  earlyAccess: false,
};

export const PREMIUM_USER_FEATURES: UserFeatures = {
  // Course Access
  offlineCoursesOnly: false,
  unlimitedOnlineCourses: true,
  customCourseGeneration: true,
  
  // Progress & Sync
  localProgressOnly: false,
  cloudProgressSync: true,
  crossDeviceSync: true,
  progressBackup: true,
  
  // Learning Features
  basicEngagementFeatures: true,
  advancedAnalytics: true,
  personalizedRecommendations: true,
  adaptiveDifficulty: true,
  
  // Social & Gamification
  basicStreaks: true,
  leaderboards: true,
  achievements: true,
  socialFeatures: true,
  
  // Content & Quality
  basicExerciseTypes: true,
  advancedExerciseTypes: true,
  audioContent: true,
  offlineDownloads: true,
  
  // Support & Experience
  communitySupport: true,
  prioritySupport: true,
  adFree: true,
  earlyAccess: true,
};

export const PREMIUM_PLUS_USER_FEATURES: UserFeatures = {
  ...PREMIUM_USER_FEATURES,
  // Additional premium plus features can be added here
};

export function getUserFeatures(tier: UserTier): UserFeatures {
  switch (tier) {
    case UserTier.FREE:
      return FREE_USER_FEATURES;
    case UserTier.PREMIUM:
      return PREMIUM_USER_FEATURES;
    case UserTier.PREMIUM_PLUS:
      return PREMIUM_PLUS_USER_FEATURES;
    default:
      return FREE_USER_FEATURES;
  }
}

export function canAccessFeature(subscription: UserSubscription, feature: keyof UserFeatures): boolean {
  if (!subscription.isActive && subscription.tier !== UserTier.FREE) {
    return FREE_USER_FEATURES[feature];
  }
  
  return subscription.features[feature];
}

export interface FreemiumLimits {
  maxOfflineCourses: number;
  maxLessonsPerDay: number;
  maxStreakWithoutSync: number;
  maxLocalProgressDays: number;
}

export const FREE_USER_LIMITS: FreemiumLimits = {
  maxOfflineCourses: 3,
  maxLessonsPerDay: 10,
  maxStreakWithoutSync: 30,
  maxLocalProgressDays: 90,
};

export const PREMIUM_USER_LIMITS: FreemiumLimits = {
  maxOfflineCourses: -1, // Unlimited
  maxLessonsPerDay: -1, // Unlimited
  maxStreakWithoutSync: -1, // Unlimited
  maxLocalProgressDays: -1, // Unlimited
};
