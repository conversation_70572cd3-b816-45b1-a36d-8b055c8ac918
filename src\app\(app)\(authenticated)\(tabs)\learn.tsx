import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { useTheme } from '@/lib/theme';
import SubjectListScreen from '@/features/hierarchy/components/SubjectListScreen';
import TopicListScreen from '@/features/hierarchy/components/TopicListScreen';
import SkillListScreen from '@/features/hierarchy/components/SkillListScreen';
import {
  useNavigationStore,
  useNavigationActions,
  useCurrentLevel,
  useCurrentSubjectId,
  useCurrentTopicId
} from '@/features/hierarchy/stores/NavigationStore';
import { hierarchyService } from '@/features/hierarchy/HierarchyService';

export default function LearnScreen() {
  const { theme } = useTheme();
  const level = useCurrentLevel();
  const subjectId = useCurrentSubjectId();
  const topicId = useCurrentTopicId();
  const { navigateToTopics, navigateToSkills, navigateBack, reset } = useNavigationActions();

  // Reset navigation state when component mounts
  useEffect(() => {
    reset();
  }, [reset]);

  const handleSubjectSelect = async (subjectId: string) => {
    try {
      const subject = await hierarchyService.getSubjectById(subjectId);
      if (subject) {
        navigateToTopics(subjectId, subject.name);
      }
    } catch (error) {
      console.error('Error loading subject:', error);
    }
  };

  const handleTopicSelect = async (topicId: string) => {
    try {
      const topic = await hierarchyService.getTopicById(topicId);
      if (topic) {
        navigateToSkills(topicId, topic.name);
      }
    } catch (error) {
      console.error('Error loading topic:', error);
    }
  };

  const handleSkillSelect = (skillId: string) => {
    // Navigate to the existing skill player
    router.push(`/(app)/(authenticated)/(tabs)/learn/${skillId}`);
  };

  const handleBack = () => {
    navigateBack();
  };

  const renderCurrentScreen = () => {
    switch (level) {
      case 'subjects':
        return (
          <SubjectListScreen
            onSubjectSelect={handleSubjectSelect}
          />
        );

      case 'topics':
        if (!subjectId) {
          // Fallback to subjects if no subject is selected
          return (
            <SubjectListScreen
              onSubjectSelect={handleSubjectSelect}
            />
          );
        }
        return (
          <TopicListScreen
            subjectId={subjectId}
            onTopicSelect={handleTopicSelect}
            onBack={handleBack}
          />
        );

      case 'skills':
        if (!topicId) {
          // Fallback to topics if no topic is selected
          return subjectId ? (
            <TopicListScreen
              subjectId={subjectId}
              onTopicSelect={handleTopicSelect}
              onBack={handleBack}
            />
          ) : (
            <SubjectListScreen
              onSubjectSelect={handleSubjectSelect}
            />
          );
        }
        return (
          <SkillListScreen
            topicId={topicId}
            onSkillSelect={handleSkillSelect}
            onBack={handleBack}
          />
        );

      default:
        return (
          <SubjectListScreen
            onSubjectSelect={handleSubjectSelect}
          />
        );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {renderCurrentScreen()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
