// CourseGenScreen: Main screen for AI-powered course generation

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Pressable,
  Dimensions,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  ScrollView,
  StyleSheet,
  Button,
} from 'react-native';
import { ImageBackground, Image } from 'expo-image';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { Fontisto, MaterialCommunityIcons, Ionicons } from '@expo/vector-icons';
import { useNavigation } from 'expo-router';
import Animated, { 
  useAnimatedStyle, 
  withSpring, 
  interpolate, 
  useSharedValue, 
  withTiming, 
  withSequence, 
  withDelay,
  FadeIn,
  FadeInDown,
  SlideInRight,
  ZoomIn,
  BounceIn
} from 'react-native-reanimated';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Checkbox from 'expo-checkbox';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

// Get device width for slider calculations
const { width } = Dimensions.get('window');

// Learning style options
const LEARNING_STYLES = [
  { key: 'visual', title: 'Visual', icon: 'eye-outline' as const },
  { key: 'interactive', title: 'Interactive', icon: 'game-controller-outline' as const },
  { key: 'text', title: 'Text-Based', icon: 'document-text-outline' as const },
];

// Tone options for course content
const LEARNING_TONES = [
  { key: 'academic', title: 'Academic & Formal', icon: '🎓', description: 'Uses precise, technical language' },
  { key: 'simple', title: 'Simple & Clear', icon: '🧑‍🏫', description: 'Breaks down complex topics into easy analogies' },
  { key: 'playful', title: 'Playful & Humorous', icon: '😂', description: 'Includes jokes, puns, and fun examples' },
  { key: 'professional', title: 'Professional & Direct', icon: '🚀', description: 'Focuses on business applications' },
];

// Course depth options
const COURSE_DEPTHS = [
  { key: 'quick', title: 'Quick Intro', icon: '🚀', description: 'A high-level overview (~5 Skills)' },
  { key: 'standard', title: 'Standard Course', icon: '📚', description: 'A detailed, comprehensive journey (~12 Skills)' },
  { key: 'deep', title: 'Deep Dive', icon: '🎓', description: 'An exhaustive exploration (~20+ Skills)' },
];

// Focus options for course content
const LEARNING_FOCUS = [
  { key: 'theory', title: 'Key Concepts & Theory', icon: '🧠', description: 'Focus on understanding the "why"' },
  { key: 'practical', title: 'Practical Application', icon: '🛠️', description: 'Focus on the "how"' },
  { key: 'balanced', title: 'A Balanced Mix', icon: '⚖️', description: 'Equal theory and practice' },
];

// Question type options for course exercises
const QUESTION_TYPES = [
  { key: 'multiple', label: 'Multiple-Choice Questions' },
  { key: 'fillblank', label: 'Fill-in-the-Blank' },
  { key: 'scenarios', label: 'Real-World Scenarios' },
  { key: 'matching', label: 'Matching Definitions' },
  { key: 'truefalse', label: 'True/False' },
  { key: 'shortanswer', label: 'Short Answer' },
  { key: 'essay', label: 'Essay Questions' },
];

// Knowledge level options for slider
const KNOWLEDGE_LEVELS = [
  { value: 0, label: 'Beginner', description: 'New to the subject' },
  { value: 33, label: 'Intermediate', description: 'Some basic knowledge' },
  { value: 66, label: 'Advanced', description: 'Good understanding' },
  { value: 100, label: 'Expert', description: 'Deep knowledge' },
];

export default function CourseGenScreen() {
  const navigation = useNavigation();
  const image = require('src/assets/images/image.png');

  // State for all user selections
  const [topic, setTopic] = useState('');
  const [learningGoal, setLearningGoal] = useState('');
  const [learningStyle, setLearningStyle] = useState('visual');
  const [learningTone, setLearningTone] = useState('simple');
  const [courseDepth, setCourseDepth] = useState('standard');
  const [learningFocus, setLearningFocus] = useState('balanced');
  const [selectedQuestionTypes, setSelectedQuestionTypes] = useState<Record<string, boolean>>({});
  const [duration, setDuration] = useState(30); // in minutes
  const [isGenerating, setIsGenerating] = useState(false);

  // Animated values for UI
  const sliderPosition = useSharedValue(0);
  const labelPosition = useSharedValue(0);
  const buttonScale = useSharedValue(1);
  const buttonRotation = useSharedValue(0);
  const headerOpacity = useSharedValue(0);
  const scrollY = useSharedValue(0);

  // Animate header in on mount
  useEffect(() => {
    headerOpacity.value = withTiming(1, { duration: 800 });
  }, []);

  // Animated style for the slider thumb
  const sliderStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: sliderPosition.value },
        { scale: interpolate(labelPosition.value, [0, 50], [1, 1.2]) }
      ],
      shadowOpacity: interpolate(labelPosition.value, [0, 50], [0.2, 0.8]),
    };
  });

  // Animated style for the slider label
  const labelStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      labelPosition.value,
      [0, 50, 100],
      [0, 1, 0]
    );
    return {
      opacity,
      transform: [
        { translateY: interpolate(labelPosition.value, [0, 50, 100], [20, 0, 20]) },
        { scale: interpolate(labelPosition.value, [0, 50], [0.8, 1]) }
      ],
    };
  });

  // Animated style for the generate button
  const buttonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: buttonScale.value },
        { rotateZ: `${buttonRotation.value}deg` }
      ]
    };
  });

  // Animated style for the header
  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: headerOpacity.value,
      transform: [
        { translateY: interpolate(headerOpacity.value, [0, 1], [-20, 0]) }
      ]
    };
  });

  // Animated style for the header background (fades/scales on scroll)
  const headerBackgroundStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(scrollY.value, [0, 100], [1, 0.85]),
      transform: [{ scale: interpolate(scrollY.value, [0, 100], [1, 0.95]) }]
    };
  });

  // Gesture for the knowledge slider
  const gesture = Gesture.Pan()
    .onUpdate((e) => {
      // Clamp slider position between 0 and max
      const newPosition = Math.max(0, Math.min(e.translationX, width - 100));
      sliderPosition.value = newPosition;
      labelPosition.value = withSpring(50, { damping: 12 });
    })
    .onEnd(() => {
      labelPosition.value = withTiming(0, { duration: 500 });
    });

  // Go back to previous screen
  const handleBackButtonPress = () => {
    navigation.goBack();
  };

  // Handle pressing the "Generate My Course" button
  const handleGeneratePress = async () => {
    // Animate button for feedback
    buttonScale.value = withSequence(
      withTiming(0.9, { duration: 100 }),
      withTiming(1.1, { duration: 200 }),
      withTiming(1, { duration: 150 })
    );
    buttonRotation.value = withSequence(
      withTiming(-5, { duration: 100 }),
      withTiming(5, { duration: 100 }),
      withTiming(0, { duration: 100 })
    );

    setIsGenerating(true);

    // Calculate knowledge level from slider
    const knowledgeLevelIndex = Math.floor((sliderPosition.value / (width - 100)) * 4);
    const knowledgeLevel = KNOWLEDGE_LEVELS[knowledgeLevelIndex].label;

    // Prepare data for API
    const courseData = {
      topic,
      learningGoal,
      learningStyle,
      learningTone,
      courseDepth,
      learningFocus,
      questionTypes: Object.keys(selectedQuestionTypes).filter(key => selectedQuestionTypes[key]),
      duration,
      knowledgeLevel
    };

    try {
      // Send POST request to API
      const response = await fetch('/api/courcegen', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(courseData),
      });

      const data = await response.json();

      // Handle the response data as needed
      setIsGenerating(false);

      // Optionally navigate to course details screen
      // navigation.navigate('CourseDetails', { courseData: data });

    } catch (error) {
      // Log and handle error
      console.error('Error generating course:', error);
      setIsGenerating(false);
      // Optionally show error UI
    }
  }

  // Toggle selection for a question type
  const toggleQuestionType = (key: string) => {
    setSelectedQuestionTypes(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Show animated loading UI while generating course
  if (isGenerating) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50 items-center justify-center p-8">
        {/* Animated background gradient */}
        <LinearGradient
          colors={['rgba(99, 102, 241, 0.1)', 'rgba(99, 102, 241, 0.05)']}
          className="absolute inset-0"
        />
        {/* Animated icon and sparkles */}
        <Animated.View 
          className="w-40 h-40 rounded-full bg-white items-center justify-center shadow-2xl mb-8"
          entering={ZoomIn.delay(300).duration(800)}
          style={{ elevation: 10 }}
        >
          <LinearGradient
            colors={['#6366F1', '#818CF8']}
            className="absolute inset-0 rounded-full opacity-10"
          />
          <Animated.View 
            entering={FadeIn.delay(800).duration(500)}
            className="items-center"
          >
            <Ionicons name="sparkles" size={80} color="#6C63FF" />
            <Animated.View 
              entering={BounceIn.delay(1200).duration(1000)}
              className="absolute -right-2 -top-2 bg-indigo-500 rounded-full p-2"
            >
              <Ionicons name="flash" size={20} color="#fff" />
            </Animated.View>
          </Animated.View>
        </Animated.View>
        {/* Animated loading text */}
        <Animated.Text 
          className="text-4xl font-bold text-gray-800 text-center"
          entering={FadeInDown.delay(400).duration(800)}
        >
          Crafting your course...
        </Animated.Text>
        <Animated.Text 
          className="text-lg text-gray-600 text-center mt-4 px-6"
          entering={FadeInDown.delay(600).duration(800)}
        >
          Our AI is designing a personalized learning experience just for you
        </Animated.Text>
        {/* Animated progress bar */}
        <Animated.View 
          className="w-full h-3 bg-gray-200 rounded-full mt-12 overflow-hidden"
          entering={FadeIn.delay(1000).duration(500)}
          style={{ elevation: 2 }}
        >
          <Animated.View 
            className="h-full"
            style={{
              width: '60%',
              transform: [{ translateX: -width * 0.6 }],
              backgroundColor: '#6C63FF',
            }}
            entering={SlideInRight.delay(1200).duration(2000)}
          />
        </Animated.View>
        {/* Animated dots */}
        <Animated.View 
          className="flex-row items-center justify-center mt-10 space-x-2"
          entering={FadeIn.delay(1500).duration(800)}
        >
          <View className="w-2 h-2 bg-indigo-300 rounded-full" />
          <View className="w-2 h-2 bg-indigo-400 rounded-full" />
          <View className="w-2 h-2 bg-indigo-500 rounded-full" />
        </Animated.View>
      </SafeAreaView>
    )
  }

  // Example function for testing API (not used in UI)
  async function fetchHello() {
    const response = await fetch('/api/hello');
    const data = await response.json();
    alert('Hello ' + data.hello);
  }

  // Main UI rendering
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
    <View className="flex-1 bg-gray-50">
      {/* Animated header background */}
      <Animated.View 
        className="absolute top-0 left-0 right-0 h-72"
        style={headerBackgroundStyle}
      >
        <LinearGradient
          colors={['#6366F1', '#4F46E5']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          className="absolute inset-0 rounded-b-[40px]"
        />
        <Image
          source={require('src/assets/images/image.png')}
          className="absolute inset-0 opacity-10 rounded-b-[40px]"
          contentFit="cover"
        />
      </Animated.View>
      
      {/* Animated header with back/help buttons */}
      <Animated.View 
        className="flex-row w-full justify-between items-center px-6 pt-16"
        style={headerAnimatedStyle}
      >
        {/* Back button */}
        <TouchableOpacity 
          className="w-12 h-12 rounded-full bg-white/30 backdrop-blur-lg justify-center items-center shadow-md"
          onPress={handleBackButtonPress}
          style={{ elevation: 4 }}
        >
          <MaterialIcons name="arrow-back-ios" color="#fff" size={22} />
        </TouchableOpacity>
        
        {/* Title */}
        <Text className="text-2xl font-bold text-white">Course Creator</Text>
        
        {/* Help button (no action) */}
        <TouchableOpacity className="w-12 h-12 rounded-full bg-white/30 backdrop-blur-lg justify-center items-center shadow-md">
          <Ionicons name="help-circle-outline" color="#fff" size={24} />
        </TouchableOpacity>
      </Animated.View>
     
      {/* Main scrollable content */}
      <Animated.ScrollView 
        className="flex-1 mt-10 rounded-t-[40px] px-5 pt-6" 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
        onScroll={(event) => {
          scrollY.value = event.nativeEvent.contentOffset.y;
        }}
        scrollEventThrottle={16}
      >
        {/* Title and subtitle */}
        <Animated.View entering={FadeInDown.duration(800).delay(100)}>
          <Text className="text-4xl font-bold text-gray-800 mb-1">AI Course Architect</Text>
          <Text className="text-lg text-gray-600 mb-8">Craft your perfect learning experience</Text>
        </Animated.View>

        {/* Course Topic input */}
        <Animated.View 
          className="bg-white rounded-3xl p-6 mb-6 shadow-md"
          style={{ elevation: 3 }}
          entering={FadeInDown.duration(800).delay(200)}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 rounded-full bg-indigo-100 items-center justify-center mr-3">
              <Ionicons name="book-outline" size={20} color="#6366F1" />
            </View>
            <Text className="text-xl font-semibold text-gray-800">Course Topic</Text>
          </View>
          <TextInput
            className="bg-gray-50 rounded-xl p-5 text-base text-gray-800 border border-gray-100"
            placeholder="e.g., The History of Ancient Rome"
            value={topic}
            onChangeText={setTopic}
            placeholderTextColor="#999"
            style={{ fontSize: 16 }}
          />
        </Animated.View>

        {/* Learning Goal input */}
        <Animated.View 
          className="bg-white rounded-3xl p-6 mb-6 shadow-md"
          style={{ elevation: 3 }}
          entering={FadeInDown.duration(800).delay(300)}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 rounded-full bg-indigo-100 items-center justify-center mr-3">
              <Ionicons name="flag-outline" size={20} color="#6366F1" />
            </View>
            <Text className="text-xl font-semibold text-gray-800">Learning Goal</Text>
          </View>
          <TextInput
            className="bg-gray-50 rounded-xl p-5 text-base text-gray-800 border border-gray-100"
            placeholder="e.g., Understand the rise and fall of the Roman Empire"
            value={learningGoal}
            onChangeText={setLearningGoal}
            placeholderTextColor="#999"
            multiline
            style={{ fontSize: 16, minHeight: 80 }}
          />
        </Animated.View>

        {/* Knowledge Level slider */}
        <Animated.View 
          className="bg-white rounded-3xl p-6 mb-6 shadow-md"
          style={{ elevation: 3 }}
          entering={FadeInDown.duration(800).delay(400)}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 rounded-full bg-indigo-100 items-center justify-center mr-3">
              <Ionicons name="school-outline" size={20} color="#6366F1" />
            </View>
            <Text className="text-xl font-semibold text-gray-800">Current Knowledge Level</Text>
          </View>
          {/* Custom slider using gesture handler */}
          <GestureDetector gesture={gesture}>
            <View className="h-16 bg-gray-100 rounded-full mb-6 overflow-hidden">
              {/* Slider track gradient */}
              <LinearGradient
                colors={['#6366F1', '#818CF8']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                className="h-full absolute"
                style={{ width: sliderPosition.value + 40 }}
              />
              {/* Slider thumb */}
              <Animated.View 
                className="w-14 h-14 bg-white rounded-full absolute top-1 shadow-xl"
                style={[sliderStyle, { elevation: 8 }]}
              >
                <LinearGradient
                  colors={['#ffffff', '#f9f9f9']}
                  className="w-full h-full rounded-full items-center justify-center"
                >
                  <Ionicons name="school" size={22} color="#6366F1" />
                </LinearGradient>
              </Animated.View>
              {/* Animated label above slider */}
              <Animated.View 
                className="absolute -top-12 items-center justify-center bg-indigo-500 px-4 py-2 rounded-xl shadow-lg"
                style={[labelStyle, { left: sliderPosition.value - 20 }]}
              >
                <Text className="text-sm font-bold text-white">
                  {KNOWLEDGE_LEVELS[Math.floor((sliderPosition.value / (width - 100)) * 4)].label}
                </Text>
                <View className="w-3 h-3 bg-indigo-500 rotate-45 absolute -bottom-1" />
              </Animated.View>
            </View>
          </GestureDetector>
          {/* Description for current knowledge level */}
          <Text className="text-sm text-gray-500 text-center">
            {KNOWLEDGE_LEVELS[Math.floor((sliderPosition.value / (width - 100)) * 4)].description}
          </Text>
        </Animated.View>

        {/* Learning Tone selection */}
        <Animated.View 
          className="bg-white rounded-3xl p-6 mb-6 shadow-md"
          style={{ elevation: 3 }}
          entering={FadeInDown.duration(800).delay(500)}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 rounded-full bg-indigo-100 items-center justify-center mr-3">
              <Ionicons name="chatbubble-ellipses-outline" size={20} color="#6366F1" />
            </View>
            <Text className="text-xl font-semibold text-gray-800">Learning Tone</Text>
          </View>
          <View className="flex-row flex-wrap justify-between gap-3">
            {LEARNING_TONES.map((tone) => (
              <TouchableOpacity 
                key={tone.key}
                className={`flex-1 min-w-[45%] ${learningTone === tone.key ? 'bg-indigo-500' : 'bg-indigo-50'} p-5 rounded-2xl shadow-sm`}
                style={{ elevation: learningTone === tone.key ? 4 : 1 }}
                onPress={() => setLearningTone(tone.key)}
              >
                <Text className="text-3xl mb-3">{tone.icon}</Text>
                <Text className={`font-bold text-base ${learningTone === tone.key ? 'text-white' : 'text-indigo-500'}`}>
                  {tone.title}
                </Text>
                <Text className={`text-xs mt-2 ${learningTone === tone.key ? 'text-indigo-100' : 'text-indigo-400'}`}>
                  {tone.description}
                </Text>
                {learningTone === tone.key && (
                  <View className="absolute top-2 right-2 bg-white rounded-full p-1">
                    <Ionicons name="checkmark-circle" size={16} color="#6366F1" />
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Course Depth selection */}
        <Animated.View 
          className="bg-white rounded-3xl p-6 mb-6 shadow-md"
          style={{ elevation: 3 }}
          entering={FadeInDown.duration(800).delay(600)}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 rounded-full bg-indigo-100 items-center justify-center mr-3">
              <Ionicons name="layers-outline" size={20} color="#6366F1" />
            </View>
            <Text className="text-xl font-semibold text-gray-800">Course Depth</Text>
          </View>
          <Text className="text-gray-600 mb-4">How deep do you want to go?</Text>
          <View className="flex-col space-y-4">
            {COURSE_DEPTHS.map((depth) => (
              <TouchableOpacity 
                key={depth.key}
                className={`flex-row items-center ${courseDepth === depth.key ? 'bg-indigo-500' : 'bg-indigo-50'} p-5 rounded-2xl shadow-sm`}
                style={{ elevation: courseDepth === depth.key ? 4 : 1 }}
                onPress={() => setCourseDepth(depth.key)}
              >
                <View className={`w-14 h-14 rounded-full ${courseDepth === depth.key ? 'bg-indigo-400' : 'bg-indigo-100'} items-center justify-center`}>
                  <Text className="text-2xl">{depth.icon}</Text>
                </View>
                <View className="ml-4 flex-1">
                  <Text className={`font-bold text-lg ${courseDepth === depth.key ? 'text-white' : 'text-indigo-500'}`}>
                    {depth.title}
                  </Text>
                  <Text className={`text-sm ${courseDepth === depth.key ? 'text-indigo-100' : 'text-indigo-400'}`}>
                    {depth.description}
                  </Text>
                </View>
                {courseDepth === depth.key && (
                  <Ionicons name="checkmark-circle" size={24} color="#fff" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Learning Focus selection */}
        <Animated.View 
          className="bg-white rounded-3xl p-6 mb-6 shadow-md"
          style={{ elevation: 3 }}
          entering={FadeInDown.duration(800).delay(700)}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 rounded-full bg-indigo-100 items-center justify-center mr-3">
              <Ionicons name="bulb-outline" size={20} color="#6366F1" />
            </View>
            <Text className="text-xl font-semibold text-gray-800">Learning Focus</Text>
          </View>
          <View className="flex-col space-y-4">
            {LEARNING_FOCUS.map((focus) => (
              <TouchableOpacity 
                key={focus.key}
                className={`flex-row items-center ${learningFocus === focus.key ? 'bg-indigo-500' : 'bg-indigo-50'} p-5 rounded-2xl shadow-sm`}
                style={{ elevation: learningFocus === focus.key ? 4 : 1 }}
                onPress={() => setLearningFocus(focus.key)}
              >
                <View className={`w-14 h-14 rounded-full ${learningFocus === focus.key ? 'bg-indigo-400' : 'bg-indigo-100'} items-center justify-center`}>
                  <Text className="text-2xl">{focus.icon}</Text>
                </View>
                <View className="ml-4 flex-1">
                  <Text className={`font-bold text-lg ${learningFocus === focus.key ? 'text-white' : 'text-indigo-500'}`}>
                    {focus.title}
                  </Text>
                  <Text className={`text-sm ${learningFocus === focus.key ? 'text-indigo-100' : 'text-indigo-400'}`}>
                    {focus.description}
                  </Text>
                </View>
                {learningFocus === focus.key && (
                  <Ionicons name="checkmark-circle" size={24} color="#fff" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Preferred Question Types selection */}
        <Animated.View 
          className="bg-white rounded-3xl p-6 mb-6 shadow-md"
          style={{ elevation: 3 }}
          entering={FadeInDown.duration(800).delay(800)}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 rounded-full bg-indigo-100 items-center justify-center mr-3">
              <Ionicons name="help-circle-outline" size={20} color="#6366F1" />
            </View>
            <Text className="text-xl font-semibold text-gray-800">Preferred Question Types</Text>
          </View>
          <Text className="text-gray-600 mb-4">Select the types of questions you'd like to see</Text>
          <View className="space-y-4">
            {QUESTION_TYPES.map((type) => (
              <TouchableOpacity 
                key={type.key}
                className={`flex-row items-center p-4 rounded-xl ${selectedQuestionTypes[type.key] ? 'bg-indigo-50 border border-indigo-200' : 'bg-white border border-gray-100'}`}
                onPress={() => toggleQuestionType(type.key)}
                style={{ elevation: selectedQuestionTypes[type.key] ? 2 : 0 }}
              >
                <Checkbox
                  value={selectedQuestionTypes[type.key] || false}
                  onValueChange={() => toggleQuestionType(type.key)}
                  color={selectedQuestionTypes[type.key] ? '#6366F1' : undefined}
                  style={{ width: 22, height: 22, borderRadius: 6 }}
                />
                <Text className="text-gray-700 ml-3 text-base">{type.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Preferred Learning Style selection */}
        <Animated.View 
          className="bg-white rounded-3xl p-6 mb-6 shadow-md"
          style={{ elevation: 3 }}
          entering={FadeInDown.duration(800).delay(900)}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 rounded-full bg-indigo-100 items-center justify-center mr-3">
              <Ionicons name="color-palette-outline" size={20} color="#6366F1" />
            </View>
            <Text className="text-xl font-semibold text-gray-800">Preferred Learning Style</Text>
          </View>
          <View className="flex-row justify-between">
            {LEARNING_STYLES.map((style) => (
              <TouchableOpacity 
                key={style.key}
                className={`flex-1 mx-1 items-center ${learningStyle === style.key ? 'bg-indigo-500' : 'bg-indigo-50'} py-5 px-4 rounded-2xl shadow-sm`}
                style={{ elevation: learningStyle === style.key ? 4 : 1 }}
                onPress={() => setLearningStyle(style.key)}
              >
                <View className={`w-14 h-14 rounded-full ${learningStyle === style.key ? 'bg-indigo-400' : 'bg-indigo-100'} items-center justify-center mb-3`}>
                  <Ionicons 
                    name={style.icon} 
                    color={learningStyle === style.key ? "#fff" : "#6C63FF"} 
                    size={24} 
                  />
                </View>
                <Text className={`font-bold ${learningStyle === style.key ? 'text-white' : 'text-indigo-500'}`}>
                  {style.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
        
        {/* Desired Course Length (static for now) */}
        <Animated.View 
          className="bg-white rounded-3xl p-6 mb-20 shadow-md"
          style={{ elevation: 2 }}
          entering={FadeInDown.duration(800).delay(1000)}
        >
          <Text className="text-xl font-semibold text-gray-800 mb-4">Desired Course Length</Text>
          <View className="items-center">
            <View className="bg-indigo-500 px-8 py-3 rounded-full shadow-lg shadow-indigo-500/30 mb-4">
              <Text className="text-3xl font-bold text-white text-center">{duration} minutes</Text>
            </View>
            <Text className="text-center text-gray-500 italic mt-2">Interactive slider coming soon!</Text>
          </View>
        </Animated.View>
      </Animated.ScrollView>

      {/* Bottom blurred bar with animated generate button */}
      <BlurView 
        intensity={20} 
        tint="light" 
        className="absolute bottom-0 left-0 right-0 h-24 flex justify-center items-center px-6"
      >
        <Animated.View 
          style={buttonAnimatedStyle}
          entering={BounceIn.delay(1200).duration(1000)}
        >
          <TouchableOpacity 
            className="w-full bg-indigo-500 py-4 rounded-2xl justify-center items-center shadow-2xl"
            style={{ 
              elevation: 8,
              shadowColor: '#6366F1',
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.4,
              shadowRadius: 12,
            }}
            onPress={handleGeneratePress}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#6366F1', '#4F46E5']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              className="absolute inset-0 rounded-2xl"
            />
            <View className="flex-row items-center">
              <Ionicons name="sparkles" color="#fff" size={24} />
              <Text className="text-white text-xl font-bold ml-2">Generate My Course</Text>
            </View>
          </TouchableOpacity>
        </Animated.View>
      </BlurView>
    </View>
    </GestureHandlerRootView>
  );
}