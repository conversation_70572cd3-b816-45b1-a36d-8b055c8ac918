/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      colors: {
        // Primary color scale (Green theme)
        primary: {
          50: '#f0f9f1',
          100: '#dcf3df',
          200: '#b9e6c0',
          300: '#8ed59a',
          400: '#5abe70',
          500: '#3da450',
          600: '#2a833c',
          700: '#236833',
          800: '#1d512a',
          900: '#184324',
          950: '#0c2515',
          DEFAULT: '#3da450',
        },
        // Secondary color scale (Brown/tan)
        secondary: {
          50: '#f8f6f1',
          100: '#efe9e1',
          200: '#ded0c3',
          300: '#cbb39e',
          400: '#b79274',
          500: '#a77b59',
          600: '#95674a',
          700: '#7c533e',
          800: '#664536',
          900: '#543a30',
          950: '#2d1e19',
          DEFAULT: '#a77b59',
        },
        // Accent color scale (Bright green)
        accent: {
          50: '#f5fff0',
          100: '#e9ffdc',
          200: '#d0ffb7',
          300: '#adff85',
          400: '#87ff4d',
          500: '#64ea26',
          600: '#4cc70f',
          700: '#3d9c11',
          800: '#347b17',
          900: '#2e6619',
          950: '#153a09',
          DEFAULT: '#64ea26',
        },
        // Success color scale
        success: {
          50: '#ecfdf5',
          100: '#d1fae5',
          200: '#a7f3d0',
          300: '#6ee7b7',
          400: '#34d399',
          500: '#10b981',
          600: '#059669',
          700: '#047857',
          800: '#065f46',
          900: '#064e3b',
          950: '#022c22',
          DEFAULT: '#10b981',
        },
        // Warning color scale
        warning: {
          50: '#fff9ec',
          100: '#ffeac2',
          200: '#fed58b',
          300: '#fec554',
          400: '#fdb022',
          500: '#f79009',
          600: '#dc6803',
          700: '#b54708',
          800: '#923a0e',
          900: '#78310f',
          950: '#451a03',
          DEFAULT: '#f79009',
        },
        // Error color scale
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
          950: '#450a0a',
          DEFAULT: '#ef4444',
        },
        // Semantic theme colors
        background: 'var(--color-background, #f9faf8)',
        foreground: 'var(--color-text, #111827)',
        card: 'var(--color-card, #ffffff)',
        'card-foreground': 'var(--color-text, #111827)',
        popover: 'var(--color-card, #ffffff)',
        'popover-foreground': 'var(--color-text, #111827)',
        muted: 'var(--color-muted, #f0f9f1)',
        'muted-foreground': 'var(--color-secondary-text, #4b5563)',
        border: 'var(--color-border, #dcf3df)',
        input: 'var(--color-input-background, #ffffff)',
        ring: 'var(--color-primary-500, #3da450)',
        destructive: 'var(--color-error-500, #ef4444)',
        'destructive-foreground': 'var(--color-error-50, #fef2f2)',
      },
      fontFamily: {
        Poppins_400Regular: ['Poppins_400Regular'],
        Poppins_500Medium: ['Poppins_500Medium'],
        Poppins_600SemiBold: ['Poppins_600SemiBold'],
        Poppins_700Bold: ['Poppins_700Bold'],
      },
    },
  },
  plugins: [],
};
