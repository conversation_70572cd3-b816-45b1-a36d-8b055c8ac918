import { UUID } from '@/types/skill';

// ==================== DATABASE-LIKE TABLES ====================
// Using normalized structure with foreign key relationships

// Subjects Table - No nested data, just subject metadata
export interface SubjectRecord {
  id: UUID;
  name: string;
  description: string;
  category: string;
  order: number;
  icon: string;
  color: string;
  estimatedDuration: number; // Total estimated duration (calculated from topics)
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  isPremium: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export const SUBJECTS_TABLE: SubjectRecord[] = [
  {
    id: 'subject-ai-fundamentals',
    name: 'AI Fundamentals',
    description: 'Master the core concepts and tools of artificial intelligence',
    category: 'Technology',
    order: 1,
    icon: '🤖',
    color: '#3B82F6',
    estimatedDuration: 180,
    difficulty: 'beginner',
    isPremium: false
  },
  {
    id: 'subject-productivity',
    name: 'Productivity & Focus',
    description: 'Learn proven techniques to boost your productivity and focus',
    category: 'Personal Development',
    order: 2,
    icon: '⚡',
    color: '#10B981',
    estimatedDuration: 120,
    difficulty: 'beginner',
    isPremium: false
  },
  {
    id: 'subject-machine-learning',
    name: 'Machine Learning',
    description: 'Dive deep into machine learning algorithms and applications',
    category: 'Technology',
    order: 3,
    icon: '🧠',
    color: '#8B5CF6',
    estimatedDuration: 240,
    difficulty: 'intermediate',
    isPremium: true
  },
  {
    id: 'subject-data-science',
    name: 'Data Science',
    description: 'Explore data analysis, visualization, and statistical methods',
    category: 'Technology',
    order: 4,
    icon: '📊',
    color: '#F59E0B',
    estimatedDuration: 300,
    difficulty: 'intermediate',
    isPremium: true
  }
];

// Topics Table - References subjects via subjectId foreign key
export interface TopicRecord {
  id: UUID;
  name: string;
  description: string;
  subjectId: UUID; // Foreign key reference to SUBJECTS_TABLE
  order: number;
  estimatedDuration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites?: UUID[]; // Array of topic IDs that must be completed first
  createdAt?: string;
  updatedAt?: string;
}

export const TOPICS_TABLE: TopicRecord[] = [
  // AI Fundamentals Topics
  {
    id: 'topic-ai-basics',
    name: 'AI Basics',
    description: 'Introduction to artificial intelligence concepts and terminology',
    subjectId: 'subject-ai-fundamentals',
    order: 1,
    estimatedDuration: 60,
    difficulty: 'beginner'
  },
  {
    id: 'topic-ai-tools',
    name: 'AI Tools & Applications',
    description: 'Practical AI tools for everyday use',
    subjectId: 'subject-ai-fundamentals',
    order: 2,
    estimatedDuration: 90,
    difficulty: 'beginner',
    prerequisites: ['topic-ai-basics']
  },
  {
    id: 'topic-ai-ethics',
    name: 'AI Ethics & Society',
    description: 'Understanding the ethical implications of AI technology',
    subjectId: 'subject-ai-fundamentals',
    order: 3,
    estimatedDuration: 30,
    difficulty: 'beginner',
    prerequisites: ['topic-ai-basics']
  },

  // Productivity Topics
  {
    id: 'topic-productivity-basics',
    name: 'Focus Fundamentals',
    description: 'Master the basics of focus and attention management',
    subjectId: 'subject-productivity',
    order: 1,
    estimatedDuration: 60,
    difficulty: 'beginner'
  },
  {
    id: 'topic-time-management',
    name: 'Time Management',
    description: 'Advanced techniques for managing your time effectively',
    subjectId: 'subject-productivity',
    order: 2,
    estimatedDuration: 60,
    difficulty: 'intermediate',
    prerequisites: ['topic-productivity-basics']
  },

  // Machine Learning Topics
  {
    id: 'topic-ml-foundations',
    name: 'ML Foundations',
    description: 'Core machine learning concepts and algorithms',
    subjectId: 'subject-machine-learning',
    order: 1,
    estimatedDuration: 120,
    difficulty: 'intermediate'
  },
  {
    id: 'topic-neural-networks',
    name: 'Neural Networks',
    description: 'Deep dive into neural networks and deep learning',
    subjectId: 'subject-machine-learning',
    order: 2,
    estimatedDuration: 120,
    difficulty: 'advanced',
    prerequisites: ['topic-ml-foundations']
  },

  // Data Science Topics
  {
    id: 'topic-data-analysis',
    name: 'Data Analysis',
    description: 'Learn to analyze and interpret data effectively',
    subjectId: 'subject-data-science',
    order: 1,
    estimatedDuration: 150,
    difficulty: 'intermediate'
  },
  {
    id: 'topic-data-visualization',
    name: 'Data Visualization',
    description: 'Create compelling visualizations from your data',
    subjectId: 'subject-data-science',
    order: 2,
    estimatedDuration: 150,
    difficulty: 'intermediate',
    prerequisites: ['topic-data-analysis']
  }
];

// Skills Table - References topics via topicId foreign key
export interface SkillRecord {
  id: UUID;
  name: string;
  description: string;
  topicId: UUID; // Foreign key reference to TOPICS_TABLE
  order: number;
  estimatedDuration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  fileName: string; // Reference to the actual skill JSON file
  prerequisites?: UUID[]; // Array of skill IDs that must be completed first
  tags?: string[]; // Optional tags for categorization
  isActive?: boolean; // Whether the skill is currently available
  createdAt?: string;
  updatedAt?: string;
}

export const SKILLS_TABLE: SkillRecord[] = [
  // Productivity Skills
  {
    id: 'skill-dummy-productivity-002',
    name: 'The Enemy: Multitasking',
    description: 'Learn why multitasking is a myth and how to reclaim your focus by mastering the art of single-tasking.',
    topicId: 'topic-productivity-basics',
    order: 1,
    estimatedDuration: 30,
    difficulty: 'beginner',
    fileName: 'dummySkill.json',
    tags: ['focus', 'productivity', 'attention'],
    isActive: true
  },

  // AI Fundamentals Skills
  {
    id: 'skill-better-001',
    name: 'Better',
    description: 'A deep dive into the principles, phenomena, and foundational concepts of quantum physics, from the quantization of energy to the bizarre world of quantum entanglement.',
    topicId: 'topic-ai-basics',
    order: 1,
    estimatedDuration: 60,
    difficulty: 'advanced',
    fileName: 'better.json',
    tags: ['quantum', 'physics', 'fundamentals'],
    isActive: true
  },

  // Time Management Skills
  {
    id: 'skill-calculus-001',
    name: 'Calculus Fundamentals',
    description: 'Master the fundamental concepts of calculus including limits, derivatives, and integrals.',
    topicId: 'topic-time-management',
    order: 1,
    estimatedDuration: 45,
    difficulty: 'intermediate',
    fileName: 'calculous.json',
    tags: ['mathematics', 'calculus', 'fundamentals'],
    isActive: true
  }
];

// ==================== QUERY HELPER FUNCTIONS ====================
// Database-like query functions for working with the normalized data

export const DatabaseQueries = {
  // Subject queries
  getAllSubjects: (): SubjectRecord[] => {
    return SUBJECTS_TABLE.slice().sort((a, b) => a.order - b.order);
  },

  getSubjectById: (id: UUID): SubjectRecord | null => {
    return SUBJECTS_TABLE.find(subject => subject.id === id) || null;
  },

  getSubjectsByCategory: (category: string): SubjectRecord[] => {
    return SUBJECTS_TABLE
      .filter(subject => subject.category === category)
      .sort((a, b) => a.order - b.order);
  },

  // Topic queries
  getTopicsBySubjectId: (subjectId: UUID): TopicRecord[] => {
    return TOPICS_TABLE
      .filter(topic => topic.subjectId === subjectId)
      .sort((a, b) => a.order - b.order);
  },

  getTopicById: (id: UUID): TopicRecord | null => {
    return TOPICS_TABLE.find(topic => topic.id === id) || null;
  },

  getTopicsByDifficulty: (difficulty: 'beginner' | 'intermediate' | 'advanced'): TopicRecord[] => {
    return TOPICS_TABLE
      .filter(topic => topic.difficulty === difficulty)
      .sort((a, b) => a.order - b.order);
  },

  // Skill queries
  getSkillsByTopicId: (topicId: UUID): SkillRecord[] => {
    return SKILLS_TABLE
      .filter(skill => skill.topicId === topicId && skill.isActive !== false)
      .sort((a, b) => a.order - b.order);
  },

  getSkillById: (id: UUID): SkillRecord | null => {
    return SKILLS_TABLE.find(skill => skill.id === id) || null;
  },

  getSkillsByTag: (tag: string): SkillRecord[] => {
    return SKILLS_TABLE
      .filter(skill => skill.tags?.includes(tag) && skill.isActive !== false)
      .sort((a, b) => a.order - b.order);
  },

  getSkillsByDifficulty: (difficulty: 'beginner' | 'intermediate' | 'advanced'): SkillRecord[] => {
    return SKILLS_TABLE
      .filter(skill => skill.difficulty === difficulty && skill.isActive !== false)
      .sort((a, b) => a.order - b.order);
  },

  // Relationship queries
  getSubjectWithTopics: (subjectId: UUID): { subject: SubjectRecord; topics: TopicRecord[] } | null => {
    const subject = DatabaseQueries.getSubjectById(subjectId);
    if (!subject) return null;

    const topics = DatabaseQueries.getTopicsBySubjectId(subjectId);
    return { subject, topics };
  },

  getTopicWithSkills: (topicId: UUID): { topic: TopicRecord; skills: SkillRecord[] } | null => {
    const topic = DatabaseQueries.getTopicById(topicId);
    if (!topic) return null;

    const skills = DatabaseQueries.getSkillsByTopicId(topicId);
    return { topic, skills };
  },

  // Search queries
  searchSkills: (query: string): SkillRecord[] => {
    const lowerQuery = query.toLowerCase();
    return SKILLS_TABLE
      .filter(skill =>
        skill.isActive !== false && (
          skill.name.toLowerCase().includes(lowerQuery) ||
          skill.description.toLowerCase().includes(lowerQuery) ||
          skill.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
        )
      )
      .sort((a, b) => a.order - b.order);
  },

  searchTopics: (query: string): TopicRecord[] => {
    const lowerQuery = query.toLowerCase();
    return TOPICS_TABLE
      .filter(topic =>
        topic.name.toLowerCase().includes(lowerQuery) ||
        topic.description.toLowerCase().includes(lowerQuery)
      )
      .sort((a, b) => a.order - b.order);
  }
};

// Legacy compatibility - for backward compatibility with existing code
export const DEV_SUBJECTS = SUBJECTS_TABLE;
export const DEV_TOPICS = TOPICS_TABLE;
export const DEV_SKILLS = SKILLS_TABLE;

// Skill file mapping for content loading
export const SKILL_FILES: Record<string, UUID> = {
  'dummySkill.json': 'skill-dummy-productivity-002',
  'better.json': 'skill-better-001',
  'calculous.json': 'skill-calculus-001'
};

// Types are already exported above with their interface declarations
