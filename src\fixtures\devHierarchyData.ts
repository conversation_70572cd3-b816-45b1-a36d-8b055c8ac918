import { Subject, Topic, UUID } from '@/types/skill';

// Development mode dummy data - only metadata, no full skill content
export const DEV_SUBJECTS: Subject[] = [
  {
    id: 'subject-ai-fundamentals',
    name: 'AI Fundamentals',
    description: 'Master the core concepts and tools of artificial intelligence',
    category: 'Technology',
    order: 1,
    topics: [], // Will be populated dynamically
    icon: '🤖',
    color: '#3B82F6',
    estimatedDuration: 180,
    difficulty: 'beginner',
    isPremium: false
  },
  {
    id: 'subject-productivity',
    name: 'Productivity & Focus',
    description: 'Learn proven techniques to boost your productivity and focus',
    category: 'Personal Development',
    order: 2,
    topics: [], // Will be populated dynamically
    icon: '⚡',
    color: '#10B981',
    estimatedDuration: 120,
    difficulty: 'beginner',
    isPremium: false
  },
  {
    id: 'subject-machine-learning',
    name: 'Machine Learning',
    description: 'Dive deep into machine learning algorithms and applications',
    category: 'Technology',
    order: 3,
    topics: [], // Will be populated dynamically
    icon: '🧠',
    color: '#8B5CF6',
    estimatedDuration: 240,
    difficulty: 'intermediate',
    isPremium: true
  },
  {
    id: 'subject-data-science',
    name: 'Data Science',
    description: 'Explore data analysis, visualization, and statistical methods',
    category: 'Technology',
    order: 4,
    topics: [], // Will be populated dynamically
    icon: '📊',
    color: '#F59E0B',
    estimatedDuration: 300,
    difficulty: 'intermediate',
    isPremium: true
  }
];

export const DEV_TOPICS: Record<UUID, Topic[]> = {
  'subject-ai-fundamentals': [
    {
      id: 'topic-ai-basics',
      name: 'AI Basics',
      description: 'Introduction to artificial intelligence concepts and terminology',
      subjectId: 'subject-ai-fundamentals',
      order: 1,
      skills: [], // Will be populated dynamically
      estimatedDuration: 60,
      difficulty: 'beginner'
    },
    {
      id: 'topic-ai-tools',
      name: 'AI Tools & Applications',
      description: 'Practical AI tools for everyday use',
      subjectId: 'subject-ai-fundamentals',
      order: 2,
      skills: [], // Will be populated dynamically
      estimatedDuration: 90,
      difficulty: 'beginner',
      prerequisites: ['topic-ai-basics']
    },
    {
      id: 'topic-ai-ethics',
      name: 'AI Ethics & Society',
      description: 'Understanding the ethical implications of AI technology',
      subjectId: 'subject-ai-fundamentals',
      order: 3,
      skills: [], // Will be populated dynamically
      estimatedDuration: 30,
      difficulty: 'beginner',
      prerequisites: ['topic-ai-basics']
    }
  ],
  'subject-productivity': [
    {
      id: 'topic-productivity-basics',
      name: 'Focus Fundamentals',
      description: 'Master the basics of focus and attention management',
      subjectId: 'subject-productivity',
      order: 1,
      skills: [], // Will be populated dynamically
      estimatedDuration: 60,
      difficulty: 'beginner'
    },
    {
      id: 'topic-time-management',
      name: 'Time Management',
      description: 'Advanced techniques for managing your time effectively',
      subjectId: 'subject-productivity',
      order: 2,
      skills: [], // Will be populated dynamically
      estimatedDuration: 60,
      difficulty: 'intermediate',
      prerequisites: ['topic-productivity-basics']
    }
  ],
  'subject-machine-learning': [
    {
      id: 'topic-ml-foundations',
      name: 'ML Foundations',
      description: 'Core machine learning concepts and algorithms',
      subjectId: 'subject-machine-learning',
      order: 1,
      skills: [], // Will be populated dynamically
      estimatedDuration: 120,
      difficulty: 'intermediate'
    },
    {
      id: 'topic-neural-networks',
      name: 'Neural Networks',
      description: 'Deep dive into neural networks and deep learning',
      subjectId: 'subject-machine-learning',
      order: 2,
      skills: [], // Will be populated dynamically
      estimatedDuration: 120,
      difficulty: 'advanced',
      prerequisites: ['topic-ml-foundations']
    }
  ],
  'subject-data-science': [
    {
      id: 'topic-data-analysis',
      name: 'Data Analysis',
      description: 'Learn to analyze and interpret data effectively',
      subjectId: 'subject-data-science',
      order: 1,
      skills: [], // Will be populated dynamically
      estimatedDuration: 150,
      difficulty: 'intermediate'
    },
    {
      id: 'topic-data-visualization',
      name: 'Data Visualization',
      description: 'Create compelling visualizations from your data',
      subjectId: 'subject-data-science',
      order: 2,
      skills: [], // Will be populated dynamically
      estimatedDuration: 150,
      difficulty: 'intermediate',
      prerequisites: ['topic-data-analysis']
    }
  ]
};

// Skill metadata mapping - references to existing skill JSON files
export interface SkillMetadata {
  id: UUID;
  name: string;
  description: string;
  topicId: UUID;
  order: number;
  estimatedDuration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  fileName: string; // Reference to the actual skill JSON file
}

export const DEV_SKILLS: Record<UUID, SkillMetadata[]> = {
  'topic-productivity-basics': [
    {
      id: 'skill-dummy-productivity-002',
      name: 'The Enemy: Multitasking',
      description: 'Learn why multitasking is a myth and how to reclaim your focus by mastering the art of single-tasking.',
      topicId: 'topic-productivity-basics',
      order: 1,
      estimatedDuration: 30,
      difficulty: 'beginner',
      fileName: 'dummySkill.json'
    }
  ],
  'topic-ai-basics': [
    {
      id: 'skill-better-001',
      name: 'Better',
      description: 'A deep dive into the principles, phenomena, and foundational concepts of quantum physics, from the quantization of energy to the bizarre world of quantum entanglement.',
      topicId: 'topic-ai-basics',
      order: 1,
      estimatedDuration: 60,
      difficulty: 'advanced',
      fileName: 'better.json'
    }
  ],
  'topic-time-management': [
    {
      id: 'skill-calculus-001',
      name: 'Calculus Fundamentals',
      description: 'Master the fundamental concepts of calculus including limits, derivatives, and integrals.',
      topicId: 'topic-time-management',
      order: 1,
      estimatedDuration: 45,
      difficulty: 'intermediate',
      fileName: 'calculous.json'
    }
  ]
};

// Available skill files mapping
export const SKILL_FILES: Record<string, string> = {
  'dummySkill.json': 'skill-dummy-productivity-002',
  'better.json': 'skill-better-001', 
  'calculous.json': 'skill-calculus-001'
};
