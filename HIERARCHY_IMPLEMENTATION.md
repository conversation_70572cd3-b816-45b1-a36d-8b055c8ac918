# Three-Tier Hierarchy Implementation

## Overview

Successfully implemented a dynamic three-tier hierarchical learning system with a **database-like normalized structure** using foreign key relationships, proper development/production mode switching, and lazy loading capabilities.

## Architecture

### 1. Database-Like Data Structure
The system now uses a **normalized relational approach** instead of nested objects:

#### **Subjects Table** (`SUBJECTS_TABLE`)
- Contains only subject metadata (no nested topics)
- Primary key: `id`
- Fields: name, description, category, order, icon, color, etc.

#### **Topics Table** (`TOPICS_TABLE`)
- Contains topic metadata with foreign key reference to subjects
- Primary key: `id`
- Foreign key: `subjectId` → references `SUBJECTS_TABLE.id`
- Fields: name, description, order, difficulty, prerequisites, etc.

#### **Skills Table** (`SKILLS_TABLE`)
- Contains skill metadata with foreign key reference to topics
- Primary key: `id`
- Foreign key: `topicId` → references `TOPICS_TABLE.id`
- Fields: name, description, order, difficulty, fileName, tags, etc.

### 2. Dynamic Data Service (`HierarchyDataService`)
- **Development Mode**: Uses normalized dummy data with database-like queries
- **Production Mode**: Makes API calls with fallback to development data
- **Environment Detection**: Automatically switches based on `__DEV__` flag
- **Lazy Loading**: Each hierarchy level loads data only when accessed
- **Query Functions**: Database-like query methods for relationships

### 3. Hierarchy Structure
```
Subject (Top Level - SUBJECTS_TABLE)
├── Topic (Middle Level - TOPICS_TABLE, subjectId FK)
│   ├── Skill (Bottom Level - SKILLS_TABLE, topicId FK)
│   │   └── Skill Content (Full JSON - Loaded on demand)
```

### 3. Key Components

#### Data Layer
- `HierarchyDataService`: Dynamic data loading with dev/prod modes
- `HierarchyService`: Progress tracking and business logic
- `ProgressService`: Hierarchical progress management
- `NavigationStore`: State management for navigation

#### UI Components
- `SubjectListScreen`: Displays available subjects with progress
- `TopicListScreen`: Shows topics within a subject
- `SkillListScreen`: Lists skills within a topic (metadata only)
- `LearnScreen`: Main navigation coordinator

#### Navigation
- `learn.tsx`: New tab for hierarchical navigation
- Updated tab layout to include Learn tab
- Breadcrumb navigation with back button support

## Database-Like Data Structure

### Subjects Table (`SUBJECTS_TABLE`)
```typescript
interface SubjectRecord {
  id: UUID;
  name: string;
  description: string;
  category: string;
  order: number;
  icon: string;
  color: string;
  estimatedDuration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  isPremium: boolean;
}
```

**Sample Data:**
- AI Fundamentals (Free, Technology)
- Productivity & Focus (Free, Personal Development)
- Machine Learning (Premium, Technology)
- Data Science (Premium, Technology)

### Topics Table (`TOPICS_TABLE`)
```typescript
interface TopicRecord {
  id: UUID;
  name: string;
  description: string;
  subjectId: UUID; // Foreign key to SUBJECTS_TABLE
  order: number;
  estimatedDuration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites?: UUID[]; // Array of topic IDs
}
```

**Relationships:**
- `topic-ai-basics` → `subject-ai-fundamentals`
- `topic-ai-tools` → `subject-ai-fundamentals` (requires `topic-ai-basics`)
- `topic-productivity-basics` → `subject-productivity`

### Skills Table (`SKILLS_TABLE`)
```typescript
interface SkillRecord {
  id: UUID;
  name: string;
  description: string;
  topicId: UUID; // Foreign key to TOPICS_TABLE
  order: number;
  estimatedDuration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  fileName: string; // Reference to JSON file
  tags?: string[];
  isActive?: boolean;
}
```

**Sample Data:**
- `skill-dummy-productivity-002` → `topic-productivity-basics` → `dummySkill.json`
- `skill-better-001` → `topic-ai-basics` → `better.json`
- `skill-calculus-001` → `topic-time-management` → `calculous.json`

## Key Features

### 1. Dynamic Loading
- **Subjects**: Loaded on app start
- **Topics**: Loaded when subject is selected
- **Skills**: Loaded when topic is selected
- **Skill Content**: Loaded when entering SkillPlayer

### 2. Development Mode
- Uses static dummy data for seamless development
- No database/API setup required
- Easy to add new content by updating data files

### 3. Production Mode
- Makes API calls to backend
- Graceful fallback to development data on failure
- Proper error handling and loading states

### 4. Database-Like Query System
- **Relationship Queries**: Get topics by subject ID, skills by topic ID
- **Search Functionality**: Search skills/topics by name, description, tags
- **Filtering**: Filter by difficulty, category, tags
- **Prerequisites**: Check topic/skill unlock status based on completion
- **Caching**: Intelligent caching for performance

### 5. Progress Tracking
- Hierarchical progress calculation using foreign key relationships
- Subject progress based on topic completion
- Topic progress based on skill completion
- Skill progress based on exercise completion

### 6. Educational UX
- Maintained all existing patterns (CHECK/CONTINUE buttons)
- Progress bars at all levels
- Completion badges and visual feedback
- Proper loading states and error handling

## File Structure

```
src/
├── features/hierarchy/
│   ├── components/
│   │   ├── SubjectListScreen.tsx
│   │   ├── TopicListScreen.tsx
│   │   └── SkillListScreen.tsx
│   ├── services/
│   │   ├── HierarchyDataService.ts
│   │   └── ProgressService.ts
│   ├── stores/
│   │   └── NavigationStore.ts
│   ├── test/
│   │   └── HierarchyTest.tsx
│   └── HierarchyService.ts
├── fixtures/
│   └── devHierarchyData.ts
├── app/(app)/(authenticated)/(tabs)/
│   └── learn.tsx
└── types/skill.ts (updated with new interfaces)
```

## Testing

### 1. Manual Testing
Navigate to the Learn tab and test:
- Subject selection → Topic list
- Topic selection → Skill list  
- Skill selection → SkillPlayer
- Back navigation at each level
- Progress tracking across levels

### 2. Development Testing
Use the test component at `src/features/hierarchy/test/HierarchyTest.tsx`:
- Shows data loading at each level
- Displays breadcrumb navigation
- Tests skill content loading
- Console logging for debugging

### 3. API Integration Testing
For production mode:
- Set up API endpoints matching the service interface
- Test fallback behavior when API is unavailable
- Verify proper error handling

## Database Query Functions

The new system includes comprehensive query functions that work like database operations:

### Basic Queries
```typescript
// Get all subjects (ordered)
DatabaseQueries.getAllSubjects(): SubjectRecord[]

// Get subject by ID
DatabaseQueries.getSubjectById(id: UUID): SubjectRecord | null

// Get topics for a subject (with foreign key relationship)
DatabaseQueries.getTopicsBySubjectId(subjectId: UUID): TopicRecord[]

// Get skills for a topic (with foreign key relationship)
DatabaseQueries.getSkillsByTopicId(topicId: UUID): SkillRecord[]
```

### Advanced Queries
```typescript
// Search functionality
DatabaseQueries.searchSkills(query: string): SkillRecord[]
DatabaseQueries.searchTopics(query: string): TopicRecord[]

// Filter by attributes
DatabaseQueries.getSkillsByTag(tag: string): SkillRecord[]
DatabaseQueries.getSkillsByDifficulty(difficulty: string): SkillRecord[]
DatabaseQueries.getSubjectsByCategory(category: string): SubjectRecord[]

// Relationship queries
DatabaseQueries.getSubjectWithTopics(subjectId: UUID): {subject, topics}
DatabaseQueries.getTopicWithSkills(topicId: UUID): {topic, skills}
```

## API Endpoints (Production Mode)

The production service expects these endpoints:

```
GET /api/subjects
GET /api/subjects/{subjectId}/topics
GET /api/topics/{topicId}/skills
GET /api/skills/{skillId}/content
GET /api/topics/{topicId}
GET /api/skills/{skillId}/metadata

# New search endpoints
GET /api/skills/search?q={query}
GET /api/topics/search?q={query}
GET /api/skills/by-tag/{tag}
```

## Benefits of Database-Like Structure

### 1. **Normalized Data Architecture**
- **No Data Duplication**: Each entity exists once with foreign key references
- **Referential Integrity**: Clear relationships between subjects, topics, and skills
- **Easy Updates**: Modify a subject/topic without affecting nested data
- **Consistent Structure**: Mirrors real database design patterns

### 2. **Scalability & Performance**
- **Efficient Queries**: Database-like query functions for fast data retrieval
- **Lazy Loading**: Load only needed data at each hierarchy level
- **Caching Strategy**: Cache individual tables instead of nested structures
- **Search Optimization**: Indexed search across normalized tables

### 3. **Development Experience**
- **Database Transition**: Seamless migration to real database in production
- **Query Flexibility**: Rich query API (search, filter, relationships)
- **Type Safety**: Strongly typed interfaces for all table records
- **Easy Testing**: Simple to mock individual tables for unit tests

### 4. **Maintainability**
- **Separation of Concerns**: Each table manages its own data
- **Relationship Management**: Clear foreign key relationships
- **Schema Evolution**: Easy to add new fields without breaking existing code
- **Data Integrity**: Validation at the table level

### 5. **Production Readiness**
- **API Mapping**: Direct mapping to REST API endpoints
- **Database Schema**: Ready for SQL/NoSQL database implementation
- **Microservices**: Each table can become its own service
- **Caching Layers**: Redis/Memcached integration ready

## Next Steps

1. **Content Management**: Add more subjects, topics, and skills to development data
2. **API Implementation**: Build backend API matching the service interface
3. **Caching Strategy**: Implement more sophisticated caching for production
4. **Analytics**: Add tracking for user navigation patterns
5. **Search**: Implement search across the hierarchy
6. **Recommendations**: Add personalized content recommendations

## Migration Notes

- Existing SkillPlayer functionality is preserved
- Progress tracking is backward compatible
- All educational UX patterns maintained
- Offline-first architecture for free users intact
