import React from 'react';
import { View, ScrollView, TouchableOpacity, Image, StyleSheet, ColorValue } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { ChivoText, HeeboText } from '@/components/ui/CustomText';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { useTheme, useThemedStyles } from '~/lib/theme';
import { getThemedShadow, colorUtils } from '~/lib/themeUtils';
import { ThemeToggle } from '~/components/ui/ThemeToggle';

// Theme-aware data - will be moved inside component to access theme colors
const getThemeAwareUserStats = (colors: any) => [
  { value: '14', label: 'Day Streak', icon: 'flame' as const, color: [colors.warning[400], colors.warning[600]], size: 'large' },
  { value: '256', label: 'Words Learned', icon: 'book' as const, color: [colors.primary[400], colors.primary[600]], size: 'small' },
  { value: '87%', label: 'Mastery', icon: 'trophy' as const, color: [colors.secondary[400], colors.secondary[600]], size: 'small' },
];

const getThemeAwareAchievements = (colors: any) => [
  { id: 1, name: 'First Steps', icon: 'footsteps', unlocked: true, gradient: [colors.primary[400], colors.primary[600]] },
  { id: 2, name: 'Word Collector', icon: 'briefcase', unlocked: true, gradient: [colors.warning[400], colors.warning[600]] },
  { id: 3, name: 'Perfect Lesson', icon: 'sparkles', unlocked: true, gradient: [colors.success[400], colors.success[600]] },
  { id: 4, name: 'Streak Starter', icon: 'rocket', unlocked: true, gradient: [colors.secondary[400], colors.secondary[600]] },
  { id: 5, name: 'Weekend Warrior', icon: 'calendar', unlocked: false },
  { id: 6, name: 'Night Owl', icon: 'moon', unlocked: false },
  { id: 7, name: 'Early Bird', icon: 'sunny', unlocked: false },
  { id: 8, name: 'Legendary', icon: 'medal', unlocked: false },
];

export default function Profile2Screen() {
  const { theme, colors, isDarkMode } = useTheme();

  // Get theme-aware data
  const USER_STATS = getThemeAwareUserStats(colors);
  const ACHIEVEMENTS = getThemeAwareAchievements(colors);

  // Create theme-aware styles
  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    backgroundGradient: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
    },
    settingsButton: {
      backgroundColor: colorUtils.addAlpha(theme.card, 0.8),
      ...getThemedShadow(theme, 'sm'),
    },
    profileImageContainer: {
      backgroundColor: theme.card,
      ...getThemedShadow(theme, 'lg'),
    },
    profileImage: {
      borderWidth: 3,
      borderColor: theme.card,
    },
    badgeContainer: {
      ...getThemedShadow(theme, 'sm'),
    },
    statCard: {
      ...getThemedShadow(theme, 'lg'),
    },
    achievementBadge: {
      width: 76,
      height: 76,
      borderRadius: 38,
      alignItems: 'center',
      justifyContent: 'center',
      ...getThemedShadow(theme, 'md'),
    },
    lockedBadge: {
      backgroundColor: isDark ? colors.gray[800] : colors.gray[100],
      ...getThemedShadow(theme, 'sm'),
    },
    statsContainer: {
      backgroundColor: theme.card,
      ...getThemedShadow(theme, 'md'),
    },
  }));

  return (
    <SafeAreaView edges={['top']} style={styles.container}>
      <LinearGradient
        colors={isDarkMode
          ? [colors.gray[900], colors.gray[800]]
          : [colors.gray[50], colors.gray[100]]
        }
        style={styles.backgroundGradient}
      />
      
      {/* --- SETTINGS ICON --- */}
      <TouchableOpacity
        className="absolute top-16 right-5 z-10 p-2 rounded-full"
        style={styles.settingsButton}
      >
        <Ionicons name="settings-outline" size={24} color={theme.secondaryText} />
      </TouchableOpacity>
        
      <ScrollView 
        showsVerticalScrollIndicator={false} 
        contentContainerStyle={{ paddingBottom: 40 }}
      >
        
        {/* --- HEADER --- */}
        <Animated.View 
          className="items-center pt-14 pb-8"
          entering={FadeInDown.duration(600).delay(100)}
        >
          <View className="w-32 h-32 rounded-full p-1.5" style={styles.profileImageContainer}>
            <Image
              source={require('../../../../assets/images/wordmark.png')} // Replace with mascot
              className="w-full h-full rounded-full"
              style={styles.profileImage}
            />
            <View
              className="absolute bottom-0 right-0 w-6 h-6 rounded-full border-2 items-center justify-center"
              style={{
                backgroundColor: colors.success[500],
                borderColor: theme.card
              }}
            >
              <View
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: theme.card }}
              />
            </View>
          </View>
          <HeeboText
            fontWeight="BOLD"
            className="text-3xl mt-5"
            style={{ color: theme.text }}
          >
            Riyan
          </HeeboText>
          <View className="rounded-full px-4 py-1.5 mt-3" style={styles.badgeContainer}>
            <LinearGradient
              colors={[
                colorUtils.addAlpha(colors.warning[500], 0.2),
                colorUtils.addAlpha(colors.warning[600], 0.2)
              ]}
              style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0, borderRadius: 9999 }}
            />
            <ChivoText
              className="text-sm font-semibold"
              style={{ color: colors.warning[600] }}
            >
              Word Explorer
            </ChivoText>
          </View>
        </Animated.View>

        {/* --- STATS CAROUSEL --- */}
        <Animated.View entering={FadeInDown.duration(600).delay(200)}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false} 
            contentContainerStyle={{ paddingHorizontal: 20, paddingVertical: 10, paddingRight: 40, alignItems: 'center' }}
            decelerationRate="fast"
            snapToInterval={200}
          >
            {USER_STATS.map((stat, index) => (
              <View 
                key={stat.label} 
                className={`rounded-3xl p-5 mr-5 ${stat.size === 'large' ? 'w-52 h-60' : 'w-44 h-52'}`} 
                style={[
                  styles.statCard,
                  { marginLeft: index === 0 ? 0 : 5 }
                ]}
              >
                <LinearGradient
                  colors={stat.color}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0, borderRadius: 24 }}
                />
                <View className="flex-1 justify-between">
                  <Ionicons
                    name={stat.icon}
                    size={stat.size === 'large' ? 110 : 90}
                    color={colors.white}
                    style={{ position: 'absolute', top: -20, right: -25, opacity: 0.2 }}
                  />
                  <View/>
                  <View>
                    <HeeboText
                      fontWeight="EXTRA_BOLD"
                      className={stat.size === 'large' ? 'text-6xl' : 'text-5xl'}
                      style={{ color: colors.white }}
                    >
                      {stat.value}
                    </HeeboText>
                    <ChivoText
                      className="text-base mt-1 tracking-wide"
                      style={{ color: colorUtils.addAlpha(colors.white, 0.9) }}
                    >
                      {stat.label}
                    </ChivoText>
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>
        </Animated.View>

        {/* --- ACHIEVEMENTS SHOWCASE --- */}
        <Animated.View 
          className="mt-10 mx-5"
          entering={FadeInDown.duration(600).delay(300)}
        >
          <View className="flex-row items-center justify-between mb-6">
            <HeeboText
              fontWeight="BOLD"
              className="text-2xl"
              style={{ color: theme.text }}
            >
              Achievements
            </HeeboText>
            <TouchableOpacity className="flex-row items-center">
              <ChivoText
                className="text-sm mr-1"
                style={{ color: colors.primary[500] }}
              >
                View All
              </ChivoText>
              <Ionicons name="chevron-forward" size={16} color={colors.primary[500]} />
            </TouchableOpacity>
          </View>
          
          <View className="flex-row flex-wrap justify-start -mx-2">
            {ACHIEVEMENTS.map((badge, index) => (
              <View 
                key={badge.id} 
                className="items-center mb-6 w-1/4 px-2"
                style={{ transform: [{ translateY: index % 2 !== 0 ? 20 : 0 }] }}
              >
                <View className="relative">
                  {badge.unlocked ? (
                    <View style={styles.achievementBadge}>
                      <LinearGradient
                        colors={badge.gradient as string[]}
                        style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0, borderRadius: 9999, width: 76, height: 76 }}
                      />
                      <Ionicons name={badge.icon as any} size={34} color={colors.white} style={{ zIndex: 1 }} />
                    </View>
                  ) : (
                    <View
                      className="w-[76px] h-[76px] rounded-full items-center justify-center"
                      style={styles.lockedBadge}
                    >
                      <Ionicons name="lock-closed" size={28} color={colors.gray[400]} />
                      <View
                        className="absolute inset-0 rounded-full"
                        style={{ backgroundColor: colorUtils.addAlpha(colors.black, 0.05) }}
                      />
                    </View>
                  )}

                  {badge.unlocked && (
                    <View
                      className="absolute -top-1 -right-1 w-5 h-5 rounded-full border-2 items-center justify-center"
                      style={{
                        backgroundColor: colors.warning[400],
                        borderColor: theme.card
                      }}
                    >
                      <Ionicons name="checkmark" size={12} color={colors.white} />
                    </View>
                  )}
                </View>
                <ChivoText
                  className="text-xs text-center mt-3 font-medium"
                  numberOfLines={2}
                  style={{ color: theme.secondaryText }}
                >
                  {badge.name}
                </ChivoText>
              </View>
            ))}
          </View>
        </Animated.View>
        
        {/* --- LEARNING STATS --- */}
        <Animated.View
          className="mt-6 mx-5 rounded-3xl p-5"
          style={styles.statsContainer}
          entering={FadeInDown.duration(600).delay(400)}
        >
          <HeeboText
            fontWeight="BOLD"
            className="text-xl mb-4"
            style={{ color: theme.text }}
          >
            Learning Stats
          </HeeboText>

          <View className="flex-row justify-between items-center mb-4">
            <View className="flex-row items-center">
              <View
                className="w-10 h-10 rounded-full items-center justify-center mr-3"
                style={{ backgroundColor: colors.primary[100] }}
              >
                <Ionicons name="time-outline" size={20} color={colors.primary[600]} />
              </View>
              <ChivoText style={{ color: theme.secondaryText }}>Time Spent</ChivoText>
            </View>
            <HeeboText
              fontWeight="MEDIUM"
              className="text-lg"
              style={{ color: theme.text }}
            >
              14.5 hours
            </HeeboText>
          </View>

          <View className="flex-row justify-between items-center mb-4">
            <View className="flex-row items-center">
              <View
                className="w-10 h-10 rounded-full items-center justify-center mr-3"
                style={{ backgroundColor: colors.success[100] }}
              >
                <Ionicons name="checkmark-circle-outline" size={20} color={colors.success[600]} />
              </View>
              <ChivoText style={{ color: theme.secondaryText }}>Lessons Completed</ChivoText>
            </View>
            <HeeboText
              fontWeight="MEDIUM"
              className="text-lg"
              style={{ color: theme.text }}
            >
              42
            </HeeboText>
          </View>

          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center">
              <View
                className="w-10 h-10 rounded-full items-center justify-center mr-3"
                style={{ backgroundColor: colors.secondary[100] }}
              >
                <Ionicons name="star-outline" size={20} color={colors.secondary[600]} />
              </View>
              <ChivoText style={{ color: theme.secondaryText }}>Total XP</ChivoText>
            </View>
            <HeeboText
              fontWeight="MEDIUM"
              className="text-lg"
              style={{ color: theme.text }}
            >
              3,450
            </HeeboText>
          </View>
        </Animated.View>

        {/* --- THEME SETTINGS --- */}
        <Animated.View
          className="mt-6 mx-5 rounded-3xl p-5"
          style={styles.statsContainer}
          entering={FadeInDown.duration(600).delay(500)}
        >
          <HeeboText
            fontWeight="BOLD"
            className="text-xl mb-4"
            style={{ color: theme.text }}
          >
            Appearance
          </HeeboText>

          <View className="flex-row justify-between items-center">
            <View>
              <ChivoText
                className="text-base font-medium"
                style={{ color: theme.text }}
              >
                Theme
              </ChivoText>
              <ChivoText
                className="text-sm mt-1"
                style={{ color: theme.secondaryText }}
              >
                Choose your preferred appearance
              </ChivoText>
            </View>
            <ThemeToggle size="sm" />
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

// Styles are now created inside the component using useThemedStyles