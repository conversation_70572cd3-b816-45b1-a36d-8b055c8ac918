import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { OptionCard } from './OptionCard';

export const MultipleChoiceSlide = ({ slide, answer, setAnswer, theme }: { slide: any, answer: any, setAnswer: any, theme: any }) => (
  <View style={{ flex: 1, width: '100%' }}>
    <Text style={[styles.question, { color: theme.text }]}>{slide.question}</Text>
    {slide.description && (
      <Text style={[styles.subDescription, { color: theme.secondaryText }]}>{slide.description}</Text>
    )}
    <View style={{ marginTop: 40 }}>
      {Array.isArray(slide.options) && slide.options.map((option: any) => (
        <OptionCard
          key={option.id}
          text={option.text}
          selected={answer === option.id}
          onPress={() => setAnswer(option.id)}
          color={theme.primaryButton}
        />
      ))}
    </View>
  </View>
);

const styles = StyleSheet.create({
    question: {
        fontSize: 26,
        fontWeight: 'bold',
        color: '#555555',
        marginBottom: 8,
        marginTop: 12,
    },
    subDescription: {
        fontSize: 16,
        color: '#555555',
        marginBottom: 16,
    },
});
