import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface RankingOption {
  id: string;
  text: string;
}

interface RankingSlideProps {
  question: string;
  options: RankingOption[];
  value: string[];
  onChange: (v: string[]) => void;
  color: string;
}

export function RankingSlide({ question, options, value, onChange, color }: RankingSlideProps) {
  // Defensive: Ensure value is an array
  if (!Array.isArray(value)) {
    console.warn('RankingSlide: value is not an array', value);
    return (
      <View style={{ width: '100%' }}>
        <Text style={styles.question}>{question}</Text>
        <Text style={{ color: 'red', marginTop: 16 }}>Ranking data is missing or invalid.</Text>
      </View>
    );
  }
  // value is an array of option ids in order
  const move = (from: number, to: number) => {
    if (to < 0 || to >= value.length) return;
    const newOrder = [...value];
    const [moved] = newOrder.splice(from, 1);
    newOrder.splice(to, 0, moved);
    onChange(newOrder);
  };
  return (
    <View style={{ width: '100%' }}>
      <Text style={styles.question}>{question}</Text>
      {value.map((id, idx) => {
        const opt = options.find(o => o.id === id);
        if (!opt) return null;
        return (
          <View key={id} style={styles.row}>
            <Text style={styles.text}>{opt.text}</Text>
            <View style={styles.btns}>
              <TouchableOpacity onPress={() => move(idx, idx - 1)} disabled={idx === 0} style={[styles.arrowBtn, idx === 0 && { opacity: 0.3 }]}>
                <Text style={{ fontSize: 18, color }}>↑</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => move(idx, idx + 1)} disabled={idx === value.length - 1} style={[styles.arrowBtn, idx === value.length - 1 && { opacity: 0.3 }]}>
                <Text style={{ fontSize: 18, color }}>↓</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  question: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  text: {
    fontSize: 18,
    color: '#222',
  },
  btns: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrowBtn: {
    marginHorizontal: 4,
    padding: 4,
  },
}); 