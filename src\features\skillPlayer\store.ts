import { create } from "zustand";
import { Skill, UUID, Exercise, SkillProgress } from "@/types/skill";
import { localStorageService, LocalUserProgress } from "@/features/freemium/services/LocalStorageService";
import { router } from "expo-router";
import { progressService } from "@/features/hierarchy/services/ProgressService";
import { hierarchyDataService } from "@/features/hierarchy/services/HierarchyDataService";

// Development import – load dummy JSON if Convex not set up yet
declare const __DEV__: boolean;
// expo global flag

export type AnswerFeedback = {
  isCorrect: boolean;
  showFeedback: boolean;
};

export type ButtonState = 'check' | 'continue';

export type CompletionType = 'lesson' | 'level' | 'skill';

export type LessonStats = {
  totalExercises: number;
  correctAnswers: number;
  startTime: number;
  endTime: number;
};

interface SkillState {
  skill?: Skill | null;
  lessonIndex: number;
  exerciseIndex: number;
  answers: Record<UUID, unknown>;
  answerFeedback: Record<UUID, AnswerFeedback>;
  buttonStates: Record<UUID, ButtonState>;
  lives: number;
  showCompletion: CompletionType | null;
  currentLessonStats: LessonStats | null;
  currentStreak: number;
  userId?: string;
  shouldSyncToCloud: boolean;
  isLoading?: boolean;
  error?: string | null;
  currentExercise?: Exercise | null;
  loadSkill: (id: string, userId?: string, shouldSync?: boolean) => Promise<void>;
  setSkill: (skill: Skill) => void;
  answer: (exerciseId: UUID, value: unknown) => void;
  validateAnswer: (exerciseId: UUID) => void;
  clearFeedback: (exerciseId: UUID) => void;
  next: () => void;
  startNextLesson: () => void;
  reviewCurrentLesson: () => void;
  goToHome: () => void;
  dismissCompletion: () => void;
  updateStreak: (isCorrect: boolean) => void;
  resetStreak: () => void;
  saveProgressLocally: () => Promise<void>;
  loadProgressFromLocal: () => Promise<void>;
  syncToCloudIfNeeded: () => Promise<void>;
}

// Helper function to validate answers based on exercise type
const validateExerciseAnswer = (exercise: Exercise, userAnswer: unknown): boolean => {
  switch (exercise.type) {
    case "single-choice":
      return userAnswer === exercise.payload.answerIndex;
    case "multi-choice":
      if (!Array.isArray(userAnswer)) return false;
      const correctIndices = exercise.payload.answerIndices;
      return userAnswer.length === correctIndices.length &&
             userAnswer.every(idx => correctIndices.includes(idx));
    case "true-false":
      return userAnswer === exercise.payload.answer;
    case "fill-blank":
      if (typeof userAnswer !== "string") return false;
      return userAnswer.toLowerCase().trim() === exercise.payload.answer.toLowerCase().trim();
    case "drag-order":
      if (!Array.isArray(userAnswer)) return false;
      const correctOrder = exercise.payload.correctOrderIndices;
      return userAnswer.length === correctOrder.length &&
             userAnswer.every((idx, pos) => idx === correctOrder[pos]);
    case "matching-pairs":
      // For matching pairs, userAnswer should be an array of [leftIndex, rightIndex] pairs
      if (!Array.isArray(userAnswer)) return false;
      const correctPairs = exercise.payload.pairs;
      return userAnswer.length === correctPairs.length &&
             userAnswer.every(pair =>
               correctPairs.some(correctPair =>
                 pair[0] === correctPair[0] && pair[1] === correctPair[1]
               )
             );
    case "text-info":
      return true; // Text info exercises are always "correct"
    default:
      return false;
  }
};

export const useSkillStore = create<SkillState>((set, get) => ({
  lessonIndex: 0,
  exerciseIndex: 0,
  answers: {},
  answerFeedback: {},
  buttonStates: {},
  lives: 3,
  showCompletion: null,
  currentLessonStats: null,
  currentStreak: 0,
  userId: undefined,
  shouldSyncToCloud: false,

  async loadSkill(id: string, userId?: string, shouldSync = false) {
    console.log('[loadSkill] Starting to load skill with ID:', id);

    set({ userId, shouldSyncToCloud: shouldSync, isLoading: true, error: null });

    try {
      // Load skill content using the dynamic data service
      const skillJson = await hierarchyDataService.getSkillContent(id);

      if (!skillJson) {
        console.error('[loadSkill] Skill not found:', id);
        set({
          skill: null,
          lessonIndex: 0,
          exerciseIndex: 0,
          currentExercise: null,
          isLoading: false,
          error: `Skill with ID "${id}" not found`
        });
        return;
      }

      console.log('[loadSkill] Successfully loaded skill:', skillJson.name);
      console.log('[loadSkill] Skill has levels:', skillJson.levels?.length || 0);
      console.log('[loadSkill] First level has lessons:', skillJson.levels?.[0]?.lessons?.length || 0);

      if (!skillJson.levels || skillJson.levels.length === 0) {
        console.error('[loadSkill] Skill has no levels');
        set({
          skill: null,
          lessonIndex: 0,
          exerciseIndex: 0,
          currentExercise: null,
          isLoading: false,
          error: 'Skill has no content'
        });
        return;
      }

      if (!skillJson.levels[0].lessons || skillJson.levels[0].lessons.length === 0) {
        console.error('[loadSkill] Skill has no lessons');
        set({
          skill: null,
          lessonIndex: 0,
          exerciseIndex: 0,
          currentExercise: null,
          isLoading: false,
          error: 'Skill has no lessons'
        });
        return;
      }

      // Load existing progress if user is provided
      let savedProgress: SkillProgress | null = null;
      if (userId) {
        try {
          savedProgress = await progressService.getSkillProgress(id, userId);
        } catch (error) {
          console.warn('Failed to load saved progress:', error);
        }
      }

      const startingLessonIndex = savedProgress?.lessonIndex || 0;
      const currentLesson = skillJson.levels[0].lessons[startingLessonIndex];
      const firstExercise = currentLesson.exercises[0];
      const initialButtonState = firstExercise.type === 'text-info' ? 'continue' : 'check';

      set({
        skill: skillJson,
        lessonIndex: startingLessonIndex,
        exerciseIndex: 0,
        answers: {},
        answerFeedback: {},
        buttonStates: { [firstExercise.id]: initialButtonState },
        showCompletion: null,
        isLoading: false,
        error: null,
        currentLessonStats: {
          totalExercises: currentLesson.exercises.length,
          correctAnswers: 0,
          startTime: Date.now(),
          endTime: 0
        }
      });
    } catch (error) {
      console.error('[loadSkill] Error loading skill:', error);
      set({
        skill: null,
        lessonIndex: 0,
        exerciseIndex: 0,
        currentExercise: null,
        isLoading: false,
        error: 'Failed to load skill content'
      });
    }
  },

  setSkill(skill) {
    const firstExercise = skill.levels[0].lessons[0].exercises[0];
    const initialButtonState = firstExercise.type === 'text-info' ? 'continue' : 'check';

    set({
      skill,
      lessonIndex: 0,
      exerciseIndex: 0,
      answers: {},
      answerFeedback: {},
      buttonStates: { [firstExercise.id]: initialButtonState },
      showCompletion: null,
      currentLessonStats: {
        totalExercises: skill.levels[0].lessons[0].exercises.length,
        correctAnswers: 0,
        startTime: Date.now(),
        endTime: 0
      }
    });
  },

  answer(exerciseId, value) {
    set(state => ({
      answers: { ...state.answers, [exerciseId]: value },
      buttonStates: { ...state.buttonStates, [exerciseId]: 'check' }
    }));
    // TODO: send to backend & validate.
  },

  validateAnswer(exerciseId) {
    const { skill, lessonIndex, exerciseIndex, answers, currentLessonStats } = get();
    if (!skill) return;

    const lesson = skill.levels[0].lessons[lessonIndex];
    const exercise = lesson.exercises[exerciseIndex];

    if (exercise.id !== exerciseId) return;

    const userAnswer = answers[exerciseId];
    const isCorrect = validateExerciseAnswer(exercise, userAnswer);

    set(state => ({
      answerFeedback: {
        ...state.answerFeedback,
        [exerciseId]: { isCorrect, showFeedback: true }
      },
      buttonStates: {
        ...state.buttonStates,
        [exerciseId]: 'continue'
      },
      currentLessonStats: currentLessonStats ? {
        ...currentLessonStats,
        correctAnswers: currentLessonStats.correctAnswers + (isCorrect ? 1 : 0)
      } : null
    }));
  },

  clearFeedback(exerciseId) {
    set(state => ({
      answerFeedback: {
        ...state.answerFeedback,
        [exerciseId]: { ...state.answerFeedback[exerciseId], showFeedback: false }
      }
    }));
  },

  next() {
    const { skill, lessonIndex, exerciseIndex, currentLessonStats } = get();
    if (!skill) return;

    const currentLevel = skill.levels[0]; // Currently only supporting first level
    const lesson = currentLevel.lessons[lessonIndex];

    if (exerciseIndex < lesson.exercises.length - 1) {
      // Move to next exercise in current lesson
      const nextExerciseIndex = exerciseIndex + 1;
      const nextExercise = lesson.exercises[nextExerciseIndex];

      set(state => ({
        exerciseIndex: nextExerciseIndex,
        // Set initial button state for next exercise
        buttonStates: {
          ...state.buttonStates,
          [nextExercise.id]: nextExercise.type === 'text-info' ? 'continue' : 'check'
        }
      }));
    } else {
      // Lesson completed - determine what type of completion
      const isLastLessonInLevel = lessonIndex >= currentLevel.lessons.length - 1;
      const isLastLevel = true; // Currently only supporting first level

      let completionType: CompletionType;
      if (isLastLessonInLevel && isLastLevel) {
        completionType = 'skill';
      } else if (isLastLessonInLevel) {
        completionType = 'level';
      } else {
        completionType = 'lesson';
      }

      // Update lesson stats with end time
      const updatedStats = currentLessonStats ? {
        ...currentLessonStats,
        endTime: Date.now()
      } : null;

      set({
        showCompletion: completionType,
        currentLessonStats: updatedStats
      });
    }
  },

  startNextLesson() {
    const { skill, lessonIndex } = get();
    if (!skill) return;

    const currentLevel = skill.levels[0]; // Currently only supporting first level
    const nextLessonIndex = lessonIndex + 1;

    if (nextLessonIndex < currentLevel.lessons.length) {
      const nextLesson = currentLevel.lessons[nextLessonIndex];
      const firstExercise = nextLesson.exercises[0];
      const initialButtonState = firstExercise.type === 'text-info' ? 'continue' : 'check';

      set(state => ({
        lessonIndex: nextLessonIndex,
        exerciseIndex: 0,
        showCompletion: null,
        buttonStates: {
          ...state.buttonStates,
          [firstExercise.id]: initialButtonState
        },
        currentLessonStats: {
          totalExercises: nextLesson.exercises.length,
          correctAnswers: 0,
          startTime: Date.now(),
          endTime: 0
        }
      }));
    }
  },

  reviewCurrentLesson() {
    const { skill, lessonIndex } = get();
    if (!skill) return;

    const currentLesson = skill.levels[0].lessons[lessonIndex];
    const firstExercise = currentLesson.exercises[0];
    const initialButtonState = firstExercise.type === 'text-info' ? 'continue' : 'check';

    set(() => ({
      exerciseIndex: 0,
      showCompletion: null,
      answers: {}, // Clear previous answers for review
      answerFeedback: {},
      buttonStates: { [firstExercise.id]: initialButtonState },
      currentLessonStats: {
        totalExercises: currentLesson.exercises.length,
        correctAnswers: 0,
        startTime: Date.now(),
        endTime: 0
      }
    }));
  },

  goToHome() {
    // This will be handled by the component that uses this store
    // by navigating back to the home screen
    set({ showCompletion: null });
  },

  dismissCompletion() {
    set({ showCompletion: null });
  },

  updateStreak(isCorrect: boolean) {
    set(state => ({
      currentStreak: isCorrect ? state.currentStreak + 1 : 0
    }));
  },

  resetStreak() {
    set({ currentStreak: 0 });
  },

  async saveProgressLocally() {
    const { skill, lessonIndex, exerciseIndex, userId, currentLessonStats, answers } = get();
    if (!skill || !userId) return;

    try {
      // Calculate total exercises completed across all lessons
      let totalCompletedExercises: string[] = [];
      for (let i = 0; i < skill.levels[0].lessons.length; i++) {
        const lesson = skill.levels[0].lessons[i];
        if (i < lessonIndex) {
          // Previous lessons are fully completed
          totalCompletedExercises.push(...lesson.exercises.map(ex => ex.id));
        } else if (i === lessonIndex) {
          // Current lesson - add completed exercises
          totalCompletedExercises.push(...lesson.exercises.slice(0, exerciseIndex).map(ex => ex.id));
        }
      }

      // Check if entire skill is completed
      const totalExercises = skill.levels[0].lessons.reduce((total, lesson) => total + lesson.exercises.length, 0);
      const isSkillCompleted = totalCompletedExercises.length >= totalExercises;

      // Calculate score
      const correctAnswers = currentLessonStats?.correctAnswers || 0;
      const totalQuestions = currentLessonStats?.totalExercises || 1;
      const score = Math.round((correctAnswers / totalQuestions) * 100);

      // Update skill progress using the new progress service
      await progressService.updateSkillProgress(skill.id, {
        lessonIndex,
        exerciseIndex,
        completedExercises: totalCompletedExercises,
        answers,
        isCompleted: isSkillCompleted,
        completionDate: isSkillCompleted ? new Date().toISOString() : undefined,
        score,
        timeSpent: currentLessonStats ? Date.now() - currentLessonStats.startTime : 0,
      }, userId);

    } catch (error) {
      console.error('Failed to save progress locally:', error);
    }
  },

  async loadProgressFromLocal() {
    const { skill, userId } = get();
    if (!skill || !userId) return;

    try {
      const userProgress = await localStorageService.getUserProgress(userId);
      const skillProgress = userProgress?.skillProgress[skill.id];

      if (skillProgress) {
        set({
          lessonIndex: skillProgress.currentLessonIndex,
        });
      }
    } catch (error) {
      console.error('Failed to load progress from local:', error);
    }
  },

  async syncToCloudIfNeeded() {
    const { shouldSyncToCloud, userId, skill } = get();
    if (!shouldSyncToCloud || !userId || !skill) return;

    try {
      // Here you would implement cloud sync logic
      // For now, we'll just log that sync would happen
      console.log('Syncing progress to cloud for user:', userId, 'skill:', skill.id);

      // TODO: Implement actual cloud sync using Convex or your backend
      // This could involve:
      // 1. Getting local progress data
      // 2. Sending it to your backend API
      // 3. Handling conflicts if needed

    } catch (error) {
      console.error('Failed to sync to cloud:', error);
    }
  },
}));