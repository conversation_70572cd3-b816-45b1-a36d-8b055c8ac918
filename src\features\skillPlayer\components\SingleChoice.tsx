import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { SingleChoiceExercise } from "@/types/skill";
import { AnswerFeedback } from "../store";
import { useTheme, useThemedStyles } from '~/lib/theme';
import { getThemedShadow } from '~/lib/themeUtils';

interface Props {
  exercise: SingleChoiceExercise;
  onAnswer: (index: number) => void;
  currentAnswer?: number;
  feedback?: AnswerFeedback;
}

export default function SingleChoice({ exercise, onAnswer, currentAnswer, feedback }: Props) {
  const { prompt, choices, answerIndex } = exercise.payload;
  const { colors } = useTheme();

  // Create theme-aware styles
  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    prompt: {
      fontSize: 28,
      fontWeight: "bold",
      marginBottom: 24,
      color: theme.text,
    },
    optionContainer: {
      backgroundColor: theme.card,
      borderWidth: 2,
      borderColor: theme.border,
      borderRadius: 16,
      padding: 20,
      marginVertical: 8,
      ...getThemedShadow(theme, 'sm'),
    },
    selectedOptionContainer: {
      borderColor: theme.primaryButton,
      backgroundColor: isDark ? colors.primary[900] : colors.primary[50],
    },
    correctOptionContainer: {
      borderColor: colors.success[500],
      backgroundColor: colors.success[50],
    },
    incorrectOptionContainer: {
      borderColor: colors.error[500],
      backgroundColor: colors.error[50],
    },
    optionText: {
      fontSize: 18,
      fontWeight: "500",
      color: theme.text,
    },
  }));

  const getOptionStyle = (idx: number) => {
    const isSelected = currentAnswer === idx;
    const isCorrect = idx === answerIndex;
    const showFeedback = feedback?.showFeedback && isSelected;

    if (showFeedback) {
      if (feedback.isCorrect) {
        return [styles.optionContainer, styles.correctOptionContainer];
      } else {
        return [styles.optionContainer, styles.incorrectOptionContainer];
      }
    }

    return [
      styles.optionContainer,
      isSelected && styles.selectedOptionContainer,
    ];
  };

  return (
    <View>
      <Text style={styles.prompt}>{prompt}</Text>
      <View>
        {choices.map((choice, idx) => (
          <TouchableOpacity
            key={idx}
            activeOpacity={0.8}
            onPress={() => onAnswer(idx)}
            style={getOptionStyle(idx)}
          >
            <Text style={styles.optionText}>{choice}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

// Styles are now created inside the component using useThemedStyles