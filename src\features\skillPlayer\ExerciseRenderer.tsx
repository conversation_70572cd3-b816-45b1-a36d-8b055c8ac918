import React from "react";
import { Text } from "react-native"; // Import Text component
import { Exercise } from "@/types/skill";
import { AnswerFeedback } from "./store";
import { registry } from "./registry";

interface Props {
  exercise: Exercise;
  onAnswer: (value: unknown) => void;
  currentAnswer?: unknown;
  feedback?: AnswerFeedback;
}

export default function ExerciseRenderer({ exercise, onAnswer, currentAnswer, feedback }: Props) {
  const Component = registry[exercise.type];

  if (!Component) {
    console.error(`ExerciseRenderer: No component found for exercise type: ${exercise.type}`);
    return <Text>Error: Unknown exercise type</Text>;
  }

  console.log(`ExerciseRenderer: Rendering component for type: ${exercise.type}`);
  return (
    <Component
      exercise={exercise}
      onAnswer={onAnswer}
      currentAnswer={currentAnswer}
      feedback={feedback}
    />
  );
}