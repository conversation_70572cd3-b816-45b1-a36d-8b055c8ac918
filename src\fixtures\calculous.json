{"id": "skill-calculus-derivatives-001", "name": "The Power of Derivatives", "description": "Master the fundamental concept of derivatives and unlock their power to understand rates of change and function behavior.", "version": 1, "levels": [{"name": "Level 1: Understanding the Concept", "lessons": [{"name": "What is a Derivative?", "objective": "Grasp the intuitive meaning of a derivative as the instantaneous rate of change.", "exercises": [{"id": "L1.1_info", "type": "text-info", "payload": {"character": "PROFESSOR DELTA", "dialogue": "Welcome to the world of calculus! Today, we begin with one of its most powerful tools: the derivative.", "markdown": "### 🚀 Introduction to Derivatives\n\nImagine you're driving a car. Your speed isn't constant, is it? Sometimes you speed up, sometimes you slow down. The derivative helps us understand exactly how fast your speed is changing *at any given moment*.\n\n---\n\n### 📈 Rate of Change\n\nIn mathematics, we often deal with quantities that change over time or with respect to another quantity. The **rate of change** tells us how one quantity changes in response to a change in another.\n\n*   **Average Rate of Change:** If you travel 100 miles in 2 hours, your average speed is 50 miles per hour. This is the total change divided by the time interval.\n\n*   **Instantaneous Rate of Change:** What is your speed *right now*, at this very second? This is what the derivative measures.\n\n---\n\n### 💡 The Intuition\n\nThe derivative of a function at a specific point is the **slope of the tangent line** to the function's graph at that point. The tangent line is the straight line that just touches the curve at that single point and has the same direction as the curve at that point.\n\nThink of it like this: if you zoom in infinitely close to a smooth curve, it starts to look like a straight line. The slope of that line is the derivative."}}, {"id": "L1.1_ex1", "type": "single-choice", "payload": {"prompt": "What does a derivative fundamentally represent?", "choices": ["The total area under a curve", "The instantaneous rate of change of a function", "The maximum value of a function", "The average value of a function over an interval"], "answerIndex": 1, "feedback_correct": "Correct! The derivative tells us how quickly a function is changing at a specific point.", "feedback_incorrect": "The derivative is about how things are changing *moment by moment*, not the total accumulation or maximum value."}}, {"id": "L1.1_ex2", "type": "true-false", "payload": {"prompt": "The derivative of a function at a point is the same as the slope of the secant line passing through that point and another point on the curve.", "answer": false, "feedback_correct": "That's right! The derivative is the slope of the *tangent* line, not the secant line (which connects two points).", "feedback_incorrect": "The derivative specifically relates to the tangent line, which captures the instantaneous slope."}}, {"id": "L1.1_ex3", "type": "fill-blank", "payload": {"promptWithBlank": "The derivative of a function at a point gives us the slope of the _____ line at that point.", "answer": "tangent", "choices": ["secant", "average", "tangent", "vertical"]}}]}, {"name": "The Limit Definition of the Derivative", "objective": "Understand the formal definition of the derivative using limits.", "exercises": [{"id": "L1.2_info", "type": "text-info", "payload": {"character": "PROFESSOR DELTA", "dialogue": "To be precise, we define the derivative using limits. This is where the magic truly happens in calculus.", "markdown": "### 🧮 The Limit Definition\n\nThe derivative of a function $f(x)$, denoted as $f'(x)$, is defined as:\n\n$f'(x) = \\lim_{h \\to 0} \\frac{f(x+h) - f(x)}{h}$\n\nLet's break this down:\n\n*   $f(x+h) - f(x)$: This represents the change in the function's value when the input changes from $x$ to $x+h$. It's the \"rise\" over a small interval.\n*   $h$: This is the change in the input ($x+h - x$). It's the \"run\" over that small interval.\n*   $\\frac{f(x+h) - f(x)}{h}$: This is the average rate of change (or slope of the secant line) over the interval $[x, x+h]$.\n*   $\\lim_{h \\to 0}$: This is the crucial part. We are taking the limit as the interval $h$ becomes infinitesimally small (approaches zero). This transforms the average rate of change into the *instantaneous* rate of change."}}, {"id": "L1.2_ex1", "type": "single-choice", "payload": {"prompt": "Which mathematical concept is essential for defining the derivative formally?", "choices": ["Integration", "Limits", "Series", "Sequences"], "answerIndex": 1, "feedback_correct": "Correct! Limits are fundamental to understanding how the slope of the secant line approaches the slope of the tangent line.", "feedback_incorrect": "The formal definition of the derivative relies on the concept of limits."}}, {"id": "L1.2_ex2", "type": "true-false", "payload": {"prompt": "In the limit definition of the derivative, $h$ represents the change in the output of the function.", "answer": false, "feedback_correct": "That's right. $h$ represents the change in the input ($x$).", "feedback_incorrect": "Remember, $h$ is the small change in $x$, not in $f(x)$."}}, {"id": "L1.2_ex3", "type": "fill-blank", "payload": {"promptWithBlank": "The expression $\\frac{f(x+h) - f(x)}{h}$ represents the _____ rate of change.", "answer": "average", "choices": ["instantaneous", "average", "maximum", "minimum"]}}]}]}, {"name": "Level 2: Calculating Derivatives", "lessons": [{"name": "Basic Differentiation Rules", "objective": "Apply common rules like the power rule, constant multiple rule, and sum/difference rule.", "exercises": [{"id": "L2.1_info", "type": "text-info", "payload": {"character": "PROFESSOR DELTA", "dialogue": "Calculating derivatives can be much simpler than using the limit definition every time. We have powerful rules for that!", "markdown": " ### 📜 Common Differentiation Rules\n\nTo make our lives easier, mathematicians have developed shortcuts (rules) for finding derivatives.\n\n1.  **Power Rule:** If $f(x) = x^n$, then $f'(x) = nx^{n-1}$.\n    *   Example: If $f(x) = x^3$, then $f'(x) = 3x^{3-1} = 3x^2$.\n2.  **Constant Multiple Rule:** If $f(x) = c \\cdot g(x)$ (where $c$ is a constant), then $f'(x) = c \\cdot g'(x)$.\n    *   Example: If $f(x) = 5x^3$, then $f'(x) = 5 \\cdot (3x^2) = 15x^2$.\n3.  **Sum/Difference Rule:** If $f(x) = g(x) \\pm h(x)$, then $f'(x) = g'(x) \\pm h'(x)$.\n    *   Example: If $f(x) = x^2 + 3x$, then $f'(x) = 2x + 3$.\n\nThese rules help us find derivatives much faster than using the limit definition."}}, {"id": "L2.1_ex1", "type": "single-choice", "payload": {"prompt": "What is the derivative of $f(x) = x^5$?", "choices": ["$5x^4$", "$x^5$", "$5x^5$", "$4x^5$"], "answerIndex": 0, "feedback_correct": "Correct! You applied the Power Rule: $nx^{n-1}$.", "feedback_incorrect": "Remember the Power Rule: bring the exponent down as a multiplier and decrease the exponent by 1."}}, {"id": "L2.1_ex2", "type": "single-choice", "payload": {"prompt": "What is the derivative of $f(x) = 8$?", "choices": ["8", "0", "$8x$", "Undefined"], "answerIndex": 1, "feedback_correct": "Exactly! The derivative of any constant is 0.", "feedback_incorrect": "Constants don't change, so their rate of change is zero."}}, {"id": "L2.1_ex3", "type": "single-choice", "payload": {"prompt": "What is the derivative of $f(x) = 3x^4$?", "choices": ["$12x^3$", "$3x^3$", "$4x^3$", "$12x^4$"], "answerIndex": 0, "feedback_correct": "You got it! You used the Constant Multiple Rule and the Power Rule.", "feedback_incorrect": "Apply the Power Rule to $x^4$ to get $4x^3$, then multiply by the constant 3."}}, {"id": "L2.1_ex4", "type": "single-choice", "payload": {"prompt": "What is the derivative of $f(x) = 2x^2 + 5x - 3$?", "choices": ["$4x + 5$", "$2x + 5$", "$4x^2 + 5x$", "$4x + 5 - 3$"], "answerIndex": 0, "feedback_correct": "Perfect! You correctly applied the Sum/Difference Rule and the Power Rule to each term.", "feedback_incorrect": "Differentiate each term separately: derivative of $2x^2$ is $4x$, derivative of $5x$ is $5$, and derivative of $-3$ is $0$."}}]}]}, {"name": "Level 3: Applications of Derivatives", "lessons": [{"name": "Finding Slopes of Tangent Lines", "objective": "Use derivatives to find the slope of the tangent line to a curve at a specific point.", "exercises": [{"id": "L3.1_info", "type": "text-info", "payload": {"character": "PROFESSOR DELTA", "dialogue": "Now that we know how to calculate derivatives, let's see how they help us find the slope of a tangent line at a particular point.", "markdown": "### 📏 Slope of Tangent Lines\n\nWe already know that the derivative $f'(x)$ *is* the slope of the tangent line to the graph of $f(x)$ at any point $x$. To find the slope at a *specific* point, say $x=a$, we simply evaluate $f'(a)$.\n\n**Example:**\nFind the slope of the tangent line to the curve $f(x) = x^2 - 4x$ at $x=3$.\n\n1.  **Find the derivative:** Using the Power Rule and Sum/Difference Rule, we get $f'(x) = 2x - 4$.\n2.  **Evaluate the derivative at the specific point:** Substitute $x=3$ into $f'(x)$.\n    $f'(3) = 2(3) - 4 = 6 - 4 = 2$.\n\nSo, the slope of the tangent line to $f(x) = x^2 - 4x$ at $x=3$ is 2."}}, {"id": "L3.1_ex1", "type": "single-choice", "payload": {"prompt": "To find the slope of the tangent line to $f(x) = x^3$ at $x=2$, what is the first step?", "choices": ["Find $f(2)$", "Find $f'(x)$", "Graph the function", "Find the limit as $h$ approaches 0"], "answerIndex": 1, "feedback_correct": "Correct! The derivative gives us the general formula for the slope.", "feedback_incorrect": "First, you need to find the derivative of the function to know the slope at any given $x$."}}, {"id": "L3.1_ex2", "type": "single-choice", "payload": {"prompt": "What is the slope of the tangent line to $f(x) = 2x^2 + 1$ at $x=1$?", "choices": ["2", "4", "5", "3"], "answerIndex": 1, "feedback_correct": "You're right! $f'(x) = 4x$, so $f'(1) = 4(1) = 4$.", "feedback_incorrect": "The derivative is $f'(x) = 4x$. Evaluating at $x=1$ gives $4(1) = 4$."}}]}]}, {"name": "Level 4: Advanced Topics", "lessons": [{"name": "The Chain Rule", "objective": "Learn how to differentiate composite functions using the chain rule.", "exercises": [{"id": "L4.1_info", "type": "text-info", "payload": {"character": "PROFESSOR DELTA", "dialogue": "Some functions are built from other functions, like nesting dolls. For these, we need the Chain Rule.", "markdown": "### 🔗 The Chain Rule\n\nThe Chain Rule is used to find the derivative of composite functions (functions within functions).\n\nIf $y = f(u)$ and $u = g(x)$, then the derivative of $y$ with respect to $x$ is:\n\n$\\, \\frac{dy}{dx} = \\frac{dy}{du} \\cdot \\frac{du}{dx}$\n\nIn simpler terms: **Derivative of the outer function (keeping the inner function the same) times the derivative of the inner function.**\n\n**Example:**\nFind the derivative of $f(x) = (x^2 + 1)^3$.\n\n*   **Outer function:** $g(u) = u^3$, where $u = x^2 + 1$.\n*   **Inner function:** $u(x) = x^2 + 1$.\n\n1.  **Derivative of the outer function:** $\\frac{dg}{du} = 3u^2$.\n2.  **Derivative of the inner function:** $\\frac{du}{dx} = 2x$.\n3.  **Apply the Chain Rule:** $\\, \\frac{dy}{dx} = (3u^2) \\cdot (2x)$.\n4.  **Substitute back $u = x^2 + 1$:** $\\, \\frac{dy}{dx} = 3(x^2 + 1)^2 \\cdot 2x = 6x(x^2 + 1)^2$.\n\nSo, the derivative of $(x^2 + 1)^3$ is $6x(x^2 + 1)^2$."}}, {"id": "L4.1_ex1", "type": "single-choice", "payload": {"prompt": "Which rule is used to differentiate composite functions (functions within functions)?", "choices": ["Power Rule", "Product Rule", "Chain Rule", "Quotient Rule"], "answerIndex": 2, "feedback_correct": "Correct! The Chain Rule is specifically designed for composite functions.", "feedback_incorrect": "The Chain Rule is the essential tool for differentiating nested functions."}}, {"id": "L4.1_ex2", "type": "single-choice", "payload": {"prompt": "What is the derivative of $f(x) = \\sin(3x)$?", "choices": ["$\\,\\cos(3x)$", "$-3\\sin(3x)$", "$3\\cos(3x)$", "$-\\cos(3x)$"], "answerIndex": 2, "feedback_correct": "Excellent! The derivative of $\\sin(u)$ is $\\cos(u)$, and the derivative of the inner function $3x$ is $3$. So, $3\\cos(3x)$.", "feedback_incorrect": "The derivative of $\\sin(u)$ is $\\cos(u)$, and the derivative of the inner part $3x$ is $3$. Multiply them together."}}]}]}]}