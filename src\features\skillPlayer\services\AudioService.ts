import { Audio } from 'expo-av';
import { Platform } from 'react-native';

export enum AudioType {
  CORRECT = 'correct',
  INCORRECT = 'incorrect',
  SELECTION = 'selection',
  BUTTON_PRESS = 'button_press',
  EXERCISE_COMPLETE = 'exercise_complete',
  LESSON_COMPLETE = 'lesson_complete',
  STREAK = 'streak',
  MILESTONE = 'milestone'
}

class AudioService {
  private isEnabled: boolean = true;
  private sounds: Map<AudioType, Audio.Sound> = new Map();
  private isInitialized: boolean = false;

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Configure audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      // Pre-load sound effects
      await this.loadSounds();
      this.isInitialized = true;
    } catch (error) {
      console.warn('Audio initialization failed:', error);
    }
  }

  private async loadSounds() {
    const soundFiles = {
      [AudioType.CORRECT]: require('@/assets/sounds/correct.mp3'),
      [AudioType.INCORRECT]: require('@/assets/sounds/incorrect.mp3'),
      [AudioType.SELECTION]: require('@/assets/sounds/selection.mp3'),
      [AudioType.BUTTON_PRESS]: require('@/assets/sounds/button.mp3'),
      [AudioType.EXERCISE_COMPLETE]: require('@/assets/sounds/exercise_complete.mp3'),
      [AudioType.LESSON_COMPLETE]: require('@/assets/sounds/lesson_complete.mp3'),
      [AudioType.STREAK]: require('@/assets/sounds/streak.mp3'),
      [AudioType.MILESTONE]: require('@/assets/sounds/milestone.mp3'),
    };

    for (const [type, source] of Object.entries(soundFiles)) {
      try {
        const { sound } = await Audio.Sound.createAsync(source, {
          shouldPlay: false,
          volume: 0.7,
        });
        this.sounds.set(type as AudioType, sound);
      } catch (error) {
        console.warn(`Failed to load sound ${type}:`, error);
        // Create fallback synthesized sounds
        await this.createFallbackSound(type as AudioType);
      }
    }
  }

  private async createFallbackSound(type: AudioType) {
    // Create simple synthesized sounds as fallbacks
    try {
      let frequency: number;
      let duration: number;

      switch (type) {
        case AudioType.CORRECT:
          frequency = 800; // Higher pitch for success
          duration = 200;
          break;
        case AudioType.INCORRECT:
          frequency = 300; // Lower pitch for error
          duration = 300;
          break;
        case AudioType.SELECTION:
          frequency = 600;
          duration = 50;
          break;
        default:
          frequency = 500;
          duration = 100;
      }

      // Note: This is a simplified fallback. In production, you'd want actual sound files
      // For now, we'll just store a placeholder
      const { sound } = await Audio.Sound.createAsync(
        { uri: 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT' },
        { shouldPlay: false, volume: 0.3 }
      );
      this.sounds.set(type, sound);
    } catch (error) {
      console.warn(`Failed to create fallback sound for ${type}:`, error);
    }
  }

  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  getEnabled(): boolean {
    return this.isEnabled;
  }

  async play(type: AudioType, volume: number = 0.7) {
    if (!this.isEnabled || !this.isInitialized) return;

    try {
      const sound = this.sounds.get(type);
      if (sound) {
        await sound.setVolumeAsync(volume);
        await sound.replayAsync();
      }
    } catch (error) {
      console.warn(`Failed to play sound ${type}:`, error);
    }
  }

  async cleanup() {
    for (const sound of this.sounds.values()) {
      try {
        await sound.unloadAsync();
      } catch (error) {
        console.warn('Failed to unload sound:', error);
      }
    }
    this.sounds.clear();
    this.isInitialized = false;
  }

  // Convenience methods
  async playCorrect() {
    await this.play(AudioType.CORRECT);
  }

  async playIncorrect() {
    await this.play(AudioType.INCORRECT);
  }

  async playSelection() {
    await this.play(AudioType.SELECTION, 0.4);
  }

  async playButtonPress() {
    await this.play(AudioType.BUTTON_PRESS, 0.5);
  }

  async playExerciseComplete() {
    await this.play(AudioType.EXERCISE_COMPLETE);
  }

  async playLessonComplete() {
    await this.play(AudioType.LESSON_COMPLETE);
  }

  async playStreak() {
    await this.play(AudioType.STREAK);
  }

  async playMilestone() {
    await this.play(AudioType.MILESTONE);
  }
}

export const audioService = new AudioService();
