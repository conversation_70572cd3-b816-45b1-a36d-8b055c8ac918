import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { Platform } from 'react-native';
import * as NavigationBar from 'expo-navigation-bar';
import { useTheme } from '~/lib/theme';

/**
 * StatusBarManager - Handles theme-aware status bar and navigation bar styling
 * 
 * Features:
 * - Automatic status bar style based on theme (light/dark)
 * - Navigation bar theming on Android
 * - Smooth transitions when theme changes
 * - Platform-specific optimizations
 */
export const StatusBarManager: React.FC = () => {
  const { isDarkMode, theme, colors } = useTheme();

  useEffect(() => {
    // Configure Android navigation bar
    if (Platform.OS === 'android') {
      const configureNavigationBar = async () => {
        try {
          // Set navigation bar background color
          await NavigationBar.setBackgroundColorAsync(theme.card);
          
          // Set navigation bar button style
          await NavigationBar.setButtonStyleAsync(
            isDarkMode ? 'light' : 'dark'
          );
          
          // Optional: Set navigation bar border color
          if (Platform.Version >= 28) {
            await NavigationBar.setBorderColorAsync(theme.border);
          }
        } catch (error) {
          console.warn('Failed to configure navigation bar:', error);
        }
      };

      configureNavigationBar();
    }
  }, [isDarkMode, theme, colors]);

  return (
    <StatusBar
      style={isDarkMode ? 'light' : 'dark'}
      backgroundColor={theme.background}
      translucent={false}
    />
  );
};

/**
 * StatusBarManagerTranslucent - For screens that need translucent status bar
 * (e.g., full-screen images, video players)
 */
export const StatusBarManagerTranslucent: React.FC<{
  style?: 'light' | 'dark' | 'auto';
}> = ({ style = 'auto' }) => {
  const { isDarkMode } = useTheme();
  
  const statusBarStyle = style === 'auto' 
    ? (isDarkMode ? 'light' : 'dark')
    : style;

  return (
    <StatusBar
      style={statusBarStyle}
      translucent={true}
      backgroundColor="transparent"
    />
  );
};

/**
 * StatusBarManagerModal - For modal screens with specific styling needs
 */
export const StatusBarManagerModal: React.FC<{
  backgroundColor?: string;
  style?: 'light' | 'dark';
}> = ({ backgroundColor, style }) => {
  const { isDarkMode, theme } = useTheme();
  
  const statusBarStyle = style || (isDarkMode ? 'light' : 'dark');
  const bgColor = backgroundColor || theme.card;

  return (
    <StatusBar
      style={statusBarStyle}
      backgroundColor={bgColor}
      translucent={false}
    />
  );
};

export default StatusBarManager;
