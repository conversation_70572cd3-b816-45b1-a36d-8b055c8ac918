import {
  Subject,
  Topic,
  Skill,
  SubjectProgress,
  TopicProgress,
  SkillProgress,
  CompletionStats,
  UUID
} from '@/types/skill';
import { hierarchyDataService, SkillRecord } from './services/HierarchyDataService';
import { localStorageService } from '@/features/freemium/services/LocalStorageService';

export class HierarchyService {
  private static instance: HierarchyService;
  private progressCache: Map<string, any> = new Map();

  static getInstance(): HierarchyService {
    if (!HierarchyService.instance) {
      HierarchyService.instance = new HierarchyService();
    }
    return HierarchyService.instance;
  }

  // -------------------- Data Access Methods --------------------

  async getAllSubjects(): Promise<Subject[]> {
    return hierarchyDataService.getSubjects();
  }

  async getSubjectById(id: UUID): Promise<Subject | null> {
    return hierarchyDataService.getSubjectById(id);
  }

  async getTopicsBySubjectId(subjectId: UUID): Promise<Topic[]> {
    return hierarchyDataService.getTopicsBySubjectId(subjectId);
  }

  async getTopicById(id: UUID): Promise<Topic | null> {
    return hierarchyDataService.getTopicById(id);
  }

  async getSkillsByTopicId(topicId: UUID): Promise<SkillRecord[]> {
    return hierarchyDataService.getSkillsByTopicId(topicId);
  }

  async getSkillById(id: UUID): Promise<Skill | null> {
    return hierarchyDataService.getSkillContent(id);
  }

  async getSkillMetadataById(id: UUID): Promise<SkillRecord | null> {
    return hierarchyDataService.getSkillMetadataById(id);
  }

  // -------------------- Progress Management --------------------

  async getSkillProgress(skillId: UUID, userId?: string): Promise<SkillProgress | null> {
    const key = `skill_progress_${skillId}${userId ? `_${userId}` : ''}`;
    
    if (this.progressCache.has(key)) {
      return this.progressCache.get(key);
    }

    try {
      const progress = await localStorageService.getItem<SkillProgress>(key);
      if (progress) {
        this.progressCache.set(key, progress);
      }
      return progress;
    } catch (error) {
      console.error('Error loading skill progress:', error);
      return null;
    }
  }

  async saveSkillProgress(skillId: UUID, progress: SkillProgress, userId?: string): Promise<void> {
    const key = `skill_progress_${skillId}${userId ? `_${userId}` : ''}`;
    
    try {
      await localStorageService.setItem(key, progress);
      this.progressCache.set(key, progress);
    } catch (error) {
      console.error('Error saving skill progress:', error);
    }
  }

  async getTopicProgress(topicId: UUID, userId?: string): Promise<TopicProgress | null> {
    const skills = await this.getSkillsByTopicId(topicId);
    const skillProgresses: Record<UUID, SkillProgress> = {};
    
    let hasAnyProgress = false;
    let completedSkills = 0;
    let totalScore = 0;
    let totalTime = 0;
    let lastAccessed = '';

    for (const skill of skills) {
      const progress = await this.getSkillProgress(skill.id, userId);
      if (progress) {
        skillProgresses[skill.id] = progress;
        hasAnyProgress = true;
        
        if (progress.isCompleted) {
          completedSkills++;
        }
        
        if (progress.score) {
          totalScore += progress.score;
        }
        
        if (progress.timeSpent) {
          totalTime += progress.timeSpent;
        }
        
        if (progress.lastAccessed > lastAccessed) {
          lastAccessed = progress.lastAccessed;
        }
      }
    }

    if (!hasAnyProgress) {
      return null;
    }

    const isCompleted = completedSkills === skills.length && skills.length > 0;
    const overallScore = skills.length > 0 ? Math.round(totalScore / skills.length) : 0;

    return {
      topicId,
      skillProgresses,
      isCompleted,
      completionDate: isCompleted ? lastAccessed : undefined,
      overallScore,
      totalTimeSpent: totalTime,
      lastAccessed
    };
  }

  async getSubjectProgress(subjectId: UUID, userId?: string): Promise<SubjectProgress | null> {
    const topics = await this.getTopicsBySubjectId(subjectId);
    const topicProgresses: Record<UUID, TopicProgress> = {};
    
    let hasAnyProgress = false;
    let completedTopics = 0;
    let totalScore = 0;
    let totalTime = 0;
    let lastAccessed = '';

    for (const topic of topics) {
      const progress = await this.getTopicProgress(topic.id, userId);
      if (progress) {
        topicProgresses[topic.id] = progress;
        hasAnyProgress = true;
        
        if (progress.isCompleted) {
          completedTopics++;
        }
        
        if (progress.overallScore) {
          totalScore += progress.overallScore;
        }
        
        if (progress.totalTimeSpent) {
          totalTime += progress.totalTimeSpent;
        }
        
        if (progress.lastAccessed > lastAccessed) {
          lastAccessed = progress.lastAccessed;
        }
      }
    }

    if (!hasAnyProgress) {
      return null;
    }

    const isCompleted = completedTopics === topics.length && topics.length > 0;
    const overallScore = topics.length > 0 ? Math.round(totalScore / topics.length) : 0;

    return {
      subjectId,
      topicProgresses,
      isCompleted,
      completionDate: isCompleted ? lastAccessed : undefined,
      overallScore,
      totalTimeSpent: totalTime,
      lastAccessed
    };
  }

  // -------------------- Progress Calculation --------------------

  async calculateCompletionStats(userId?: string): Promise<CompletionStats> {
    const subjects = await this.getAllSubjects();
    let completedSubjects = 0;
    let totalTopics = 0;
    let completedTopics = 0;
    let totalSkills = 0;
    let completedSkills = 0;

    for (const subject of subjects) {
      const subjectProgress = await this.getSubjectProgress(subject.id, userId);
      if (subjectProgress?.isCompleted) {
        completedSubjects++;
      }

      const topics = await this.getTopicsBySubjectId(subject.id);
      totalTopics += topics.length;

      for (const topic of topics) {
        const topicProgress = await this.getTopicProgress(topic.id, userId);
        if (topicProgress?.isCompleted) {
          completedTopics++;
        }

        const skills = await this.getSkillsByTopicId(topic.id);
        totalSkills += skills.length;

        for (const skill of skills) {
          const skillProgress = await this.getSkillProgress(skill.id, userId);
          if (skillProgress?.isCompleted) {
            completedSkills++;
          }
        }
      }
    }

    const overallProgress = totalSkills > 0 ? Math.round((completedSkills / totalSkills) * 100) : 0;

    return {
      totalSubjects: subjects.length,
      completedSubjects,
      totalTopics,
      completedTopics,
      totalSkills,
      completedSkills,
      overallProgress
    };
  }

  // -------------------- Utility Methods --------------------

  async isTopicUnlocked(topicId: UUID, userId?: string): Promise<boolean> {
    // Get completed topics for this user
    const completedTopics: UUID[] = [];
    const topic = await this.getTopicById(topicId);
    if (!topic?.subjectId) return true;

    const allTopics = await this.getTopicsBySubjectId(topic.subjectId);
    for (const t of allTopics) {
      const progress = await this.getTopicProgress(t.id, userId);
      if (progress?.isCompleted) {
        completedTopics.push(t.id);
      }
    }

    return hierarchyDataService.isTopicUnlocked(topicId, completedTopics);
  }

  async getNextUnlockedTopic(subjectId: UUID, userId?: string): Promise<Topic | null> {
    // Get completed topics for this user
    const completedTopics: UUID[] = [];
    const allTopics = await this.getTopicsBySubjectId(subjectId);

    for (const topic of allTopics) {
      const progress = await this.getTopicProgress(topic.id, userId);
      if (progress?.isCompleted) {
        completedTopics.push(topic.id);
      }
    }

    return hierarchyDataService.getNextUnlockedTopic(subjectId, completedTopics);
  }

  async getNextUnlockedSkill(topicId: UUID, userId?: string): Promise<SkillRecord | null> {
    // Get completed skills for this user
    const completedSkills: UUID[] = [];
    const allSkills = await this.getSkillsByTopicId(topicId);

    for (const skill of allSkills) {
      const progress = await this.getSkillProgress(skill.id, userId);
      if (progress?.isCompleted) {
        completedSkills.push(skill.id);
      }
    }

    return hierarchyDataService.getNextUnlockedSkill(topicId, completedSkills);
  }

  // -------------------- Cache Management --------------------

  clearProgressCache(): void {
    this.progressCache.clear();
  }

  async refreshProgress(userId?: string): Promise<void> {
    this.clearProgressCache();
    // Pre-load commonly accessed progress data
    const subjects = await this.getAllSubjects();
    for (const subject of subjects) {
      await this.getSubjectProgress(subject.id, userId);
    }
  }
}

// Export singleton instance
export const hierarchyService = HierarchyService.getInstance();
