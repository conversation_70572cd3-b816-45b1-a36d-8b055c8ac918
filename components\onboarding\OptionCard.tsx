import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';

interface OptionCardProps {
  text: string;
  selected: boolean;
  onPress: () => void;
  color: string;
}

export function OptionCard({ text, selected, onPress, color }: OptionCardProps) {
  return (
    <TouchableOpacity
      style={[
        styles.card,
        { borderColor: selected ? color : '#E5E7f0', backgroundColor: selected ? '#f0f0f0' : '#f7f8fa' }
      ]}
      onPress={onPress}
      activeOpacity={0.85}
    >
      <Text style={[styles.text, selected && { color }]}>{text}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 16,
    paddingVertical: 18,
    paddingHorizontal: 18,
    marginBottom: 12,
    alignItems: 'flex-start',
    justifyContent: 'center',
    borderWidth: 2,
   
  },
  text: {
    fontSize: 18,
    color: '#222',
    fontWeight: '600',
  },
}); 