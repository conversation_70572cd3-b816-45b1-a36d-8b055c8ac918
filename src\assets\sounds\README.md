# Sound Assets for SkillPlayer

This directory contains audio files for the enhanced SkillPlayer experience.

## Required Sound Files

Place the following audio files in this directory:

- `correct.mp3` - Positive chime for correct answers
- `incorrect.mp3` - Gentle error tone for incorrect answers  
- `selection.mp3` - Subtle tap feedback for option selection
- `button.mp3` - But<PERSON> press feedback
- `exercise_complete.mp3` - Exercise completion sound
- `lesson_complete.mp3` - Lesson completion celebration
- `streak.mp3` - Streak milestone achievement
- `milestone.mp3` - Major milestone celebration

## Audio Specifications

- **Format**: MP3 or WAV
- **Duration**: 0.1-2 seconds (keep short for responsiveness)
- **Volume**: Normalized to prevent clipping
- **Quality**: 44.1kHz, 16-bit minimum

## Fallback Behavior

If sound files are missing, the AudioService will:
1. Attempt to create simple synthesized tones as fallbacks
2. Gracefully handle missing files without crashing
3. Log warnings for debugging

## Licensing

Ensure all audio files are properly licensed for your application use.

## Recommended Sources

- Freesound.org (CC licensed)
- Zapsplat (with subscription)
- Custom recordings
- Synthesized tones using audio software
