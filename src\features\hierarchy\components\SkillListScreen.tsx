import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { useUser } from '@clerk/clerk-expo';
import { useTheme } from '@/lib/theme';
import { Topic, SkillProgress } from '@/types/skill';
import { hierarchyService } from '../HierarchyService';
import { SkillMetadata } from '../services/HierarchyDataService';
import ProgressBar from '@/features/skillPlayer/components/ProgressBar';

const { width } = Dimensions.get('window');

interface SkillCardProps {
  skill: SkillMetadata;
  progress: number;
  index: number;
  onPress: () => void;
  isCompleted: boolean;
  isLocked: boolean;
}

const SkillCard: React.FC<SkillCardProps> = ({
  skill,
  progress,
  index,
  onPress,
  isCompleted,
  isLocked,
}) => {
  const { theme, colors } = useTheme();
  const scale = useSharedValue(1);
  const elevation = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      shadowOpacity: interpolate(elevation.value, [1, 1.5], [0.1, 0.25]),
      shadowRadius: interpolate(elevation.value, [1, 1.5], [4, 8]),
      elevation: interpolate(elevation.value, [1, 1.5], [2, 6]),
    };
  });

  const handlePressIn = () => {
    if (!isLocked) {
      scale.value = withSpring(0.97, { damping: 15, stiffness: 150 });
      elevation.value = withTiming(1.5, { duration: 150 });
    }
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 150 });
    elevation.value = withTiming(1, { duration: 150 });
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'beginner':
        return colors.success[500];
      case 'intermediate':
        return colors.warning[500];
      case 'advanced':
        return colors.error[500];
      default:
        return theme.secondaryText;
    }
  };

  const getSkillIcon = (index: number) => {
    const icons = ['🎯', '📝', '🔍', '💡', '🚀', '⚡', '🎨', '🔧'];
    return icons[index % icons.length];
  };

  const getLessonCount = () => {
    // For skill metadata, we don't have the full content yet
    // This could be estimated or fetched separately if needed
    return '?'; // Will be replaced with actual count when skill content is loaded
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={isLocked}
      activeOpacity={0.9}
    >
      <Animated.View
        style={[
          styles.skillCard,
          {
            backgroundColor: theme.card,
            borderColor: isCompleted ? colors.success[500] : theme.border,
            shadowColor: theme.shadow,
          },
          animatedStyle,
          isLocked && { opacity: 0.6 },
          isCompleted && { borderWidth: 2 },
        ]}
      >
        {/* Skill Header */}
        <View style={styles.cardHeader}>
          <View
            style={[
              styles.iconContainer,
              {
                backgroundColor: isCompleted
                  ? colors.success[500]
                  : colors.primary[100],
              },
            ]}
          >
            <Text style={styles.skillIcon}>
              {isCompleted ? '✅' : getSkillIcon(index)}
            </Text>
          </View>
          
          {isLocked && (
            <View style={styles.lockIcon}>
              <Ionicons name="lock-closed" size={16} color={theme.secondaryText} />
            </View>
          )}
          
          {isCompleted && (
            <View style={styles.completedBadge}>
              <Ionicons name="checkmark-circle" size={20} color={colors.success[500]} />
            </View>
          )}
        </View>

        {/* Skill Content */}
        <View style={styles.cardContent}>
          <Text style={[styles.skillTitle, { color: theme.text }]} numberOfLines={2}>
            {skill.name}
          </Text>
          <Text style={[styles.skillDescription, { color: theme.secondaryText }]} numberOfLines={3}>
            {skill.description}
          </Text>

          {/* Meta Information */}
          <View style={styles.metaInfo}>
            {skill.difficulty && (
              <View style={styles.difficultyContainer}>
                <Ionicons
                  name="trending-up-outline"
                  size={14}
                  color={getDifficultyColor(skill.difficulty)}
                />
                <Text
                  style={[
                    styles.difficultyText,
                    { color: getDifficultyColor(skill.difficulty) },
                  ]}
                >
                  {skill.difficulty}
                </Text>
              </View>
            )}
            
            {skill.estimatedDuration && (
              <View style={styles.durationContainer}>
                <Ionicons name="time-outline" size={14} color={theme.secondaryText} />
                <Text style={[styles.durationText, { color: theme.secondaryText }]}>
                  {skill.estimatedDuration}m
                </Text>
              </View>
            )}
          </View>

          {/* Lesson Count */}
          <Text style={[styles.lessonCount, { color: theme.secondaryText }]}>
            {getLessonCount()} lessons
          </Text>

          {/* Progress Bar */}
          <View style={styles.progressContainer}>
            <ProgressBar progress={progress / 100} animated={true} />
            <Text style={[styles.progressText, { color: theme.secondaryText }]}>
              {progress}% complete
            </Text>
          </View>
        </View>

        {/* Continue Arrow */}
        {!isLocked && (
          <View style={styles.arrowContainer}>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={isCompleted ? colors.success[500] : colors.primary[500]}
            />
          </View>
        )}
      </Animated.View>
    </TouchableOpacity>
  );
};

interface SkillListScreenProps {
  topicId: string;
  onSkillSelect: (skillId: string) => void;
  onBack: () => void;
}

export default function SkillListScreen({ topicId, onSkillSelect, onBack }: SkillListScreenProps) {
  const { user } = useUser();
  const { theme, colors } = useTheme();

  const [topic, setTopic] = useState<Topic | null>(null);
  const [skills, setSkills] = useState<SkillMetadata[]>([]);
  const [skillProgresses, setSkillProgresses] = useState<Record<string, number>>({});
  const [completedSkills, setCompletedSkills] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSkills();
  }, [topicId, user]);

  const loadSkills = async () => {
    try {
      setLoading(true);
      
      // Load topic info
      const topicData = await hierarchyService.getTopicById(topicId);
      setTopic(topicData || null);

      // Load skills for this topic
      const skillsData = await hierarchyService.getSkillsByTopicId(topicId);
      setSkills(skillsData);

      // Load progress and completion status for each skill
      const progresses: Record<string, number> = {};
      const completed = new Set<string>();

      for (const skill of skillsData) {
        const progress = await hierarchyService.getSkillProgress(skill.id, user?.id);
        if (progress) {
          progresses[skill.id] = calculateProgressPercentage(progress);
          if (progress.isCompleted) {
            completed.add(skill.id);
          }
        } else {
          progresses[skill.id] = 0;
        }
      }

      setSkillProgresses(progresses);
      setCompletedSkills(completed);
    } catch (error) {
      console.error('Error loading skills:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateProgressPercentage = (progress: SkillProgress): number => {
    if (!progress || !progress.completedExercises) return 0;

    // For now, use a simple calculation based on progress data
    // In a full implementation, you might fetch the skill content to get exact exercise count
    if (progress.score !== undefined) {
      return progress.score;
    }

    // Fallback calculation
    return progress.isCompleted ? 100 : 0;
  };

  const handleSkillPress = (skill: SkillMetadata) => {
    // For now, skills are not locked within a topic
    // In the future, you could implement skill prerequisites
    onSkillSelect(skill.id);
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.text }]}>Loading skills...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color={theme.text} />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.text }]} numberOfLines={2}>
            {topic?.name || 'Skills'}
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.secondaryText }]}>
            Choose a skill to practice
          </Text>
        </View>
      </View>

      {/* Skills List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {skills.map((skill, index) => (
          <SkillCard
            key={skill.id}
            skill={skill}
            progress={skillProgresses[skill.id] || 0}
            index={index}
            onPress={() => handleSkillPress(skill)}
            isCompleted={completedSkills.has(skill.id)}
            isLocked={false} // Skills are not locked for now
          />
        ))}
        
        {skills.length === 0 && (
          <View style={styles.emptyState}>
            <Text style={[styles.emptyStateText, { color: theme.secondaryText }]}>
              No skills available in this topic yet.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  backButton: {
    marginRight: 16,
    padding: 4,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  skillCard: {
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 16,
    padding: 20,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  skillIcon: {
    fontSize: 20,
  },
  lockIcon: {
    marginLeft: 'auto',
  },
  completedBadge: {
    marginLeft: 'auto',
  },
  cardContent: {
    flex: 1,
  },
  skillTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    lineHeight: 22,
  },
  skillDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  metaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  difficultyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  durationText: {
    fontSize: 12,
    marginLeft: 4,
  },
  lessonCount: {
    fontSize: 12,
    marginBottom: 12,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressText: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'right',
  },
  arrowContainer: {
    position: 'absolute',
    right: 20,
    top: '50%',
    transform: [{ translateY: -10 }],
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
  },
});
