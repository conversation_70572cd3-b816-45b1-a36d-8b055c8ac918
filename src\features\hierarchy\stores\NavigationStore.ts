import { create } from 'zustand';
import { UUID } from '@/types/skill';

export type NavigationLevel = 'subjects' | 'topics' | 'skills';

export interface NavigationState {
  level: NavigationLevel;
  subjectId?: UUID;
  topicId?: UUID;
  breadcrumb: Array<{
    level: NavigationLevel;
    id?: UUID;
    name: string;
  }>;
}

interface NavigationStore extends NavigationState {
  // Navigation actions
  navigateToSubjects: () => void;
  navigateToTopics: (subjectId: UUID, subjectName: string) => void;
  navigateToSkills: (topicId: UUID, topicName: string) => void;
  navigateBack: () => void;
  
  // Utility methods
  getCurrentBreadcrumb: () => string;
  canGoBack: () => boolean;
  reset: () => void;
}

const initialState: NavigationState = {
  level: 'subjects',
  breadcrumb: [{ level: 'subjects', name: 'Subjects' }],
};

export const useNavigationStore = create<NavigationStore>((set, get) => ({
  ...initialState,

  navigateToSubjects: () => {
    set({
      level: 'subjects',
      subjectId: undefined,
      topicId: undefined,
      breadcrumb: [{ level: 'subjects', name: 'Subjects' }],
    });
  },

  navigateToTopics: (subjectId: UUID, subjectName: string) => {
    set({
      level: 'topics',
      subjectId,
      topicId: undefined,
      breadcrumb: [
        { level: 'subjects', name: 'Subjects' },
        { level: 'topics', id: subjectId, name: subjectName },
      ],
    });
  },

  navigateToSkills: (topicId: UUID, topicName: string) => {
    const state = get();
    set({
      level: 'skills',
      topicId,
      breadcrumb: [
        ...state.breadcrumb,
        { level: 'skills', id: topicId, name: topicName },
      ],
    });
  },

  navigateBack: () => {
    const state = get();
    const newBreadcrumb = [...state.breadcrumb];
    newBreadcrumb.pop();

    if (newBreadcrumb.length === 0) {
      // Fallback to subjects
      set({
        level: 'subjects',
        subjectId: undefined,
        topicId: undefined,
        breadcrumb: [{ level: 'subjects', name: 'Subjects' }],
      });
      return;
    }

    const lastItem = newBreadcrumb[newBreadcrumb.length - 1];
    
    switch (lastItem.level) {
      case 'subjects':
        set({
          level: 'subjects',
          subjectId: undefined,
          topicId: undefined,
          breadcrumb: newBreadcrumb,
        });
        break;
      
      case 'topics':
        set({
          level: 'topics',
          subjectId: lastItem.id,
          topicId: undefined,
          breadcrumb: newBreadcrumb,
        });
        break;
      
      case 'skills':
        set({
          level: 'skills',
          topicId: lastItem.id,
          breadcrumb: newBreadcrumb,
        });
        break;
    }
  },

  getCurrentBreadcrumb: () => {
    const state = get();
    return state.breadcrumb.map(item => item.name).join(' > ');
  },

  canGoBack: () => {
    const state = get();
    return state.breadcrumb.length > 1;
  },

  reset: () => {
    set(initialState);
  },
}));

// Helper hooks for easier access to specific navigation states
export const useCurrentLevel = () => useNavigationStore(state => state.level);
export const useCurrentSubjectId = () => useNavigationStore(state => state.subjectId);
export const useCurrentTopicId = () => useNavigationStore(state => state.topicId);
export const useBreadcrumb = () => useNavigationStore(state => state.breadcrumb);
export const useCanGoBack = () => useNavigationStore(state => state.canGoBack());

// Navigation action hooks
export const useNavigationActions = () => {
  const store = useNavigationStore();
  return {
    navigateToSubjects: store.navigateToSubjects,
    navigateToTopics: store.navigateToTopics,
    navigateToSkills: store.navigateToSkills,
    navigateBack: store.navigateBack,
    reset: store.reset,
  };
};
