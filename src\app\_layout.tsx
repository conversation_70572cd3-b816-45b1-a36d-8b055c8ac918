import './global.css';
import { Slot, SplashScreen } from 'expo-router';
import { LogBox, useColorScheme, View, Text } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { tokenCache } from '@/utils/cache';
import { ClerkProvider, ClerkLoaded, useAuth } from '@clerk/clerk-expo';
import { ConvexReactClient } from 'convex/react';
import { ConvexProviderWithClerk } from 'convex/react-clerk';
import { useEffect } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { ThemeProvider, useTheme } from 'lib/theme';
import { useFonts } from '@/utils/fonts';
import { OnboardingStateProvider } from 'lib/onboardingState';
import { FreemiumProvider } from '@/features/freemium/FreemiumProvider';

SplashScreen.preventAutoHideAsync();

const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!;

if (!publishableKey) {
  throw new Error(
    'Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env'
  );
}
LogBox.ignoreLogs(['Clerk: Clerk has been loaded with development keys.']);

const convex = new ConvexReactClient(process.env.EXPO_PUBLIC_CONVEX_URL!, {
  unsavedChangesWarning: false,
});

// AppContent component with theme-aware status bar
const AppContent: React.FC = () => {
  const { theme, isDarkMode } = useTheme();

  return (
    <>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} backgroundColor={theme.background} />
      <ClerkProvider
        publishableKey={process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!}
        tokenCache={tokenCache}
      >
        <FreemiumProvider>
          <Slot />
        </FreemiumProvider>
      </ClerkProvider>
    </>
  );
};

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const { fontsLoaded, fontError } = useFonts();

  useEffect(() => {
    if (fontsLoaded || fontError) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  // Show loading screen while fonts are loading
  if (!fontsLoaded && !fontError) {
    return (
      <View className="flex-1 justify-center items-center bg-[#111]">
        <Text className="text-white text-lg">Loading fonts...</Text>
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <OnboardingStateProvider>
        <ThemeProvider>
          <AppContent />
        </ThemeProvider>
      </OnboardingStateProvider>
    </GestureHandlerRootView>
  );
}
