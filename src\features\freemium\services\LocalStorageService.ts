import AsyncStorage from '@react-native-async-storage/async-storage';
import { Skill, Lesson, Exercise } from '@/features/skillPlayer/types';

// Storage Keys
const STORAGE_KEYS = {
  USER_PROGRESS: 'user_progress',
  OFFLINE_COURSES: 'offline_courses',
  USER_SUBSCRIPTION: 'user_subscription',
  LESSON_STATS: 'lesson_stats',
  STREAK_DATA: 'streak_data',
  SETTINGS: 'app_settings',
  LAST_SYNC: 'last_sync_timestamp',
  PENDING_SYNC: 'pending_sync_data',
} as const;

// Local Data Structures
export interface LocalUserProgress {
  userId: string;
  skillProgress: Record<string, SkillProgress>;
  globalStats: GlobalStats;
  lastUpdated: number;
  version: string;
}

export interface SkillProgress {
  skillId: string;
  currentLessonIndex: number;
  completedLessons: string[];
  lessonProgress: Record<string, LessonProgress>;
  totalXP: number;
  lastAccessed: number;
}

export interface LessonProgress {
  lessonId: string;
  isCompleted: boolean;
  completedExercises: string[];
  exerciseAnswers: Record<string, unknown>;
  attempts: number;
  bestScore: number;
  timeSpent: number;
  completedAt?: number;
}

export interface GlobalStats {
  totalXP: number;
  currentStreak: number;
  longestStreak: number;
  totalLessonsCompleted: number;
  totalExercisesCompleted: number;
  totalTimeSpent: number;
  lastActiveDate: string;
  streakFreezeUsed: number;
  achievements: string[];
}

export interface OfflineCourse {
  skill: Skill;
  bundleVersion: string;
  downloadedAt: number;
  size: number;
  isDefault: boolean;
}

export interface PendingSyncData {
  progressUpdates: LocalUserProgress[];
  completedLessons: string[];
  achievements: string[];
  timestamp: number;
}

class LocalStorageService {
  private cache: Map<string, any> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  // Generic storage methods
  async setItem<T>(key: string, value: T): Promise<void> {
    try {
      const serialized = JSON.stringify({
        data: value,
        timestamp: Date.now(),
      });
      await AsyncStorage.setItem(key, serialized);
      this.cache.set(key, { data: value, timestamp: Date.now() });
    } catch (error) {
      console.error(`Failed to save ${key}:`, error);
      throw new Error(`Storage write failed for ${key}`);
    }
  }

  async getItem<T>(key: string): Promise<T | null> {
    try {
      // Check cache first
      const cached = this.cache.get(key);
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return cached.data;
      }

      const stored = await AsyncStorage.getItem(key);
      if (!stored) return null;

      const parsed = JSON.parse(stored);
      const data = parsed.data;
      
      // Update cache
      this.cache.set(key, { data, timestamp: Date.now() });
      
      return data;
    } catch (error) {
      console.error(`Failed to read ${key}:`, error);
      return null;
    }
  }

  private async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
      this.cache.delete(key);
    } catch (error) {
      console.error(`Failed to remove ${key}:`, error);
    }
  }

  // User Progress Methods
  async getUserProgress(userId: string): Promise<LocalUserProgress | null> {
    const key = `${STORAGE_KEYS.USER_PROGRESS}_${userId}`;
    return await this.getItem<LocalUserProgress>(key);
  }

  async saveUserProgress(progress: LocalUserProgress): Promise<void> {
    const key = `${STORAGE_KEYS.USER_PROGRESS}_${progress.userId}`;
    progress.lastUpdated = Date.now();
    progress.version = '1.0.0'; // App version for migration handling
    await this.setItem(key, progress);
  }

  async updateSkillProgress(userId: string, skillId: string, updates: Partial<SkillProgress>): Promise<void> {
    const progress = await this.getUserProgress(userId) || this.createEmptyProgress(userId);
    
    if (!progress.skillProgress[skillId]) {
      progress.skillProgress[skillId] = {
        skillId,
        currentLessonIndex: 0,
        completedLessons: [],
        lessonProgress: {},
        totalXP: 0,
        lastAccessed: Date.now(),
      };
    }

    progress.skillProgress[skillId] = {
      ...progress.skillProgress[skillId],
      ...updates,
      lastAccessed: Date.now(),
    };

    await this.saveUserProgress(progress);
  }

  async updateLessonProgress(
    userId: string, 
    skillId: string, 
    lessonId: string, 
    updates: Partial<LessonProgress>
  ): Promise<void> {
    const progress = await this.getUserProgress(userId) || this.createEmptyProgress(userId);
    
    if (!progress.skillProgress[skillId]) {
      progress.skillProgress[skillId] = {
        skillId,
        currentLessonIndex: 0,
        completedLessons: [],
        lessonProgress: {},
        totalXP: 0,
        lastAccessed: Date.now(),
      };
    }

    const skillProgress = progress.skillProgress[skillId];
    
    if (!skillProgress.lessonProgress[lessonId]) {
      skillProgress.lessonProgress[lessonId] = {
        lessonId,
        isCompleted: false,
        completedExercises: [],
        exerciseAnswers: {},
        attempts: 0,
        bestScore: 0,
        timeSpent: 0,
      };
    }

    skillProgress.lessonProgress[lessonId] = {
      ...skillProgress.lessonProgress[lessonId],
      ...updates,
    };

    // Update completed lessons list if lesson is completed
    if (updates.isCompleted && !skillProgress.completedLessons.includes(lessonId)) {
      skillProgress.completedLessons.push(lessonId);
    }

    await this.saveUserProgress(progress);
  }

  // Offline Courses Methods
  async getOfflineCourses(): Promise<OfflineCourse[]> {
    const courses = await this.getItem<OfflineCourse[]>(STORAGE_KEYS.OFFLINE_COURSES);
    return courses || [];
  }

  async saveOfflineCourse(course: OfflineCourse): Promise<void> {
    const courses = await this.getOfflineCourses();
    const existingIndex = courses.findIndex(c => c.skill.id === course.skill.id);
    
    if (existingIndex >= 0) {
      courses[existingIndex] = course;
    } else {
      courses.push(course);
    }
    
    await this.setItem(STORAGE_KEYS.OFFLINE_COURSES, courses);
  }

  async removeOfflineCourse(skillId: string): Promise<void> {
    const courses = await this.getOfflineCourses();
    const filtered = courses.filter(c => c.skill.id !== skillId);
    await this.setItem(STORAGE_KEYS.OFFLINE_COURSES, filtered);
  }

  // Global Stats Methods
  async updateGlobalStats(userId: string, updates: Partial<GlobalStats>): Promise<void> {
    const progress = await this.getUserProgress(userId) || this.createEmptyProgress(userId);
    
    progress.globalStats = {
      ...progress.globalStats,
      ...updates,
    };

    await this.saveUserProgress(progress);
  }

  // Sync Methods
  async getLastSyncTimestamp(): Promise<number> {
    const timestamp = await this.getItem<number>(STORAGE_KEYS.LAST_SYNC);
    return timestamp || 0;
  }

  async setLastSyncTimestamp(timestamp: number): Promise<void> {
    await this.setItem(STORAGE_KEYS.LAST_SYNC, timestamp);
  }

  async getPendingSyncData(): Promise<PendingSyncData | null> {
    return await this.getItem<PendingSyncData>(STORAGE_KEYS.PENDING_SYNC);
  }

  async setPendingSyncData(data: PendingSyncData): Promise<void> {
    await this.setItem(STORAGE_KEYS.PENDING_SYNC, data);
  }

  async clearPendingSyncData(): Promise<void> {
    await this.removeItem(STORAGE_KEYS.PENDING_SYNC);
  }

  // Utility Methods
  private createEmptyProgress(userId: string): LocalUserProgress {
    return {
      userId,
      skillProgress: {},
      globalStats: {
        totalXP: 0,
        currentStreak: 0,
        longestStreak: 0,
        totalLessonsCompleted: 0,
        totalExercisesCompleted: 0,
        totalTimeSpent: 0,
        lastActiveDate: new Date().toISOString().split('T')[0],
        streakFreezeUsed: 0,
        achievements: [],
      },
      lastUpdated: Date.now(),
      version: '1.0.0',
    };
  }

  async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.clear();
      this.cache.clear();
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  }

  async getStorageSize(): Promise<number> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      let totalSize = 0;

      for (const key of keys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          totalSize += value.length;
        }
      }

      return totalSize;
    } catch (error) {
      console.error('Failed to calculate storage size:', error);
      return 0;
    }
  }


}

export const localStorageService = new LocalStorageService();
