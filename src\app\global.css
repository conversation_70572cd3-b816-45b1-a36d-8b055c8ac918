@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary Colors */
  --color-primary-50: #f0f9f1;
  --color-primary-100: #dcf3df;
  --color-primary-200: #b9e6c0;
  --color-primary-300: #8ed59a;
  --color-primary-400: #5abe70;
  --color-primary-500: #3da450;
  --color-primary-600: #2a833c;
  --color-primary-700: #236833;
  --color-primary-800: #1d512a;
  --color-primary-900: #184324;
  --color-primary-950: #0c2515;

  /* Secondary Colors */
  --color-secondary-50: #f8f6f1;
  --color-secondary-100: #efe9e1;
  --color-secondary-200: #ded0c3;
  --color-secondary-300: #cbb39e;
  --color-secondary-400: #b79274;
  --color-secondary-500: #a77b59;
  --color-secondary-600: #95674a;
  --color-secondary-700: #7c533e;
  --color-secondary-800: #664536;
  --color-secondary-900: #543a30;
  --color-secondary-950: #2d1e19;

  /* Success Colors */
  --color-success-50: #ecfdf5;
  --color-success-100: #d1fae5;
  --color-success-200: #a7f3d0;
  --color-success-300: #6ee7b7;
  --color-success-400: #34d399;
  --color-success-500: #10b981;
  --color-success-600: #059669;
  --color-success-700: #047857;
  --color-success-800: #065f46;
  --color-success-900: #064e3b;
  --color-success-950: #022c22;

  /* Error Colors */
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;
  --color-error-950: #450a0a;

  /* Warning Colors */
  --color-warning-50: #fff9ec;
  --color-warning-100: #ffeac2;
  --color-warning-200: #fed58b;
  --color-warning-300: #fec554;
  --color-warning-400: #fdb022;
  --color-warning-500: #f79009;
  --color-warning-600: #dc6803;
  --color-warning-700: #b54708;
  --color-warning-800: #923a0e;
  --color-warning-900: #78310f;
  --color-warning-950: #451a03;

  /* Gray Colors */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;

  /* Light Theme Semantic Colors */
  --color-background: #f9faf8;
  --color-text: #111827;
  --color-secondary-text: #4b5563;
  --color-card: #ffffff;
  --color-border: #dcf3df;
  --color-input-background: #ffffff;
  --color-muted: #f0f9f1;
  --color-accent: #3da450;
}

[data-theme="dark"] {
  /* Dark Theme Semantic Colors */
  --color-background: #121212;
  --color-text: #f9fafb;
  --color-secondary-text: #9ca3af;
  --color-card: #1e1e1e;
  --color-border: #2c2c2c;
  --color-input-background: #333333;
  --color-muted: #282828;
  --color-accent: #3da450;
}