import { Subject, Topic, Skill, UUID } from '@/types/skill';
import {
  SUBJECTS_TABLE,
  TOPICS_TABLE,
  SK<PERSON>LS_TABLE,
  SubjectRecord,
  TopicRecord,
  SkillRecord,
  DatabaseQueries,
  SKILL_FILES
} from '@/fixtures/devHierarchyData';

// Environment detection
declare const __DEV__: boolean;

export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface HierarchyDataService {
  // Core data loading methods
  getSubjects(): Promise<Subject[]>;
  getTopicsBySubjectId(subjectId: UUID): Promise<Topic[]>;
  getSkillsByTopicId(topicId: UUID): Promise<SkillRecord[]>;
  getSkillContent(skillId: UUID): Promise<Skill | null>;

  // Individual entity getters
  getSubjectById(id: UUID): Promise<Subject | null>;
  getTopicById(id: UUID): Promise<Topic | null>;
  getSkillMetadataById(id: UUID): Promise<SkillRecord | null>;

  // Utility methods
  isTopicUnlocked(topicId: UUID, completedTopics: UUID[]): Promise<boolean>;
  getNextUnlockedTopic(subjectId: UUID, completedTopics: UUID[]): Promise<Topic | null>;
  getNextUnlockedSkill(topicId: UUID, completedSkills: UUID[]): Promise<SkillRecord | null>;

  // Database-like query methods
  searchSkills(query: string): Promise<SkillRecord[]>;
  searchTopics(query: string): Promise<TopicRecord[]>;
  getSkillsByTag(tag: string): Promise<SkillRecord[]>;
}

class DevelopmentHierarchyDataService implements HierarchyDataService {
  private cache: Map<string, any> = new Map();

  async getSubjects(): Promise<Subject[]> {
    // Simulate API delay in development
    await this.simulateDelay();

    // Convert SubjectRecord to Subject format (add empty topics array for compatibility)
    const subjects = DatabaseQueries.getAllSubjects();
    return subjects.map(subject => ({
      ...subject,
      topics: [] // Will be populated dynamically when needed
    }));
  }

  async getTopicsBySubjectId(subjectId: UUID): Promise<Topic[]> {
    await this.simulateDelay();

    // Use database query to get topics by subject ID
    const topics = DatabaseQueries.getTopicsBySubjectId(subjectId);
    return topics.map(topic => ({
      ...topic,
      skills: [] // Will be populated dynamically when needed
    }));
  }

  async getSkillsByTopicId(topicId: UUID): Promise<SkillRecord[]> {
    await this.simulateDelay();

    // Use database query to get skills by topic ID
    return DatabaseQueries.getSkillsByTopicId(topicId);
  }

  async getSkillContent(skillId: UUID): Promise<Skill | null> {
    const cacheKey = `skill_content_${skillId}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    await this.simulateDelay();

    try {
      // Use database query to find the skill metadata
      const skillMetadata = DatabaseQueries.getSkillById(skillId);
      if (!skillMetadata) {
        console.warn(`Skill metadata not found for ID: ${skillId}`);
        return null;
      }

      // Dynamically import the skill JSON file
      let skillContent: Skill;

      switch (skillMetadata.fileName) {
        case 'dummySkill.json':
          skillContent = (await import('@/fixtures/dummySkill.json')).default;
          break;
        case 'better.json':
          skillContent = (await import('@/fixtures/better.json')).default;
          break;
        case 'calculous.json':
          skillContent = (await import('@/fixtures/calculous.json')).default;
          break;
        default:
          console.warn(`Unknown skill file: ${skillMetadata.fileName}`);
          return null;
      }

      // Ensure the skill has the correct metadata
      const enrichedSkill: Skill = {
        ...skillContent,
        id: skillId, // Use the metadata ID
        name: skillMetadata.name,
        description: skillMetadata.description,
        topicId: skillMetadata.topicId,
        order: skillMetadata.order,
        estimatedDuration: skillMetadata.estimatedDuration,
        difficulty: skillMetadata.difficulty,
      };

      this.cache.set(cacheKey, enrichedSkill);
      return enrichedSkill;
    } catch (error) {
      console.error(`Error loading skill content for ${skillId}:`, error);
      return null;
    }
  }

  async getSubjectById(id: UUID): Promise<Subject | null> {
    await this.simulateDelay();

    const subject = DatabaseQueries.getSubjectById(id);
    if (!subject) return null;

    return {
      ...subject,
      topics: [] // Will be populated dynamically when needed
    };
  }

  async getTopicById(id: UUID): Promise<Topic | null> {
    await this.simulateDelay();

    const topic = DatabaseQueries.getTopicById(id);
    if (!topic) return null;

    return {
      ...topic,
      skills: [] // Will be populated dynamically when needed
    };
  }

  async getSkillMetadataById(id: UUID): Promise<SkillRecord | null> {
    await this.simulateDelay();

    return DatabaseQueries.getSkillById(id);
  }

  async isTopicUnlocked(topicId: UUID, completedTopics: UUID[]): Promise<boolean> {
    const topic = DatabaseQueries.getTopicById(topicId);
    if (!topic || !topic.prerequisites || topic.prerequisites.length === 0) {
      return true; // No prerequisites
    }

    // Check if all prerequisites are completed
    return topic.prerequisites.every(prereqId => completedTopics.includes(prereqId));
  }

  async getNextUnlockedTopic(subjectId: UUID, completedTopics: UUID[]): Promise<Topic | null> {
    const topics = await this.getTopicsBySubjectId(subjectId);

    for (const topic of topics) {
      const isUnlocked = await this.isTopicUnlocked(topic.id, completedTopics);
      const isCompleted = completedTopics.includes(topic.id);

      if (isUnlocked && !isCompleted) {
        return topic;
      }
    }

    return null; // All topics completed or none unlocked
  }

  async getNextUnlockedSkill(topicId: UUID, completedSkills: UUID[]): Promise<SkillRecord | null> {
    const skills = await this.getSkillsByTopicId(topicId);

    for (const skill of skills) {
      if (!completedSkills.includes(skill.id)) {
        return skill;
      }
    }

    return null; // All skills completed
  }

  // New database-like query methods
  async searchSkills(query: string): Promise<SkillRecord[]> {
    await this.simulateDelay();
    return DatabaseQueries.searchSkills(query);
  }

  async searchTopics(query: string): Promise<TopicRecord[]> {
    await this.simulateDelay();
    return DatabaseQueries.searchTopics(query);
  }

  async getSkillsByTag(tag: string): Promise<SkillRecord[]> {
    await this.simulateDelay();
    return DatabaseQueries.getSkillsByTag(tag);
  }

  private async simulateDelay(ms: number = 100): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

class ProductionHierarchyDataService implements HierarchyDataService {
  private cache: Map<string, any> = new Map();
  private convexClient: any; // ConvexReactClient type

  constructor(convexClient?: any) {
    this.convexClient = convexClient;
  }

  async getSubjects(): Promise<Subject[]> {
    const cacheKey = 'subjects';

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      if (this.convexClient) {
        // Use Convex client
        const subjects = await this.convexClient.query('subjects:getAllSubjects');
        // Convert to Subject format (add empty topics array for compatibility)
        const formattedSubjects = subjects.map((subject: any) => ({
          ...subject,
          id: subject._id,
          topics: [] // Will be populated dynamically when needed
        }));
        this.cache.set(cacheKey, formattedSubjects);
        return formattedSubjects;
      } else {
        // Fallback to REST API
        const response = await fetch('/api/subjects');
        if (!response.ok) {
          throw new Error(`Failed to fetch subjects: ${response.statusText}`);
        }

        const subjects: Subject[] = await response.json();
        this.cache.set(cacheKey, subjects);
        return subjects;
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
      // Fallback to development data in case of API failure
      console.warn('Falling back to development data');
      return new DevelopmentHierarchyDataService().getSubjects();
    }
  }

  async getTopicsBySubjectId(subjectId: UUID): Promise<Topic[]> {
    const cacheKey = `topics_${subjectId}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      if (this.convexClient) {
        // Use Convex client - get topics from embedded subject data
        const topics = await this.convexClient.query('subjects:getTopicsBySubjectId', { subjectId });
        // Convert embedded topics to Topic format (add empty skills array for compatibility)
        const formattedTopics = topics.map((topic: any) => ({
          ...topic,
          skills: [] // Will be populated dynamically when needed
        }));
        this.cache.set(cacheKey, formattedTopics);
        return formattedTopics;
      } else {
        // Fallback to REST API
        const response = await fetch(`/api/subjects/${subjectId}/topics`);
        if (!response.ok) {
          throw new Error(`Failed to fetch topics: ${response.statusText}`);
        }

        const topics: Topic[] = await response.json();
        this.cache.set(cacheKey, topics);
        return topics;
      }
    } catch (error) {
      console.error('Error fetching topics:', error);
      // Fallback to development data
      return new DevelopmentHierarchyDataService().getTopicsBySubjectId(subjectId);
    }
  }

  async getSkillsByTopicId(topicId: UUID): Promise<SkillRecord[]> {
    const cacheKey = `skills_${topicId}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      if (this.convexClient) {
        // For Convex, we need to find the subject that contains this topic
        // This is more complex with embedded data, so we'll search across subjects
        const skillData = await this.convexClient.query('subjects:findSkillsByTopicId', { topicId });
        this.cache.set(cacheKey, skillData || []);
        return skillData || [];
      } else {
        // Fallback to REST API
        const response = await fetch(`/api/topics/${topicId}/skills`);
        if (!response.ok) {
          throw new Error(`Failed to fetch skills: ${response.statusText}`);
        }

        const skills: SkillRecord[] = await response.json();
        this.cache.set(cacheKey, skills);
        return skills;
      }
    } catch (error) {
      console.error('Error fetching skills:', error);
      // Fallback to development data
      return new DevelopmentHierarchyDataService().getSkillsByTopicId(topicId);
    }
  }

  async getSkillContent(skillId: UUID): Promise<Skill | null> {
    const cacheKey = `skill_content_${skillId}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const response = await fetch(`${this.baseUrl}/skills/${skillId}/content`);
      if (!response.ok) {
        throw new Error(`Failed to fetch skill content: ${response.statusText}`);
      }
      
      const skill: Skill = await response.json();
      this.cache.set(cacheKey, skill);
      return skill;
    } catch (error) {
      console.error('Error fetching skill content:', error);
      // Fallback to development data
      return new DevelopmentHierarchyDataService().getSkillContent(skillId);
    }
  }

  // Implement other methods similar to development service but with API calls
  async getSubjectById(id: UUID): Promise<Subject | null> {
    const subjects = await this.getSubjects();
    return subjects.find(subject => subject.id === id) || null;
  }

  async getTopicById(id: UUID): Promise<Topic | null> {
    try {
      const response = await fetch(`${this.baseUrl}/topics/${id}`);
      if (!response.ok) return null;
      return await response.json();
    } catch (error) {
      console.error('Error fetching topic:', error);
      return new DevelopmentHierarchyDataService().getTopicById(id);
    }
  }

  async getSkillMetadataById(id: UUID): Promise<SkillRecord | null> {
    try {
      const response = await fetch(`${this.baseUrl}/skills/${id}/metadata`);
      if (!response.ok) return null;
      return await response.json();
    } catch (error) {
      console.error('Error fetching skill metadata:', error);
      return new DevelopmentHierarchyDataService().getSkillMetadataById(id);
    }
  }

  async isTopicUnlocked(topicId: UUID, completedTopics: UUID[]): Promise<boolean> {
    return new DevelopmentHierarchyDataService().isTopicUnlocked(topicId, completedTopics);
  }

  async getNextUnlockedTopic(subjectId: UUID, completedTopics: UUID[]): Promise<Topic | null> {
    return new DevelopmentHierarchyDataService().getNextUnlockedTopic(subjectId, completedTopics);
  }

  async getNextUnlockedSkill(topicId: UUID, completedSkills: UUID[]): Promise<SkillRecord | null> {
    return new DevelopmentHierarchyDataService().getNextUnlockedSkill(topicId, completedSkills);
  }

  // New database-like query methods
  async searchSkills(query: string): Promise<SkillRecord[]> {
    try {
      if (this.convexClient) {
        // Use Convex client for embedded search
        return await this.convexClient.query('subjects:searchSkills', { query });
      } else {
        // Fallback to REST API
        const response = await fetch(`/api/skills/search?q=${encodeURIComponent(query)}`);
        if (!response.ok) throw new Error('Search failed');
        return await response.json();
      }
    } catch (error) {
      console.error('Error searching skills:', error);
      return new DevelopmentHierarchyDataService().searchSkills(query);
    }
  }

  async searchTopics(query: string): Promise<TopicRecord[]> {
    try {
      if (this.convexClient) {
        // Use Convex client for embedded search
        return await this.convexClient.query('subjects:searchTopics', { query });
      } else {
        // Fallback to REST API
        const response = await fetch(`/api/topics/search?q=${encodeURIComponent(query)}`);
        if (!response.ok) throw new Error('Search failed');
        return await response.json();
      }
    } catch (error) {
      console.error('Error searching topics:', error);
      return new DevelopmentHierarchyDataService().searchTopics(query);
    }
  }

  async getSkillsByTag(tag: string): Promise<SkillRecord[]> {
    try {
      if (this.convexClient) {
        // Use Convex client for embedded tag search
        return await this.convexClient.query('subjects:getSkillsByTag', { tag });
      } else {
        // Fallback to REST API
        const response = await fetch(`/api/skills/by-tag/${encodeURIComponent(tag)}`);
        if (!response.ok) throw new Error('Tag query failed');
        return await response.json();
      }
    } catch (error) {
      console.error('Error fetching skills by tag:', error);
      return new DevelopmentHierarchyDataService().getSkillsByTag(tag);
    }
  }
}

// Factory function to create the appropriate service
export function createHierarchyDataService(convexClient?: any): HierarchyDataService {
  if (__DEV__ || process.env.NODE_ENV === 'development') {
    console.log('[HierarchyDataService] Using development mode with dummy data');
    return new DevelopmentHierarchyDataService();
  } else {
    console.log('[HierarchyDataService] Using production mode with Convex/API calls');
    return new ProductionHierarchyDataService(convexClient);
  }
}

// Singleton instance
export const hierarchyDataService = createHierarchyDataService();
