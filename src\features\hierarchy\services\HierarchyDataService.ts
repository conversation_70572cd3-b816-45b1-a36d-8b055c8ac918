import { Subject, Topic, Skill, UUID } from '@/types/skill';
import { 
  DEV_SUBJECTS, 
  DEV_TOPICS, 
  DEV_SKILLS, 
  SkillMetadata,
  SKILL_FILES 
} from '@/fixtures/devHierarchyData';

// Environment detection
declare const __DEV__: boolean;

export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface HierarchyDataService {
  // Core data loading methods
  getSubjects(): Promise<Subject[]>;
  getTopicsBySubjectId(subjectId: UUID): Promise<Topic[]>;
  getSkillsByTopicId(topicId: UUID): Promise<SkillMetadata[]>;
  getSkillContent(skillId: UUID): Promise<Skill | null>;
  
  // Individual entity getters
  getSubjectById(id: UUID): Promise<Subject | null>;
  getTopicById(id: UUID): Promise<Topic | null>;
  getSkillMetadataById(id: UUID): Promise<SkillMetadata | null>;
  
  // Utility methods
  isTopicUnlocked(topicId: UUID, completedTopics: UUID[]): Promise<boolean>;
  getNextUnlockedTopic(subjectId: UUID, completedTopics: UUID[]): Promise<Topic | null>;
  getNextUnlockedSkill(topicId: UUID, completedSkills: UUID[]): Promise<SkillMetadata | null>;
}

class DevelopmentHierarchyDataService implements HierarchyDataService {
  private cache: Map<string, any> = new Map();

  async getSubjects(): Promise<Subject[]> {
    // Simulate API delay in development
    await this.simulateDelay();
    return [...DEV_SUBJECTS].sort((a, b) => a.order - b.order);
  }

  async getTopicsBySubjectId(subjectId: UUID): Promise<Topic[]> {
    await this.simulateDelay();
    const topics = DEV_TOPICS[subjectId] || [];
    return [...topics].sort((a, b) => a.order - b.order);
  }

  async getSkillsByTopicId(topicId: UUID): Promise<SkillMetadata[]> {
    await this.simulateDelay();
    const skills = DEV_SKILLS[topicId] || [];
    return [...skills].sort((a, b) => a.order - b.order);
  }

  async getSkillContent(skillId: UUID): Promise<Skill | null> {
    const cacheKey = `skill_content_${skillId}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    await this.simulateDelay();

    try {
      // Find the skill metadata to get the file name
      const skillMetadata = await this.getSkillMetadataById(skillId);
      if (!skillMetadata) {
        console.warn(`Skill metadata not found for ID: ${skillId}`);
        return null;
      }

      // Dynamically import the skill JSON file
      let skillContent: Skill;
      
      switch (skillMetadata.fileName) {
        case 'dummySkill.json':
          skillContent = (await import('@/fixtures/dummySkill.json')).default;
          break;
        case 'better.json':
          skillContent = (await import('@/fixtures/better.json')).default;
          break;
        case 'calculous.json':
          skillContent = (await import('@/fixtures/calculous.json')).default;
          break;
        default:
          console.warn(`Unknown skill file: ${skillMetadata.fileName}`);
          return null;
      }

      // Ensure the skill has the correct metadata
      const enrichedSkill: Skill = {
        ...skillContent,
        id: skillId, // Use the metadata ID
        name: skillMetadata.name,
        description: skillMetadata.description,
        topicId: skillMetadata.topicId,
        order: skillMetadata.order,
        estimatedDuration: skillMetadata.estimatedDuration,
        difficulty: skillMetadata.difficulty,
      };

      this.cache.set(cacheKey, enrichedSkill);
      return enrichedSkill;
    } catch (error) {
      console.error(`Error loading skill content for ${skillId}:`, error);
      return null;
    }
  }

  async getSubjectById(id: UUID): Promise<Subject | null> {
    const subjects = await this.getSubjects();
    return subjects.find(subject => subject.id === id) || null;
  }

  async getTopicById(id: UUID): Promise<Topic | null> {
    // Search through all subjects' topics
    for (const subjectId of Object.keys(DEV_TOPICS)) {
      const topics = await this.getTopicsBySubjectId(subjectId);
      const topic = topics.find(t => t.id === id);
      if (topic) return topic;
    }
    return null;
  }

  async getSkillMetadataById(id: UUID): Promise<SkillMetadata | null> {
    // Search through all topics' skills
    for (const topicId of Object.keys(DEV_SKILLS)) {
      const skills = await this.getSkillsByTopicId(topicId);
      const skill = skills.find(s => s.id === id);
      if (skill) return skill;
    }
    return null;
  }

  async isTopicUnlocked(topicId: UUID, completedTopics: UUID[]): Promise<boolean> {
    const topic = await this.getTopicById(topicId);
    if (!topic || !topic.prerequisites || topic.prerequisites.length === 0) {
      return true; // No prerequisites
    }

    // Check if all prerequisites are completed
    return topic.prerequisites.every(prereqId => completedTopics.includes(prereqId));
  }

  async getNextUnlockedTopic(subjectId: UUID, completedTopics: UUID[]): Promise<Topic | null> {
    const topics = await this.getTopicsBySubjectId(subjectId);
    
    for (const topic of topics) {
      const isUnlocked = await this.isTopicUnlocked(topic.id, completedTopics);
      const isCompleted = completedTopics.includes(topic.id);
      
      if (isUnlocked && !isCompleted) {
        return topic;
      }
    }
    
    return null; // All topics completed or none unlocked
  }

  async getNextUnlockedSkill(topicId: UUID, completedSkills: UUID[]): Promise<SkillMetadata | null> {
    const skills = await this.getSkillsByTopicId(topicId);
    
    for (const skill of skills) {
      if (!completedSkills.includes(skill.id)) {
        return skill;
      }
    }
    
    return null; // All skills completed
  }

  private async simulateDelay(ms: number = 100): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

class ProductionHierarchyDataService implements HierarchyDataService {
  private cache: Map<string, any> = new Map();
  private baseUrl: string;

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl;
  }

  async getSubjects(): Promise<Subject[]> {
    const cacheKey = 'subjects';
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const response = await fetch(`${this.baseUrl}/subjects`);
      if (!response.ok) {
        throw new Error(`Failed to fetch subjects: ${response.statusText}`);
      }
      
      const subjects: Subject[] = await response.json();
      this.cache.set(cacheKey, subjects);
      return subjects;
    } catch (error) {
      console.error('Error fetching subjects:', error);
      // Fallback to development data in case of API failure
      console.warn('Falling back to development data');
      return new DevelopmentHierarchyDataService().getSubjects();
    }
  }

  async getTopicsBySubjectId(subjectId: UUID): Promise<Topic[]> {
    const cacheKey = `topics_${subjectId}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const response = await fetch(`${this.baseUrl}/subjects/${subjectId}/topics`);
      if (!response.ok) {
        throw new Error(`Failed to fetch topics: ${response.statusText}`);
      }
      
      const topics: Topic[] = await response.json();
      this.cache.set(cacheKey, topics);
      return topics;
    } catch (error) {
      console.error('Error fetching topics:', error);
      // Fallback to development data
      return new DevelopmentHierarchyDataService().getTopicsBySubjectId(subjectId);
    }
  }

  async getSkillsByTopicId(topicId: UUID): Promise<SkillMetadata[]> {
    const cacheKey = `skills_${topicId}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const response = await fetch(`${this.baseUrl}/topics/${topicId}/skills`);
      if (!response.ok) {
        throw new Error(`Failed to fetch skills: ${response.statusText}`);
      }
      
      const skills: SkillMetadata[] = await response.json();
      this.cache.set(cacheKey, skills);
      return skills;
    } catch (error) {
      console.error('Error fetching skills:', error);
      // Fallback to development data
      return new DevelopmentHierarchyDataService().getSkillsByTopicId(topicId);
    }
  }

  async getSkillContent(skillId: UUID): Promise<Skill | null> {
    const cacheKey = `skill_content_${skillId}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const response = await fetch(`${this.baseUrl}/skills/${skillId}/content`);
      if (!response.ok) {
        throw new Error(`Failed to fetch skill content: ${response.statusText}`);
      }
      
      const skill: Skill = await response.json();
      this.cache.set(cacheKey, skill);
      return skill;
    } catch (error) {
      console.error('Error fetching skill content:', error);
      // Fallback to development data
      return new DevelopmentHierarchyDataService().getSkillContent(skillId);
    }
  }

  // Implement other methods similar to development service but with API calls
  async getSubjectById(id: UUID): Promise<Subject | null> {
    const subjects = await this.getSubjects();
    return subjects.find(subject => subject.id === id) || null;
  }

  async getTopicById(id: UUID): Promise<Topic | null> {
    try {
      const response = await fetch(`${this.baseUrl}/topics/${id}`);
      if (!response.ok) return null;
      return await response.json();
    } catch (error) {
      console.error('Error fetching topic:', error);
      return new DevelopmentHierarchyDataService().getTopicById(id);
    }
  }

  async getSkillMetadataById(id: UUID): Promise<SkillMetadata | null> {
    try {
      const response = await fetch(`${this.baseUrl}/skills/${id}/metadata`);
      if (!response.ok) return null;
      return await response.json();
    } catch (error) {
      console.error('Error fetching skill metadata:', error);
      return new DevelopmentHierarchyDataService().getSkillMetadataById(id);
    }
  }

  async isTopicUnlocked(topicId: UUID, completedTopics: UUID[]): Promise<boolean> {
    return new DevelopmentHierarchyDataService().isTopicUnlocked(topicId, completedTopics);
  }

  async getNextUnlockedTopic(subjectId: UUID, completedTopics: UUID[]): Promise<Topic | null> {
    return new DevelopmentHierarchyDataService().getNextUnlockedTopic(subjectId, completedTopics);
  }

  async getNextUnlockedSkill(topicId: UUID, completedSkills: UUID[]): Promise<SkillMetadata | null> {
    return new DevelopmentHierarchyDataService().getNextUnlockedSkill(topicId, completedSkills);
  }
}

// Factory function to create the appropriate service
export function createHierarchyDataService(): HierarchyDataService {
  if (__DEV__ || process.env.NODE_ENV === 'development') {
    console.log('[HierarchyDataService] Using development mode with dummy data');
    return new DevelopmentHierarchyDataService();
  } else {
    console.log('[HierarchyDataService] Using production mode with API calls');
    return new ProductionHierarchyDataService();
  }
}

// Singleton instance
export const hierarchyDataService = createHierarchyDataService();
