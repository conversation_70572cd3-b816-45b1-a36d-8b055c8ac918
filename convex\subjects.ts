import { v } from 'convex/values';
import { query, mutation } from './_generated/server';
import { Id } from './_generated/dataModel';

// ==================== SUBJECT QUERIES (SINGLE TABLE DESIGN) ====================

export const getAllSubjects = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('subjects')
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();
  },
});

export const getSubjectById = query({
  args: { id: v.id('subjects') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

export const getSubjectByUUID = query({
  args: { uuid: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('subjects')
      .withIndex('byId', (q) => q.eq('id', args.uuid))
      .filter((q) => q.eq(q.field('isActive'), true))
      .first();
  },
});

export const getSubjectsByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('subjects')
      .withIndex('byCategory', (q) => q.eq('category', args.category))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();
  },
});

// ==================== TOPIC EXTRACTION FROM EMBEDDED DATA ====================

export const getTopicsBySubjectId = query({
  args: { subjectId: v.id('subjects') },
  handler: async (ctx, args) => {
    const subject = await ctx.db.get(args.subjectId);
    if (!subject || !subject.isActive) return [];

    // Return embedded topics, filtered by isActive
    return subject.topics
      .filter(topic => topic.isActive !== false)
      .sort((a, b) => a.order - b.order);
  },
});

export const getTopicsBySubjectUUID = query({
  args: { subjectUUID: v.string() },
  handler: async (ctx, args) => {
    const subject = await ctx.db
      .query('subjects')
      .withIndex('byId', (q) => q.eq('id', args.subjectUUID))
      .filter((q) => q.eq(q.field('isActive'), true))
      .first();

    if (!subject) return [];

    // Return embedded topics, filtered by isActive
    return subject.topics
      .filter(topic => topic.isActive !== false)
      .sort((a, b) => a.order - b.order);
  },
});

export const getTopicById = query({
  args: {
    subjectId: v.id('subjects'),
    topicId: v.string(),
  },
  handler: async (ctx, args) => {
    const subject = await ctx.db.get(args.subjectId);
    if (!subject || !subject.isActive) return null;

    return subject.topics.find(topic =>
      topic.id === args.topicId && topic.isActive !== false
    ) || null;
  },
});

// ==================== SKILL EXTRACTION FROM EMBEDDED DATA ====================

export const getSkillsByTopicId = query({
  args: {
    subjectId: v.id('subjects'),
    topicId: v.string(),
  },
  handler: async (ctx, args) => {
    const subject = await ctx.db.get(args.subjectId);
    if (!subject || !subject.isActive) return [];

    const topic = subject.topics.find(t => t.id === args.topicId && t.isActive !== false);
    if (!topic) return [];

    // Return embedded skills, filtered by isActive
    return topic.skills
      .filter(skill => skill.isActive !== false)
      .sort((a, b) => a.order - b.order);
  },
});

export const getSkillById = query({
  args: {
    subjectId: v.id('subjects'),
    topicId: v.string(),
    skillId: v.string(),
  },
  handler: async (ctx, args) => {
    const subject = await ctx.db.get(args.subjectId);
    if (!subject || !subject.isActive) return null;

    const topic = subject.topics.find(t => t.id === args.topicId && t.isActive !== false);
    if (!topic) return null;

    return topic.skills.find(skill =>
      skill.id === args.skillId && skill.isActive !== false
    ) || null;
  },
});

// Find skill across all subjects (for UUID-based lookups)
export const findSkillByUUID = query({
  args: { skillId: v.string() },
  handler: async (ctx, args) => {
    const subjects = await ctx.db
      .query('subjects')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    for (const subject of subjects) {
      for (const topic of subject.topics) {
        if (topic.isActive === false) continue;

        const skill = topic.skills.find(s => s.id === args.skillId && s.isActive !== false);
        if (skill) {
          return {
            skill,
            topic,
            subject,
            subjectId: subject._id,
            topicId: topic.id,
          };
        }
      }
    }

    return null;
  },
});

// Find skills by topic ID across all subjects
export const findSkillsByTopicId = query({
  args: { topicId: v.string() },
  handler: async (ctx, args) => {
    const subjects = await ctx.db
      .query('subjects')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    for (const subject of subjects) {
      const topic = subject.topics.find(t => t.id === args.topicId && t.isActive !== false);
      if (topic) {
        return topic.skills.filter(skill => skill.isActive !== false);
      }
    }

    return [];
  },
});

export const getSubjectsByDifficulty = query({
  args: { difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')) },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('subjects')
      .withIndex('byDifficulty', (q) => q.eq('difficulty', args.difficulty))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();
  },
});

export const getPremiumSubjects = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('subjects')
      .withIndex('byPremium', (q) => q.eq('isPremium', true))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();
  },
});

export const getFreeSubjects = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('subjects')
      .withIndex('byPremium', (q) => q.eq('isPremium', false))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();
  },
});

// ==================== SUBJECT MUTATIONS (EMBEDDED STRUCTURE) ====================

export const createSubject = mutation({
  args: {
    id: v.string(), // UUID for subject
    name: v.string(),
    description: v.string(),
    category: v.string(),
    order: v.number(),
    icon: v.string(),
    color: v.string(),
    estimatedDuration: v.number(),
    difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
    isPremium: v.boolean(),
    tags: v.optional(v.array(v.string())),
    topics: v.optional(v.array(v.any())), // Simplified validation
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    return await ctx.db.insert('subjects', {
      ...args,
      topics: args.topics || [],
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

export const updateSubject = mutation({
  args: {
    id: v.id('subjects'),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    category: v.optional(v.string()),
    order: v.optional(v.number()),
    icon: v.optional(v.string()),
    color: v.optional(v.string()),
    estimatedDuration: v.optional(v.number()),
    difficulty: v.optional(v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced'))),
    isPremium: v.optional(v.boolean()),
    isActive: v.optional(v.boolean()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    
    return await ctx.db.patch(id, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

export const deleteSubject = mutation({
  args: { id: v.id('subjects') },
  handler: async (ctx, args) => {
    // Soft delete by setting isActive to false
    return await ctx.db.patch(args.id, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});

// ==================== SEARCH FUNCTIONS (EMBEDDED DATA) ====================

export const searchSubjects = query({
  args: { query: v.string() },
  handler: async (ctx, args) => {
    const subjects = await ctx.db
      .query('subjects')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    const searchTerm = args.query.toLowerCase();

    return subjects.filter(subject =>
      subject.name.toLowerCase().includes(searchTerm) ||
      subject.description.toLowerCase().includes(searchTerm) ||
      subject.category.toLowerCase().includes(searchTerm) ||
      (subject.tags && subject.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
    );
  },
});

export const searchTopics = query({
  args: { query: v.string() },
  handler: async (ctx, args) => {
    const subjects = await ctx.db
      .query('subjects')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    const searchTerm = args.query.toLowerCase();
    const results = [];

    for (const subject of subjects) {
      for (const topic of subject.topics) {
        if (topic.isActive === false) continue;

        if (topic.name.toLowerCase().includes(searchTerm) ||
            topic.description.toLowerCase().includes(searchTerm) ||
            (topic.tags && topic.tags.some(tag => tag.toLowerCase().includes(searchTerm)))) {
          results.push({
            ...topic,
            subjectId: subject._id,
            subjectName: subject.name,
          });
        }
      }
    }

    return results;
  },
});

export const searchSkills = query({
  args: { query: v.string() },
  handler: async (ctx, args) => {
    const subjects = await ctx.db
      .query('subjects')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    const searchTerm = args.query.toLowerCase();
    const results = [];

    for (const subject of subjects) {
      for (const topic of subject.topics) {
        if (topic.isActive === false) continue;

        for (const skill of topic.skills) {
          if (skill.isActive === false) continue;

          if (skill.name.toLowerCase().includes(searchTerm) ||
              skill.description.toLowerCase().includes(searchTerm) ||
              (skill.tags && skill.tags.some(tag => tag.toLowerCase().includes(searchTerm)))) {
            results.push({
              ...skill,
              subjectId: subject._id,
              subjectName: subject.name,
              topicId: topic.id,
              topicName: topic.name,
            });
          }
        }
      }
    }

    return results;
  },
});

export const getSkillsByTag = query({
  args: { tag: v.string() },
  handler: async (ctx, args) => {
    const subjects = await ctx.db
      .query('subjects')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    const results = [];

    for (const subject of subjects) {
      for (const topic of subject.topics) {
        if (topic.isActive === false) continue;

        for (const skill of topic.skills) {
          if (skill.isActive === false) continue;

          if (skill.tags && skill.tags.includes(args.tag)) {
            results.push({
              ...skill,
              subjectId: subject._id,
              subjectName: subject.name,
              topicId: topic.id,
              topicName: topic.name,
            });
          }
        }
      }
    }

    return results;
  },
});

// ==================== SUBJECT WITH RELATIONSHIPS ====================

export const getSubjectWithTopics = query({
  args: { subjectId: v.id('subjects') },
  handler: async (ctx, args) => {
    const subject = await ctx.db.get(args.subjectId);
    if (!subject) return null;

    // Topics are now embedded in the subject
    const topics = subject.topics
      .filter((t: any) => t.isActive !== false)
      .sort((a: any, b: any) => a.order - b.order);

    return {
      subject,
      topics,
    };
  },
});

export const getSubjectWithProgress = query({
  args: { 
    subjectId: v.id('subjects'),
    userId: v.id('users'),
  },
  handler: async (ctx, args) => {
    const subject = await ctx.db.get(args.subjectId);
    if (!subject) return null;

    const progress = await ctx.db
      .query('subjectProgress')
      .withIndex('byUserSubject', (q) => 
        q.eq('userId', args.userId).eq('subjectId', args.subjectId)
      )
      .first();

    return {
      subject,
      progress,
    };
  },
});

// ==================== ANALYTICS ====================

export const getSubjectStats = query({
  args: { subjectId: v.id('subjects') },
  handler: async (ctx, args) => {
    // Get subject with embedded data
    const subject = await ctx.db.get(args.subjectId);
    if (!subject) return null;

    // Count topics and skills from embedded data
    const topics = subject.topics.filter((t: any) => t.isActive !== false);
    let totalSkills = 0;

    for (const topic of topics) {
      const skills = topic.skills.filter((s: any) => s.isActive !== false);
      totalSkills += skills.length;
    }

    // Get enrollment count (users who have progress in this subject)
    const enrollments = await ctx.db
      .query('subjectProgress')
      .withIndex('bySubjectId', (q: any) => q.eq('subjectId', args.subjectId))
      .collect();

    // Get completion count
    const completions = enrollments.filter(p => p.isCompleted).length;

    return {
      totalTopics: topics.length,
      totalSkills,
      enrolledUsers: enrollments.length,
      completedUsers: completions,
      completionRate: enrollments.length > 0 ? (completions / enrollments.length) * 100 : 0,
    };
  },
});

// ==================== BULK OPERATIONS ====================

export const bulkCreateSubjects = mutation({
  args: {
    subjects: v.array(v.object({
      id: v.string(), // Add required id field
      name: v.string(),
      description: v.string(),
      category: v.string(),
      order: v.number(),
      icon: v.string(),
      color: v.string(),
      estimatedDuration: v.number(),
      difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
      isPremium: v.boolean(),
      tags: v.optional(v.array(v.string())),
      topics: v.optional(v.array(v.any())), // Add topics field
    })),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const results = [];

    for (const subject of args.subjects) {
      const docId = await ctx.db.insert('subjects', {
        id: subject.id, // Explicitly set the id
        name: subject.name,
        description: subject.description,
        category: subject.category,
        order: subject.order,
        icon: subject.icon,
        color: subject.color,
        estimatedDuration: subject.estimatedDuration,
        difficulty: subject.difficulty,
        isPremium: subject.isPremium,
        tags: subject.tags,
        topics: subject.topics || [], // Ensure topics array exists
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      results.push(docId);
    }

    return results;
  },
});
