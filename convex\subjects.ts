import { v } from 'convex/values';
import { query, mutation } from './_generated/server';
import { Id } from './_generated/dataModel';

// ==================== SUBJECT QUERIES ====================

export const getAllSubjects = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('subjects')
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();
  },
});

export const getSubjectById = query({
  args: { id: v.id('subjects') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

export const getSubjectsByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('subjects')
      .withIndex('byCategory', (q) => q.eq('category', args.category))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();
  },
});

export const getSubjectsByDifficulty = query({
  args: { difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')) },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('subjects')
      .withIndex('byDifficulty', (q) => q.eq('difficulty', args.difficulty))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();
  },
});

export const getPremiumSubjects = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('subjects')
      .withIndex('byPremium', (q) => q.eq('isPremium', true))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();
  },
});

export const getFreeSubjects = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('subjects')
      .withIndex('byPremium', (q) => q.eq('isPremium', false))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();
  },
});

// ==================== SUBJECT MUTATIONS ====================

export const createSubject = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    category: v.string(),
    order: v.number(),
    icon: v.string(),
    color: v.string(),
    estimatedDuration: v.number(),
    difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
    isPremium: v.boolean(),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    return await ctx.db.insert('subjects', {
      ...args,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

export const updateSubject = mutation({
  args: {
    id: v.id('subjects'),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    category: v.optional(v.string()),
    order: v.optional(v.number()),
    icon: v.optional(v.string()),
    color: v.optional(v.string()),
    estimatedDuration: v.optional(v.number()),
    difficulty: v.optional(v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced'))),
    isPremium: v.optional(v.boolean()),
    isActive: v.optional(v.boolean()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    
    return await ctx.db.patch(id, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

export const deleteSubject = mutation({
  args: { id: v.id('subjects') },
  handler: async (ctx, args) => {
    // Soft delete by setting isActive to false
    return await ctx.db.patch(args.id, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});

// ==================== SUBJECT SEARCH ====================

export const searchSubjects = query({
  args: { query: v.string() },
  handler: async (ctx, args) => {
    const subjects = await ctx.db
      .query('subjects')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    const searchTerm = args.query.toLowerCase();
    
    return subjects.filter(subject => 
      subject.name.toLowerCase().includes(searchTerm) ||
      subject.description.toLowerCase().includes(searchTerm) ||
      subject.category.toLowerCase().includes(searchTerm) ||
      (subject.tags && subject.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
    );
  },
});

// ==================== SUBJECT WITH RELATIONSHIPS ====================

export const getSubjectWithTopics = query({
  args: { subjectId: v.id('subjects') },
  handler: async (ctx, args) => {
    const subject = await ctx.db.get(args.subjectId);
    if (!subject) return null;

    const topics = await ctx.db
      .query('topics')
      .withIndex('bySubject', (q) => q.eq('subjectId', args.subjectId))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc')
      .collect();

    return {
      subject,
      topics,
    };
  },
});

export const getSubjectWithProgress = query({
  args: { 
    subjectId: v.id('subjects'),
    userId: v.id('users'),
  },
  handler: async (ctx, args) => {
    const subject = await ctx.db.get(args.subjectId);
    if (!subject) return null;

    const progress = await ctx.db
      .query('subjectProgress')
      .withIndex('byUserSubject', (q) => 
        q.eq('userId', args.userId).eq('subjectId', args.subjectId)
      )
      .first();

    return {
      subject,
      progress,
    };
  },
});

// ==================== ANALYTICS ====================

export const getSubjectStats = query({
  args: { subjectId: v.id('subjects') },
  handler: async (ctx, args) => {
    // Get total topics count
    const topics = await ctx.db
      .query('topics')
      .withIndex('bySubject', (q) => q.eq('subjectId', args.subjectId))
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    // Get total skills count across all topics
    let totalSkills = 0;
    for (const topic of topics) {
      const skills = await ctx.db
        .query('skills')
        .withIndex('byTopic', (q) => q.eq('topicId', topic._id))
        .filter((q) => q.eq(q.field('isActive'), true))
        .collect();
      totalSkills += skills.length;
    }

    // Get enrollment count (users who have progress in this subject)
    const enrollments = await ctx.db
      .query('subjectProgress')
      .withIndex('bySubject', (q) => q.eq('subjectId', args.subjectId))
      .collect();

    // Get completion count
    const completions = enrollments.filter(p => p.isCompleted).length;

    return {
      totalTopics: topics.length,
      totalSkills,
      enrolledUsers: enrollments.length,
      completedUsers: completions,
      completionRate: enrollments.length > 0 ? (completions / enrollments.length) * 100 : 0,
    };
  },
});

// ==================== BULK OPERATIONS ====================

export const bulkCreateSubjects = mutation({
  args: {
    subjects: v.array(v.object({
      name: v.string(),
      description: v.string(),
      category: v.string(),
      order: v.number(),
      icon: v.string(),
      color: v.string(),
      estimatedDuration: v.number(),
      difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
      isPremium: v.boolean(),
      tags: v.optional(v.array(v.string())),
    })),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const results = [];

    for (const subject of args.subjects) {
      const id = await ctx.db.insert('subjects', {
        ...subject,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      results.push(id);
    }

    return results;
  },
});
