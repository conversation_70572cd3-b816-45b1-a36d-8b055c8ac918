import React from 'react';
import { View, StyleSheet } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StatusBarManager } from './StatusBarManager';
import { useTheme } from '~/lib/theme';

interface AppWrapperProps {
  children: React.ReactNode;
  statusBarStyle?: 'default' | 'translucent' | 'modal';
  statusBarBackgroundColor?: string;
}

/**
 * AppWrapper - Root wrapper component that provides:
 * - Theme-aware status bar management
 * - Gesture handler root
 * - Consistent app-wide styling
 * - Platform-specific optimizations
 */
export const AppWrapper: React.FC<AppWrapperProps> = ({
  children,
  statusBarStyle = 'default',
  statusBarBackgroundColor,
}) => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    gestureHandler: {
      flex: 1,
    },
  });

  const renderStatusBar = () => {
    switch (statusBarStyle) {
      case 'translucent':
        return <StatusBarManager />;
      case 'modal':
        return (
          <StatusBarManager />
        );
      default:
        return <StatusBarManager />;
    }
  };

  return (
    <View style={styles.container}>
      {renderStatusBar()}
      <GestureHandlerRootView style={styles.gestureHandler}>
        {children}
      </GestureHandlerRootView>
    </View>
  );
};

export default AppWrapper;
