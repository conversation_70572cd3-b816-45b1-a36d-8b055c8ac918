import React from 'react';
import { View, Text } from 'react-native';

// 1. Survey Data - Onboarding Flow for User Purpose & Intent, Learning Style, Motivation, and Personalization
export const surveyData = [
  // --- User Purpose & Intent ---
  {
    id: 'welcome',
    type: 'feature',
    step: 'WELCOME',
    icon: '🌱',
    title: 'Unlock Your Health Potential.',
    description: 'Answer a few questions to generate your personalized health plan. It only takes a minute.',
  },
  
  {
    id: 'user-purpose',
    type: 'multiple-select',
    question: 'What are you hoping to achieve in the next 30 days?',
    options: [
      { id: 'habit', text: 'Build a consistent learning habit' },
      { id: 'master', text: 'Master a new topic or skill' },
      { id: 'routine', text: 'Make learning part of my daily routine' },
      { id: 'explore', text: 'Explore different ideas' },
      { id: 'other', text: 'Other', input: { type: 'text', placeholder: 'Other (please specify)' } },
    ],
  },
  {
    id: 'current-mindset',
    type: 'multiple-choice',
    question: 'Which of these best describes your current mindset?',
    options: [
      { id: 'curious', text: "I'm curious and exploring" },
      { id: 'goal', text: 'I have a goal and want to stay focused' },
      { id: 'fun', text: 'I want to make learning fun again' },
      { id: 'insights', text: 'I need short, useful insights for daily life' },
    ],
  },

  // --- Learning Style & Preferences ---
  {
    id: 'learning-formats',
    type: 'multiple-select',
    question: 'Which formats do you enjoy most? (Choose all that apply)',
    options: [
      { id: 'quizzes', text: 'Interactive quizzes' },
      { id: 'flashcards', text: 'Flashcards' },
      { id: 'stories', text: 'Stories or case studies' },
      { id: 'videos', text: 'Videos or animations' },
      { id: 'audio', text: 'Audio lessons' },
      { id: 'challenges', text: 'Challenges or games' },
      { id: 'visuals', text: 'Infographics or visuals' },
    ],
  },
  {
    id: 'learning-pace',
    type: 'multiple-choice',
    question: 'What’s your preferred pace?',
    options: [
      { id: 'fast', text: 'Fast and intensive' },
      { id: 'steady', text: 'Steady and consistent' },
      { id: 'flexible', text: 'Flexible, based on my mood' },
    ],
  },
  {
    id: 'challenge-preference',
    type: 'multiple-choice',
    question: 'Do you like being challenged while learning?',
    options: [
      { id: 'yes', text: 'Yes, push me!' },
      { id: 'little', text: 'A little challenge is good' },
      { id: 'relaxed', text: 'Keep it relaxed and fun' },
    ],
  },

  // --- Learning Interests (Extended) ---
  {
    id: 'favorite-topics',
    type: 'multiple-choice',
    question: 'What topics would you never get bored of learning about?',
    options: [
      { id: 'technology', text: 'Technology' },
      { id: 'science', text: 'Science' },
      { id: 'history', text: 'History' },
      { id: 'art', text: 'Art' },
      { id: 'music', text: 'Music' },
      { id: 'sports', text: 'Sports' },
      { id: 'other', text: 'Other', input: { type: 'text', placeholder: 'Type your favorite topics or select from suggestions' } },
    ],
  },
  {
    id: 'real-world-problems',
    type: 'multiple-choice',
    question: "Are there any real-world problems you're trying to solve?",
    options: [
      { id: 'yes', text: 'Yes', input: { type: 'text', placeholder: 'What problem are you trying to solve?' } },
      { id: 'no', text: "Not really, just learning for fun" },
    ],
  },
  {
    id: 'adaptive-path',
    type: 'multiple-choice',
    question: 'Would you like a learning path that adapts to your mood or energy?',
    options: [
      { id: 'yes', text: 'Yes, that sounds great' },
      { id: 'no', text: 'No, I prefer a fixed routine' },
    ],
  },

  // --- Motivation & Personality ---
  {
    id: 'motivation-style',
    type: 'multiple-select',
    question: 'How do you stay motivated?',
    options: [
      { id: 'progress', text: 'Seeing progress' },
      { id: 'rewards', text: 'Getting rewards or streaks' },
      { id: 'compete', text: 'Competing with others' },
      { id: 'little-every-day', text: 'Just doing a little every day' },
      { id: 'fun', text: 'Having fun' },
    ],
  },
  {
    id: 'failure-attitude',
    type: 'multiple-choice',
    question: 'How do you feel about failure in learning?',
    options: [
      { id: 'embrace', text: 'I embrace mistakes' },
      { id: 'discouraged', text: 'I get discouraged easily' },
      { id: 'avoid', text: 'I try to avoid failure' },
      { id: 'playful', text: 'I learn best when it’s playful' },
    ],
  },
  {
    id: 'learner-type',
    type: 'multiple-choice',
    question: 'Are you more of a...',
    options: [
      { id: 'solo', text: 'Solo learner' },
      { id: 'social', text: 'Social learner' },
      { id: 'depends', text: 'Depends on the topic' },
    ],
  },

  // --- Personalization & Reminders ---
  {
    id: 'personalization-current-events',
    type: 'multiple-choice',
    question: 'Do you want your lessons personalized with current events or trends?',
    options: [
      { id: 'yes', text: 'Yes' },
      { id: 'no', text: 'No' },
      { id: 'maybe', text: 'Maybe later' },
    ],
  },
  {
    id: 'reminder-frequency',
    type: 'multiple-choice',
    question: 'How often do you want to be reminded to learn?',
    options: [
      { id: 'daily', text: 'Daily' },
      { id: 'few-week', text: 'A few times a week' },
      { id: 'weekly', text: 'Weekly' },
      { id: 'falling-behind', text: 'Just when I’m falling behind' },
    ],
  },
  {
    id: 'focus-time',
    type: 'multiple-choice',
    question: 'What time of day are you most focused?',
    options: [
      { id: 'morning', text: 'Morning' },
      { id: 'midday', text: 'Midday' },
      { id: 'evening', text: 'Evening' },
      { id: 'late-night', text: 'Late night' },
      { id: 'varies', text: 'Varies day to day' },
    ],
  },
];

// 2. Styling Configuration - Inspired by Cal AI
export const stylesConfig = {
  container: {
    backgroundColor: '#0D0D0D', // Dark, almost black
  },
  progressSegment: {
    activeColor: '#4CAF50', // A vibrant green
    inactiveColor: 'rgba(255, 255, 255, 0.2)',
  },
  stepIndicator: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 14,
    fontWeight: 'bold',
  },
  title: {
    color: '#FFFFFF',
    fontSize: 34,
    fontWeight: 'bold',
    lineHeight: 42,
  },
  question: {
    color: '#FFFFFF',
    fontSize: 28,
    fontWeight: 'bold',
    lineHeight: 36,
  },
  description: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 18,
    lineHeight: 25,
  },
  button: {
    backgroundColor: '#4CAF50',
    textColor: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold' as 'bold',
  },
  skipButton: {
    textColor: 'rgba(255, 255, 255, 0.6)',
    fontSize: 16,
  },
  backButton: {
    color: 'rgba(255, 255, 255, 0.6)',
  },
  optionCard: {
    iconSize: 28,
    text: {
      fontSize: 18,
    },
    unselected: {
      backgroundColor: '#1A1A1A',
      borderColor: 'transparent',
      textColor: '#FFFFFF',
    },
    selected: {
      backgroundColor: 'rgba(76, 175, 80, 0.15)',
      borderColor: '#4CAF50',
      textColor: '#4CAF50',
    },
  },
};

// 3. Animation Configuration
export const animationConfig = {
  contentTransition: {
    duration: 300,
  },
};

