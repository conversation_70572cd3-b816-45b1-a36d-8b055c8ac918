import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';

interface ImageOption {
  id: string;
  text: string;
  imageUrl: string;
}

interface ImageSelectSlideProps {
  question: string;
  options: ImageOption[];
  value: string | null;
  onChange: (id: string) => void;
  color: string;
}

export function ImageSelectSlide({ question, options, value, onChange, color }: ImageSelectSlideProps) {
  return (
    <View style={{ width: '100%' }}>
      <Text style={styles.question}>{question}</Text>
      <View style={styles.grid}>
        {options.map(option => (
          <TouchableOpacity
            key={option.id}
            style={[styles.card, value === option.id && { borderColor: color, backgroundColor: '#f0f0f0' }]}
            onPress={() => onChange(option.id)}
          >
            <Image source={{ uri: option.imageUrl }} style={styles.image} />
            <Text style={[styles.text, value === option.id && { color }]}>{option.text}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  question: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  card: {
    width: '30%',
    aspectRatio: 0.8,
    borderWidth: 2,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    backgroundColor: '#f7f8fa',
    borderColor: 'transparent',
    padding: 8,
  },
  image: {
    width: 48,
    height: 48,
    marginBottom: 8,
    borderRadius: 8,
  },
  text: {
    fontSize: 16,
    color: '#222',
    fontWeight: '600',
    textAlign: 'center',
  },
}); 