import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, interpolate } from 'react-native-reanimated';
import { Link, Redirect, useRouter } from 'expo-router';
import { surveyData } from './config';
import { useColorScheme } from 'lib/useColorScheme';
import { OptionCard } from './OptionCard';
import { ProgressBar } from './ProgressBar';
import { BackButton } from './BackButton';
import { SliderSlide } from './SliderSlide';
import { InputSlide } from './InputSlide';
import { YesNoSlide } from './YesNoSlide';
import { ImageSelectSlide } from './ImageSelectSlide';
import { ToggleGroupSlide } from './ToggleGroupSlide';
import { RankingSlide } from './RankingSlide';
import { AnimatedSlide } from './AnimatedSlide';
import { PermissionSlide } from './PermissionSlide';
import { useOnboardingState } from 'lib/onboardingState';
import { FeatureSlide } from './FeatureSlide';
import { MultipleChoiceSlide } from './MultipleChoiceSlide';
import { MultipleSelectSlide } from './MultipleSelectSlide';

const { width } = Dimensions.get('window');

// Helper to get default value for each slide type
function getDefaultValue(slide: any) {
  switch (slide.type) {
    case 'slider': {
      let def = (typeof slide.default === 'number' && !isNaN(slide.default)) ? slide.default : undefined;
      let min = (typeof slide.min === 'number' && !isNaN(slide.min)) ? slide.min : 0;
      let max = (typeof slide.max === 'number' && !isNaN(slide.max)) ? slide.max : min;
      let step = (typeof slide.step === 'number' && !isNaN(slide.step) && slide.step > 0) ? slide.step : 1;
      // Generate valid steps
      const values = [];
      for (let v = min; v <= max; v += step) values.push(v);
      if (def === undefined) def = min;
      if (!values.includes(def)) {
        if (values.length > 0) {
          console.warn(`Slider default value ${def} is not a valid step for slide id: ${slide.id}. Using ${values[0]} instead.`);
          def = values[0];
        }
      }
      if (typeof slide.min !== 'number' || typeof slide.max !== 'number' || typeof slide.step !== 'number' || slide.step <= 0 || slide.min > slide.max) {
        console.warn(`Invalid slider config for slide id: ${slide.id}`);
      }
      return def;
    }
    case 'input':
      return '';
    case 'yes-no':
      return null;
    case 'date-picker':
      return null;
    case 'image-select':
      return null;
    case 'toggle-group':
      return Object.fromEntries(slide.options.map((o: any) => [o.id, !!o.default]));
    case 'ranking':
      return slide.options.map((o: any) => o.id);
    case 'multiple-choice':
      return null;
    case 'multiple-select':
      return [];
    default:
      return null;
  }
}

// Slide type to component mapping
const SLIDE_COMPONENTS: Record<string, React.ComponentType<any>> = {
  'feature': FeatureSlide,
  'multiple-choice': MultipleChoiceSlide,
  'multiple-select': MultipleSelectSlide,
  'slider': ({ slide, answer, setAnswer, theme }) => (
    <SliderSlide
      question={String(slide.question ?? '')}
      min={Number('min' in slide && typeof slide.min === 'number' ? slide.min : 0)}
      max={Number('max' in slide && typeof slide.max === 'number' ? slide.max : 10)}
      step={Number('step' in slide && typeof slide.step === 'number' ? slide.step : 1)}
      unit={String('unit' in slide && slide.unit ? slide.unit : '')}
      value={answer}
      onChange={setAnswer}
      color={theme.primaryButton}
    />
  ),
  'input': ({ slide, answer, setAnswer, theme }) => (
    <InputSlide
      question={String(slide.question ?? '')}
      inputType={slide.inputType === 'number' ? 'number' : 'text'}
      value={answer}
      onChange={setAnswer}
      placeholder={slide.placeholder ?? ''}
      validation={slide.type === 'input' ? slide.validation ?? {} : {}}
      color={theme.primaryButton}
      error={(() => {
        if (slide.type === 'input' && slide.validation && answer) {
          if (slide.validation.min && +answer < slide.validation.min) return `Min: ${slide.validation.min}`;
          if (slide.validation.max && +answer > slide.validation.max) return `Max: ${slide.validation.max}`;
        }
        return '';
      })()}
    />
  ),
  'yes-no': ({ slide, answer, setAnswer, theme }) => (
    <YesNoSlide
      question={String(slide.question ?? '')}
      yesLabel={slide.yesLabel}
      noLabel={slide.noLabel}
      value={typeof answer === 'boolean' ? answer : null}
      onChange={setAnswer}
      color={theme.primaryButton}
    />
  ),
 
  'image-select': ({ slide, answer, setAnswer, theme }) => (
    <ImageSelectSlide
      question={String(slide.question ?? '')}
      options={Array.isArray(slide.options) ? (slide.options.filter((o: any) => typeof o.imageUrl === 'string')) as any : []}
      value={answer}
      onChange={setAnswer}
      color={theme.primaryButton}
    />
  ),
  'toggle-group': ({ slide, answer, setAnswer, theme }) => (
    <ToggleGroupSlide
      question={String(slide.question ?? '')}
      options={Array.isArray(slide.options) ? slide.options : []}
      value={answer && typeof answer === 'object' ? (answer as Record<string, boolean>) : {}}
      onChange={setAnswer}
      color={theme.primaryButton}
    />
  ),
  'ranking': ({ slide, answer, setAnswer, theme }) => (
    <RankingSlide
      question={String(slide.question ?? '')}
      options={Array.isArray(slide.options) ? slide.options : []}
      value={answer}
      onChange={setAnswer}
      color={theme.primaryButton}
    />
  ),
  'animated': ({ slide, theme, handleNext }) => (
    <AnimatedSlide
      title={slide.title}
      description={slide.description}
      animationUrl={slide.animationUrl}
      color={theme.primaryButton}
      onNext={handleNext}
    />
  ),
  'permission': ({ slide, theme, handleNext }) => (
    <PermissionSlide
      question={slide.question}
      description={slide.description}
      allowLabel={slide.allowLabel}
      denyLabel={slide.denyLabel}
      illustration={slide.illustration}
      color={theme.primaryButton}
      onAllow={handleNext}
      onDeny={handleNext}
    />
  ),
};

const Onboard = () => {
  const {
    isLoaded,
    answers,
    lastSlideId,
    updateAnswer,
    completeOnboarding,
  } = useOnboardingState();
  
  const [current, setCurrent] = useState(0);
  const slideAnim = useSharedValue(1);
  const { colors, isDarkMode, theme } = useColorScheme();
  const router = useRouter();
  
  // When the component loads, determine the starting slide.
  useEffect(() => {
    if (isLoaded && lastSlideId) {
      const lastIndex = surveyData.findIndex(s => s.id === lastSlideId);
      if (lastIndex > -1) {
        setCurrent(lastIndex);
      }
    }
  }, [isLoaded, lastSlideId]);

  const handleNext = () => {
    if (current < surveyData.length - 1) {
      slideAnim.value = 0;
      setTimeout(() => {
        setCurrent((prev) => prev + 1);
        slideAnim.value = withTiming(1, { duration: 350 });
      }, 200);
    } else {
      // Reached the end
      completeOnboarding();
      router.push('/(app)/(authenticated)/(tabs)');
    }
  };

  const handleBack = () => {
    if (current > 0) {
      slideAnim.value = 0;
      setTimeout(() => {
        setCurrent((prev) => prev - 1);
        slideAnim.value = withTiming(1, { duration: 350 });
      }, 200);
    }
  };

  if (!isLoaded) {
    // You can return a loading spinner here
    return <View style={[styles.container, { backgroundColor: theme.background }]} />;
  }

  const slide = surveyData[current];
  const answer = answers[slide.id];
  const progress = (current + 1) / surveyData.length;

  const setAnswerForSlide = (val: any) => {
    updateAnswer(slide.id, val);
  };

  // Validation for next button
  let nextDisabled = false;
  if (slide.type === 'multiple-choice') nextDisabled = !answer;
  else if (slide.type === 'multiple-select') nextDisabled = !answer || answer.length === 0;
  else if (slide.type === 'slider') nextDisabled = answer === null || answer === undefined;
  else if (slide.type === 'input') {
    const inputSlide = slide as typeof slide & { validation?: { min?: number; max?: number } };
    nextDisabled = !answer || !!(inputSlide.validation && ((inputSlide.validation.min && +answer < inputSlide.validation.min) || (inputSlide.validation.max && +answer > inputSlide.validation.max)));
  }
  else if (slide.type === 'yes-no') nextDisabled = answer === null;
  else if (slide.type === 'date-picker') nextDisabled = !answer;
  else if (slide.type === 'image-select') nextDisabled = !answer;
  else if (slide.type === 'toggle-group') nextDisabled = false;
  else if (slide.type === 'ranking') nextDisabled = !answer || answer.length === 0;

  const nextBtnColor = nextDisabled ? theme.border : theme.primaryButton;

  // Render slide dynamically
  const SlideComponent = SLIDE_COMPONENTS[slide.type];

  const slideAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: slideAnim.value,
      transform: [
        {
          translateX: interpolate(slideAnim.value, [0, 1], [width * 0.1, 0]),
        },
      ],
    };
  });

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>  
      {/* Progress Bar & Back Button */}
      <View style={styles.topBar}>
        <BackButton
          onPress={handleBack}
          disabled={Boolean(current === 0)}
          color={theme.text}
          backgroundColor={theme.card}
        />
        <ProgressBar
          progress={progress}
          color={theme.primaryButton}
          backgroundColor={theme.border}
        />
      </View>
      <Animated.View style={[slideAnimatedStyle, { width: '100%', flex: 1 }]}>  
        {SlideComponent ? (
          <SlideComponent
            slide={slide}
            answer={answer}
            setAnswer={setAnswerForSlide}
            theme={theme}
            handleNext={handleNext}
          />
        ) : (
          <View style={styles.centered}>
            <Text style={[styles.title, { color: theme.text }]}>Unknown slide type: {slide.type}</Text>
          </View>
        )}
      </Animated.View>
      {/* Next Button (hide for animated/permission) */}
      {!(slide.type === 'animated' || slide.type === 'permission') && (
        <TouchableOpacity
          onPress={handleNext}
          style={[styles.nextBtn, { backgroundColor: nextBtnColor }]}
          disabled={nextDisabled}
          activeOpacity={nextDisabled ? 1 : 0.85}
        >
          <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 18 }}>Next</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default Onboard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 10,
    paddingHorizontal: 32,
    backgroundColor: '#fff',
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 32,
  },
  centered: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    marginTop: 40,
  },
  icon: {
    fontSize: 48,
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  question: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 8,
    marginTop: 12,
  },
  subDescription: {
    fontSize: 16,
    color: '#888',
    marginBottom: 16,
  },
  nextBtn: {
    width: '100%',
    paddingVertical: 18,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    marginTop: 8,
  },
});