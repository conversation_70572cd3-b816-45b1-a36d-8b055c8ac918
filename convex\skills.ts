import { v } from 'convex/values';
import { query, mutation } from './_generated/server';
import { Id } from './_generated/dataModel';

// ==================== SKILL QUERIES ====================

export const getSkillsByTopicId = query({
  args: { topicId: v.id('topics') },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('skills')
      .withIndex('byTopicOrder', (q) => q.eq('topicId', args.topicId))
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();
  },
});

export const getSkillById = query({
  args: { id: v.id('skills') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

export const getSkillsByDifficulty = query({
  args: { difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')) },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('skills')
      .withIndex('byDifficulty', (q) => q.eq('difficulty', args.difficulty))
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();
  },
});

export const getAllSkills = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('skills')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();
  },
});

// ==================== SKILL CONTENT ====================

export const getSkillContent = query({
  args: { skillId: v.id('skills') },
  handler: async (ctx, args) => {
    const skill = await ctx.db.get(args.skillId);
    if (!skill) return null;

    // Get the latest active content version
    const content = await ctx.db
      .query('skillContents')
      .withIndex('bySkillVersion', (q) => q.eq('skillId', args.skillId))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('desc') // Get latest version
      .first();

    return {
      skill,
      content: content?.content || null,
      version: content?.version || 1,
    };
  },
});

export const getSkillContentByVersion = query({
  args: { 
    skillId: v.id('skills'),
    version: v.number(),
  },
  handler: async (ctx, args) => {
    const content = await ctx.db
      .query('skillContents')
      .withIndex('bySkillVersion', (q) => 
        q.eq('skillId', args.skillId).eq('version', args.version)
      )
      .first();

    return content?.content || null;
  },
});

// ==================== SKILL MUTATIONS ====================

export const createSkill = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    topicId: v.id('topics'),
    order: v.number(),
    estimatedDuration: v.number(),
    difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
    contentFileName: v.string(),
    prerequisites: v.optional(v.array(v.id('skills'))),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    return await ctx.db.insert('skills', {
      ...args,
      contentVersion: 1,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

export const updateSkill = mutation({
  args: {
    id: v.id('skills'),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    topicId: v.optional(v.id('topics')),
    order: v.optional(v.number()),
    estimatedDuration: v.optional(v.number()),
    difficulty: v.optional(v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced'))),
    contentFileName: v.optional(v.string()),
    contentVersion: v.optional(v.number()),
    prerequisites: v.optional(v.array(v.id('skills'))),
    isActive: v.optional(v.boolean()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    
    return await ctx.db.patch(id, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

export const deleteSkill = mutation({
  args: { id: v.id('skills') },
  handler: async (ctx, args) => {
    // Soft delete by setting isActive to false
    return await ctx.db.patch(args.id, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});

// ==================== SKILL CONTENT MANAGEMENT ====================

export const createSkillContent = mutation({
  args: {
    skillId: v.id('skills'),
    content: v.object({}),
    version: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // If no version specified, get the next version number
    let version = args.version;
    if (!version) {
      const latestContent = await ctx.db
        .query('skillContents')
        .withIndex('bySkill', (q) => q.eq('skillId', args.skillId))
        .order('desc')
        .first();
      
      version = latestContent ? latestContent.version + 1 : 1;
    }

    // Deactivate previous versions
    const previousContents = await ctx.db
      .query('skillContents')
      .withIndex('bySkill', (q) => q.eq('skillId', args.skillId))
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    for (const content of previousContents) {
      await ctx.db.patch(content._id, { isActive: false });
    }

    // Create new content version
    const contentId = await ctx.db.insert('skillContents', {
      skillId: args.skillId,
      version,
      content: args.content,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    // Update skill's content version
    await ctx.db.patch(args.skillId, {
      contentVersion: version,
      updatedAt: now,
    });

    return contentId;
  },
});

// ==================== SKILL SEARCH ====================

export const searchSkills = query({
  args: { query: v.string() },
  handler: async (ctx, args) => {
    const skills = await ctx.db
      .query('skills')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    const searchTerm = args.query.toLowerCase();
    
    return skills.filter(skill => 
      skill.name.toLowerCase().includes(searchTerm) ||
      skill.description.toLowerCase().includes(searchTerm) ||
      (skill.tags && skill.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
    );
  },
});

export const getSkillsByTag = query({
  args: { tag: v.string() },
  handler: async (ctx, args) => {
    const skills = await ctx.db
      .query('skills')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    return skills.filter(skill => 
      skill.tags && skill.tags.includes(args.tag)
    );
  },
});

// ==================== SKILL PREREQUISITES ====================

export const checkSkillUnlocked = query({
  args: { 
    skillId: v.id('skills'),
    userId: v.id('users'),
  },
  handler: async (ctx, args) => {
    const skill = await ctx.db.get(args.skillId);
    if (!skill || !skill.prerequisites || skill.prerequisites.length === 0) {
      return true; // No prerequisites
    }

    // Check if all prerequisite skills are completed
    for (const prereqId of skill.prerequisites) {
      const progress = await ctx.db
        .query('skillProgress')
        .withIndex('byUserSkill', (q) => 
          q.eq('userId', args.userId).eq('skillId', prereqId)
        )
        .first();

      if (!progress || !progress.isCompleted) {
        return false;
      }
    }

    return true;
  },
});

export const getNextUnlockedSkill = query({
  args: { 
    topicId: v.id('topics'),
    userId: v.id('users'),
  },
  handler: async (ctx, args) => {
    const skills = await ctx.db
      .query('skills')
      .withIndex('byTopicOrder', (q) => q.eq('topicId', args.topicId))
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    for (const skill of skills) {
      // Check if skill is unlocked
      const isUnlocked = await checkSkillUnlocked(ctx, { skillId: skill._id, userId: args.userId });
      
      // Check if skill is completed
      const progress = await ctx.db
        .query('skillProgress')
        .withIndex('byUserSkill', (q) => 
          q.eq('userId', args.userId).eq('skillId', skill._id)
        )
        .first();

      if (isUnlocked && (!progress || !progress.isCompleted)) {
        return skill;
      }
    }

    return null; // All skills completed or none unlocked
  },
});

// ==================== SKILL ANALYTICS ====================

export const getSkillStats = query({
  args: { skillId: v.id('skills') },
  handler: async (ctx, args) => {
    // Get enrollment count (users who have progress in this skill)
    const enrollments = await ctx.db
      .query('skillProgress')
      .withIndex('bySkill', (q) => q.eq('skillId', args.skillId))
      .collect();

    // Get completion count
    const completions = enrollments.filter(p => p.isCompleted).length;

    // Calculate average score
    const scoredProgresses = enrollments.filter(p => p.score !== undefined);
    const avgScore = scoredProgresses.length > 0
      ? scoredProgresses.reduce((sum, p) => sum + (p.score || 0), 0) / scoredProgresses.length
      : 0;

    // Calculate average completion time
    const completedProgresses = enrollments.filter(p => p.isCompleted && p.timeSpent);
    const avgCompletionTime = completedProgresses.length > 0
      ? completedProgresses.reduce((sum, p) => sum + (p.timeSpent || 0), 0) / completedProgresses.length
      : 0;

    return {
      enrolledUsers: enrollments.length,
      completedUsers: completions,
      completionRate: enrollments.length > 0 ? (completions / enrollments.length) * 100 : 0,
      averageScore: Math.round(avgScore),
      averageCompletionTime: Math.round(avgCompletionTime / 60), // Convert to minutes
    };
  },
});

// ==================== BULK OPERATIONS ====================

export const bulkCreateSkills = mutation({
  args: {
    skills: v.array(v.object({
      name: v.string(),
      description: v.string(),
      topicId: v.id('topics'),
      order: v.number(),
      estimatedDuration: v.number(),
      difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
      contentFileName: v.string(),
      prerequisites: v.optional(v.array(v.id('skills'))),
      tags: v.optional(v.array(v.string())),
    })),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const results = [];

    for (const skill of args.skills) {
      const id = await ctx.db.insert('skills', {
        ...skill,
        contentVersion: 1,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      results.push(id);
    }

    return results;
  },
});
