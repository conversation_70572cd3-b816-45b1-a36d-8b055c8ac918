import React from 'react';
import { Tabs } from 'expo-router';
import { StyleSheet, View, TouchableOpacity, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { usePathname } from 'expo-router';
import { useTheme, useThemedStyles } from '~/lib/theme';
import { getThemedShadow } from '~/lib/themeUtils';

// Tab configuration
const TABS = [
  { name: 'index', icon: 'home', label: 'Home' },
  { name: 'premium', icon: 'star', label: 'Premium' },
  { name: 'test', icon: 'airplane', label: 'Test' },
  { name: 'profile', icon: 'person', label: 'Profile' }
];

// Simple Tab Item Component
const TabItem = ({ icon, label, isFocused, onPress }: {
  icon: string;
  label: string;
  isFocused: boolean;
  onPress: () => void;
}) => {
  const { theme, colors } = useTheme();

  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    tabItemContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 8,
    },
    iconWrapper: {
      padding: 8,
      borderRadius: 10,
      borderWidth: 1,
      borderColor: 'transparent',
    },
    activeIconWrapper: {
      backgroundColor: theme.muted,
    },
    tabLabel: {
      fontSize: 16,
      marginTop: 4,
      color: theme.secondaryText,
    },
    activeTabLabel: {
      color: theme.text,
      fontWeight: '500',
    },
  }));

  return (
    <TouchableOpacity
      style={styles.tabItemContainer}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[
        styles.iconWrapper,
        isFocused && styles.activeIconWrapper
      ]}>
        <Ionicons
          name={icon as React.ComponentProps<typeof Ionicons>['name']}
          size={24}
          color={isFocused ? theme.text : theme.secondaryText}
        />
      </View>
      <Text style={[
        styles.tabLabel,
        isFocused && styles.activeTabLabel
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
};

// Custom Tab Bar
function CustomTabBar({ state, navigation }: BottomTabBarProps) {
  const pathname = usePathname();
  const { theme } = useTheme();

  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    tabBarContainer: {
      flexDirection: 'row',
      height: 100,
      backgroundColor: theme.card,
      borderTopWidth: 1,
      borderTopColor: theme.border,
      ...getThemedShadow(theme, 'md'),
      paddingBottom: 10,
    },
  }));

  // Hide the tab bar on the lesson screen
  if (pathname.startsWith('/learn')) {
    return null;
  }

  return (
    <View style={styles.tabBarContainer}>
      {state.routes.map((route, index) => {
        const tabConfig = TABS.find(t => t.name === route.name);
        if (!tabConfig) return null;

        const isFocused = state.index === index;

        return (
          <TabItem
            key={route.key}
            icon={tabConfig.icon}
            label={tabConfig.label}
            isFocused={isFocused}
            onPress={() => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });
              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name, route.params);
              }
            }}
          />
        );
      })}
    </View>
  );
}

// Layout Component
export default function TabLayout() {
  return (
    <Tabs
      tabBar={(props) => <CustomTabBar {...props} />}
      screenOptions={{ headerShown: false }}
    >
      {TABS.map(tab => <Tabs.Screen key={tab.name} name={tab.name} />)}
    </Tabs>
  );
}

// Styles are now created inside components using useThemedStyles