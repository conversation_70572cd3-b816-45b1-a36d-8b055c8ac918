import { colors, AppTheme } from '~/constants/colors';
import { StyleSheet } from 'react-native';

/**
 * Calculate contrast color for text on a given background
 */
export const getContrastColor = (backgroundColor: string): string => {
  // Remove # if present
  const hex = backgroundColor.replace('#', '');
  
  // Parse RGB values
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Calculate brightness using luminance formula
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  
  // Return dark text for light backgrounds, light text for dark backgrounds
  return brightness > 128 ? colors.gray[900] : colors.gray[50];
};

/**
 * Get a themed color value safely
 */
export const getThemedColor = (
  theme: AppTheme,
  colorKey: keyof AppTheme
): string => {
  return theme[colorKey];
};

/**
 * Create a themed StyleSheet factory function
 */
export const createThemedStyleSheet = <T extends Record<string, any>>(
  styleCreator: (theme: AppTheme, colors: typeof colors, isDark: boolean) => T
) => {
  return (theme: AppTheme, colorsObj: typeof colors, isDark: boolean): T => {
    return styleCreator(theme, colorsObj, isDark);
  };
};

/**
 * Get semantic color based on state
 */
export const getStateColor = (
  theme: AppTheme,
  colors: typeof colors,
  state: 'success' | 'error' | 'warning' | 'info' | 'default'
): string => {
  switch (state) {
    case 'success':
      return theme.success;
    case 'error':
      return theme.error;
    case 'warning':
      return theme.warning;
    case 'info':
      return colors.primary[500];
    default:
      return theme.text;
  }
};

/**
 * Get background color for state
 */
export const getStateBackgroundColor = (
  colors: typeof colors,
  state: 'success' | 'error' | 'warning' | 'info' | 'default',
  isDark: boolean = false
): string => {
  const opacity = isDark ? 200 : 50;
  
  switch (state) {
    case 'success':
      return colors.success[opacity];
    case 'error':
      return colors.error[opacity];
    case 'warning':
      return colors.warning[opacity];
    case 'info':
      return colors.primary[opacity];
    default:
      return isDark ? colors.gray[800] : colors.gray[50];
  }
};

/**
 * Generate shadow styles based on theme
 */
export const getThemedShadow = (
  theme: AppTheme,
  elevation: 'sm' | 'md' | 'lg' = 'md'
): object => {
  const shadowColor = theme.shadow || 'rgba(0, 0, 0, 0.1)';
  
  const shadows = {
    sm: {
      shadowColor,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    lg: {
      shadowColor,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 5,
    },
  };
  
  return shadows[elevation];
};

/**
 * Color palette utilities
 */
export const colorUtils = {
  /**
   * Get a color from the palette with fallback
   */
  getColor: (
    colorName: keyof typeof colors,
    shade: keyof typeof colors.primary = 500,
    fallback: string = colors.gray[500]
  ): string => {
    try {
      const colorGroup = colors[colorName];
      if (colorGroup && typeof colorGroup === 'object' && shade in colorGroup) {
        return (colorGroup as any)[shade];
      }
      return fallback;
    } catch {
      return fallback;
    }
  },

  /**
   * Check if a color is light or dark
   */
  isLight: (color: string): boolean => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128;
  },

  /**
   * Add alpha to hex color
   */
  addAlpha: (color: string, alpha: number): string => {
    const hex = color.replace('#', '');
    const alphaHex = Math.round(alpha * 255).toString(16).padStart(2, '0');
    return `#${hex}${alphaHex}`;
  },
};

/**
 * Animation constants for consistent theming
 */
export const themeAnimations = {
  spring: {
    damping: 15,
    stiffness: 300,
  },
  timing: {
    duration: 200,
  },
  easing: {
    ease: 'ease-in-out',
  },
};

/**
 * Common border radius values
 */
export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
};

/**
 * Common spacing values
 */
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};
