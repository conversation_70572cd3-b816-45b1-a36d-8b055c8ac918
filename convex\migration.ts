import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { Id } from './_generated/dataModel';

// ==================== DEVELOPMENT DATA SEEDING ====================

// This would normally import from your development data, but since we can't import
// from src/ in Convex functions, we'll define the data here or pass it as arguments

const DEV_SUBJECTS_DATA = [
  {
    name: 'AI Fundamentals',
    description: 'Master the core concepts and tools of artificial intelligence',
    category: 'Technology',
    order: 1,
    icon: '🤖',
    color: '#3B82F6',
    estimatedDuration: 180,
    difficulty: 'beginner' as const,
    isPremium: false,
    tags: ['ai', 'technology', 'fundamentals'],
  },
  {
    name: 'Productivity & Focus',
    description: 'Learn proven techniques to boost your productivity and focus',
    category: 'Personal Development',
    order: 2,
    icon: '⚡',
    color: '#10B981',
    estimatedDuration: 120,
    difficulty: 'beginner' as const,
    isPremium: false,
    tags: ['productivity', 'focus', 'personal-development'],
  },
  {
    name: 'Machine Learning',
    description: 'Dive deep into machine learning algorithms and applications',
    category: 'Technology',
    order: 3,
    icon: '🧠',
    color: '#8B5CF6',
    estimatedDuration: 240,
    difficulty: 'intermediate' as const,
    isPremium: true,
    tags: ['machine-learning', 'ai', 'algorithms'],
  },
  {
    name: 'Data Science',
    description: 'Explore data analysis, visualization, and statistical methods',
    category: 'Technology',
    order: 4,
    icon: '📊',
    color: '#F59E0B',
    estimatedDuration: 300,
    difficulty: 'intermediate' as const,
    isPremium: true,
    tags: ['data-science', 'analytics', 'statistics'],
  },
];

const DEV_TOPICS_DATA = [
  // AI Fundamentals Topics
  {
    name: 'AI Basics',
    description: 'Introduction to artificial intelligence concepts and terminology',
    subjectName: 'AI Fundamentals',
    order: 1,
    estimatedDuration: 60,
    difficulty: 'beginner' as const,
    tags: ['basics', 'introduction'],
  },
  {
    name: 'AI Tools & Applications',
    description: 'Practical AI tools for everyday use',
    subjectName: 'AI Fundamentals',
    order: 2,
    estimatedDuration: 90,
    difficulty: 'beginner' as const,
    prerequisiteNames: ['AI Basics'],
    tags: ['tools', 'applications', 'practical'],
  },
  {
    name: 'AI Ethics & Society',
    description: 'Understanding the ethical implications of AI technology',
    subjectName: 'AI Fundamentals',
    order: 3,
    estimatedDuration: 30,
    difficulty: 'beginner' as const,
    prerequisiteNames: ['AI Basics'],
    tags: ['ethics', 'society', 'responsibility'],
  },
  
  // Productivity Topics
  {
    name: 'Focus Fundamentals',
    description: 'Master the basics of focus and attention management',
    subjectName: 'Productivity & Focus',
    order: 1,
    estimatedDuration: 60,
    difficulty: 'beginner' as const,
    tags: ['focus', 'attention', 'fundamentals'],
  },
  {
    name: 'Time Management',
    description: 'Advanced techniques for managing your time effectively',
    subjectName: 'Productivity & Focus',
    order: 2,
    estimatedDuration: 60,
    difficulty: 'intermediate' as const,
    prerequisiteNames: ['Focus Fundamentals'],
    tags: ['time-management', 'efficiency', 'planning'],
  },
];

const DEV_SKILLS_DATA = [
  {
    name: 'The Enemy: Multitasking',
    description: 'Learn why multitasking is a myth and how to reclaim your focus by mastering the art of single-tasking.',
    topicName: 'Focus Fundamentals',
    order: 1,
    estimatedDuration: 30,
    difficulty: 'beginner' as const,
    contentFileName: 'dummySkill.json',
    tags: ['focus', 'productivity', 'attention'],
  },
  {
    name: 'Better',
    description: 'A deep dive into the principles, phenomena, and foundational concepts of quantum physics, from the quantization of energy to the bizarre world of quantum entanglement.',
    topicName: 'AI Basics',
    order: 1,
    estimatedDuration: 60,
    difficulty: 'advanced' as const,
    contentFileName: 'better.json',
    tags: ['quantum', 'physics', 'fundamentals'],
  },
  {
    name: 'Calculus Fundamentals',
    description: 'Master the fundamental concepts of calculus including limits, derivatives, and integrals.',
    topicName: 'Time Management',
    order: 1,
    estimatedDuration: 45,
    difficulty: 'intermediate' as const,
    contentFileName: 'calculous.json',
    tags: ['mathematics', 'calculus', 'fundamentals'],
  },
];

// ==================== MIGRATION FUNCTIONS ====================

export const seedDevelopmentData = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    const results = {
      subjects: [] as Id<'subjects'>[],
      topics: [] as Id<'topics'>[],
      skills: [] as Id<'skills'>[],
    };

    // 1. Create Subjects
    const subjectMap = new Map<string, Id<'subjects'>>();
    
    for (const subjectData of DEV_SUBJECTS_DATA) {
      const subjectId = await ctx.db.insert('subjects', {
        ...subjectData,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      
      subjectMap.set(subjectData.name, subjectId);
      results.subjects.push(subjectId);
    }

    // 2. Create Topics
    const topicMap = new Map<string, Id<'topics'>>();
    
    for (const topicData of DEV_TOPICS_DATA) {
      const subjectId = subjectMap.get(topicData.subjectName);
      if (!subjectId) {
        throw new Error(`Subject not found: ${topicData.subjectName}`);
      }

      const { subjectName, prerequisiteNames, ...topicFields } = topicData;
      
      const topicId = await ctx.db.insert('topics', {
        ...topicFields,
        subjectId,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      
      topicMap.set(topicData.name, topicId);
      results.topics.push(topicId);
    }

    // 3. Update Topic Prerequisites
    for (const topicData of DEV_TOPICS_DATA) {
      if (topicData.prerequisiteNames && topicData.prerequisiteNames.length > 0) {
        const topicId = topicMap.get(topicData.name);
        const prerequisites: Id<'topics'>[] = [];
        
        for (const prereqName of topicData.prerequisiteNames) {
          const prereqId = topicMap.get(prereqName);
          if (prereqId) {
            prerequisites.push(prereqId);
          }
        }
        
        if (topicId && prerequisites.length > 0) {
          await ctx.db.patch(topicId, { prerequisites });
        }
      }
    }

    // 4. Create Skills
    for (const skillData of DEV_SKILLS_DATA) {
      const topicId = topicMap.get(skillData.topicName);
      if (!topicId) {
        throw new Error(`Topic not found: ${skillData.topicName}`);
      }

      const { topicName, ...skillFields } = skillData;
      
      const skillId = await ctx.db.insert('skills', {
        ...skillFields,
        topicId,
        contentVersion: 1,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      
      results.skills.push(skillId);
    }

    return results;
  },
});

export const clearAllData = mutation({
  args: {},
  handler: async (ctx) => {
    // WARNING: This will delete ALL data - use with caution!
    
    // Delete in reverse dependency order
    const skillContents = await ctx.db.query('skillContents').collect();
    for (const content of skillContents) {
      await ctx.db.delete(content._id);
    }

    const skills = await ctx.db.query('skills').collect();
    for (const skill of skills) {
      await ctx.db.delete(skill._id);
    }

    const topics = await ctx.db.query('topics').collect();
    for (const topic of topics) {
      await ctx.db.delete(topic._id);
    }

    const subjects = await ctx.db.query('subjects').collect();
    for (const subject of subjects) {
      await ctx.db.delete(subject._id);
    }

    // Also clear progress data
    const skillProgress = await ctx.db.query('skillProgress').collect();
    for (const progress of skillProgress) {
      await ctx.db.delete(progress._id);
    }

    const topicProgress = await ctx.db.query('topicProgress').collect();
    for (const progress of topicProgress) {
      await ctx.db.delete(progress._id);
    }

    const subjectProgress = await ctx.db.query('subjectProgress').collect();
    for (const progress of subjectProgress) {
      await ctx.db.delete(progress._id);
    }

    return { message: 'All data cleared successfully' };
  },
});

export const checkDataExists = query({
  args: {},
  handler: async (ctx) => {
    const subjectCount = (await ctx.db.query('subjects').collect()).length;
    const topicCount = (await ctx.db.query('topics').collect()).length;
    const skillCount = (await ctx.db.query('skills').collect()).length;

    return {
      subjects: subjectCount,
      topics: topicCount,
      skills: skillCount,
      hasData: subjectCount > 0 || topicCount > 0 || skillCount > 0,
    };
  },
});

// ==================== BACKUP AND RESTORE ====================

export const exportAllData = query({
  args: {},
  handler: async (ctx) => {
    const subjects = await ctx.db.query('subjects').collect();
    const topics = await ctx.db.query('topics').collect();
    const skills = await ctx.db.query('skills').collect();
    const skillContents = await ctx.db.query('skillContents').collect();

    return {
      subjects,
      topics,
      skills,
      skillContents,
      exportedAt: Date.now(),
    };
  },
});

export const importData = mutation({
  args: {
    data: v.object({
      subjects: v.array(v.any()),
      topics: v.array(v.any()),
      skills: v.array(v.any()),
      skillContents: v.optional(v.array(v.any())),
    }),
  },
  handler: async (ctx, args) => {
    const { data } = args;
    const results = {
      subjects: [] as Id<'subjects'>[],
      topics: [] as Id<'topics'>[],
      skills: [] as Id<'skills'>[],
      skillContents: [] as Id<'skillContents'>[],
    };

    // Import subjects
    for (const subject of data.subjects) {
      const { _id, _creationTime, ...subjectData } = subject;
      const id = await ctx.db.insert('subjects', subjectData);
      results.subjects.push(id);
    }

    // Import topics
    for (const topic of data.topics) {
      const { _id, _creationTime, ...topicData } = topic;
      const id = await ctx.db.insert('topics', topicData);
      results.topics.push(id);
    }

    // Import skills
    for (const skill of data.skills) {
      const { _id, _creationTime, ...skillData } = skill;
      const id = await ctx.db.insert('skills', skillData);
      results.skills.push(id);
    }

    // Import skill contents if provided
    if (data.skillContents) {
      for (const content of data.skillContents) {
        const { _id, _creationTime, ...contentData } = content;
        const id = await ctx.db.insert('skillContents', contentData);
        results.skillContents.push(id);
      }
    }

    return results;
  },
});
