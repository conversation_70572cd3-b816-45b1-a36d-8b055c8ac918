import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { Id } from './_generated/dataModel';

// ==================== SINGLE TABLE DEVELOPMENT DATA ====================

// Complete subject data with embedded topics and skills
const DEV_SUBJECTS_DATA = [
  {
    id: 'subject-ai-fundamentals',
    name: 'AI Fundamentals',
    description: 'Master the core concepts and tools of artificial intelligence',
    category: 'Technology',
    order: 1,
    icon: '🤖',
    color: '#3B82F6',
    estimatedDuration: 180,
    difficulty: 'beginner' as const,
    isPremium: false,
    tags: ['ai', 'technology', 'fundamentals'],
    topics: [
      {
        id: 'topic-ai-basics',
        name: 'AI Basics',
        description: 'Introduction to artificial intelligence concepts and terminology',
        order: 1,
        estimatedDuration: 60,
        difficulty: 'beginner' as const,
        tags: ['basics', 'introduction'],
        skills: [
          {
            id: 'skill-better-001',
            name: 'Better',
            description: 'A deep dive into the principles, phenomena, and foundational concepts of quantum physics, from the quantization of energy to the bizarre world of quantum entanglement.',
            order: 1,
            estimatedDuration: 60,
            difficulty: 'advanced' as const,
            contentFileName: 'better.json',
            contentVersion: 1,
            tags: ['quantum', 'physics', 'fundamentals'],
            isActive: true,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'topic-ai-tools',
        name: 'AI Tools & Applications',
        description: 'Practical AI tools for everyday use',
        order: 2,
        estimatedDuration: 90,
        difficulty: 'beginner' as const,
        prerequisites: ['topic-ai-basics'],
        tags: ['tools', 'applications', 'practical'],
        skills: [],
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'topic-ai-ethics',
        name: 'AI Ethics & Society',
        description: 'Understanding the ethical implications of AI technology',
        order: 3,
        estimatedDuration: 30,
        difficulty: 'beginner' as const,
        prerequisites: ['topic-ai-basics'],
        tags: ['ethics', 'society', 'responsibility'],
        skills: [],
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    ],
  },
  {
    id: 'subject-productivity',
    name: 'Productivity & Focus',
    description: 'Learn proven techniques to boost your productivity and focus',
    category: 'Personal Development',
    order: 2,
    icon: '⚡',
    color: '#10B981',
    estimatedDuration: 120,
    difficulty: 'beginner' as const,
    isPremium: false,
    tags: ['productivity', 'focus', 'personal-development'],
    topics: [
      {
        id: 'topic-productivity-basics',
        name: 'Focus Fundamentals',
        description: 'Master the basics of focus and attention management',
        order: 1,
        estimatedDuration: 60,
        difficulty: 'beginner' as const,
        tags: ['focus', 'attention', 'fundamentals'],
        skills: [
          {
            id: 'skill-dummy-productivity-002',
            name: 'The Enemy: Multitasking',
            description: 'Learn why multitasking is a myth and how to reclaim your focus by mastering the art of single-tasking.',
            order: 1,
            estimatedDuration: 30,
            difficulty: 'beginner' as const,
            contentFileName: 'dummySkill.json',
            contentVersion: 1,
            tags: ['focus', 'productivity', 'attention'],
            isActive: true,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'topic-time-management',
        name: 'Time Management',
        description: 'Advanced techniques for managing your time effectively',
        order: 2,
        estimatedDuration: 60,
        difficulty: 'intermediate' as const,
        prerequisites: ['topic-productivity-basics'],
        tags: ['time-management', 'efficiency', 'planning'],
        skills: [
          {
            id: 'skill-calculus-001',
            name: 'Calculus Fundamentals',
            description: 'Master the fundamental concepts of calculus including limits, derivatives, and integrals.',
            order: 1,
            estimatedDuration: 45,
            difficulty: 'intermediate' as const,
            contentFileName: 'calculous.json',
            contentVersion: 1,
            tags: ['mathematics', 'calculus', 'fundamentals'],
            isActive: true,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    ],
  },
  {
    id: 'subject-machine-learning',
    name: 'Machine Learning',
    description: 'Dive deep into machine learning algorithms and applications',
    category: 'Technology',
    order: 3,
    icon: '🧠',
    color: '#8B5CF6',
    estimatedDuration: 240,
    difficulty: 'intermediate' as const,
    isPremium: true,
    tags: ['machine-learning', 'ai', 'algorithms'],
    topics: [],
  },
  {
    id: 'subject-data-science',
    name: 'Data Science',
    description: 'Explore data analysis, visualization, and statistical methods',
    category: 'Technology',
    order: 4,
    icon: '📊',
    color: '#F59E0B',
    estimatedDuration: 300,
    difficulty: 'intermediate' as const,
    isPremium: true,
    tags: ['data-science', 'analytics', 'statistics'],
    topics: [],
  },
];

// ==================== SINGLE TABLE MIGRATION FUNCTIONS ====================

export const seedDevelopmentData = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    const results = {
      subjects: [] as Id<'subjects'>[],
      totalTopics: 0,
      totalSkills: 0,
    };

    // Insert complete subjects with embedded topics and skills
    for (const subjectData of DEV_SUBJECTS_DATA) {
      const subjectId = await ctx.db.insert('subjects', {
        ...subjectData,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });

      results.subjects.push(subjectId);
      results.totalTopics += subjectData.topics.length;

      // Count skills across all topics
      for (const topic of subjectData.topics) {
        results.totalSkills += topic.skills.length;
      }
    }

    return results;
  },
});

export const clearAllData = mutation({
  args: {},
  handler: async (ctx) => {
    // WARNING: This will delete ALL data - use with caution!

    // With single table design, much simpler cleanup
    const subjects = await ctx.db.query('subjects').collect();
    for (const subject of subjects) {
      await ctx.db.delete(subject._id);
    }

    // Clear progress data
    const skillProgress = await ctx.db.query('skillProgress').collect();
    for (const progress of skillProgress) {
      await ctx.db.delete(progress._id);
    }

    const topicProgress = await ctx.db.query('topicProgress').collect();
    for (const progress of topicProgress) {
      await ctx.db.delete(progress._id);
    }

    const subjectProgress = await ctx.db.query('subjectProgress').collect();
    for (const progress of subjectProgress) {
      await ctx.db.delete(progress._id);
    }

    return { message: 'All data cleared successfully' };
  },
});

export const checkDataExists = query({
  args: {},
  handler: async (ctx) => {
    const subjects = await ctx.db.query('subjects').collect();
    const subjectCount = subjects.length;

    // Count embedded topics and skills
    let topicCount = 0;
    let skillCount = 0;

    for (const subject of subjects) {
      topicCount += subject.topics.length;
      for (const topic of subject.topics) {
        skillCount += topic.skills.length;
      }
    }

    return {
      subjects: subjectCount,
      topics: topicCount,
      skills: skillCount,
      hasData: subjectCount > 0,
    };
  },
});

// ==================== BACKUP AND RESTORE ====================

export const exportAllData = query({
  args: {},
  handler: async (ctx) => {
    const subjects = await ctx.db.query('subjects').collect();

    return {
      subjects,
      exportedAt: Date.now(),
    };
  },
});

export const importData = mutation({
  args: {
    data: v.object({
      subjects: v.array(v.any()),
    }),
  },
  handler: async (ctx, args) => {
    const { data } = args;
    const results = {
      subjects: [] as Id<'subjects'>[],
    };

    // Import subjects (with embedded topics and skills)
    for (const subject of data.subjects) {
      const { _id, _creationTime, ...subjectData } = subject;
      const id = await ctx.db.insert('subjects', subjectData);
      results.subjects.push(id);
    }

    return results;
  },
});

// ==================== MIGRATION FROM NORMALIZED TO SINGLE TABLE ====================

export const migrateFromNormalizedData = mutation({
  args: {
    normalizedData: v.object({
      subjects: v.array(v.any()),
      topics: v.array(v.any()),
      skills: v.array(v.any()),
    }),
  },
  handler: async (ctx, args) => {
    const { normalizedData } = args;
    const now = Date.now();
    const results = [];

    // Group topics by subject
    const topicsBySubject = new Map();
    for (const topic of normalizedData.topics) {
      if (!topicsBySubject.has(topic.subjectId)) {
        topicsBySubject.set(topic.subjectId, []);
      }
      topicsBySubject.get(topic.subjectId).push(topic);
    }

    // Group skills by topic
    const skillsByTopic = new Map();
    for (const skill of normalizedData.skills) {
      if (!skillsByTopic.has(skill.topicId)) {
        skillsByTopic.set(skill.topicId, []);
      }
      skillsByTopic.get(skill.topicId).push(skill);
    }

    // Create subjects with embedded topics and skills
    for (const subject of normalizedData.subjects) {
      const topics = topicsBySubject.get(subject._id) || [];

      const embeddedTopics = topics.map((topic: any) => {
        const skills = skillsByTopic.get(topic._id) || [];

        const embeddedSkills = skills.map((skill: any) => ({
          id: skill.id || `skill-${skill._id}`,
          name: skill.name,
          description: skill.description,
          order: skill.order,
          estimatedDuration: skill.estimatedDuration,
          difficulty: skill.difficulty,
          contentFileName: skill.contentFileName,
          contentVersion: skill.contentVersion || 1,
          prerequisites: skill.prerequisites || [],
          isActive: skill.isActive !== false,
          tags: skill.tags || [],
          createdAt: skill.createdAt || now,
          updatedAt: skill.updatedAt || now,
        }));

        return {
          id: topic.id || `topic-${topic._id}`,
          name: topic.name,
          description: topic.description,
          order: topic.order,
          estimatedDuration: topic.estimatedDuration,
          difficulty: topic.difficulty,
          prerequisites: topic.prerequisites || [],
          isActive: topic.isActive !== false,
          tags: topic.tags || [],
          skills: embeddedSkills,
          createdAt: topic.createdAt || now,
          updatedAt: topic.updatedAt || now,
        };
      });

      const subjectId = await ctx.db.insert('subjects', {
        id: subject.id || `subject-${subject._id}`,
        name: subject.name,
        description: subject.description,
        category: subject.category,
        order: subject.order,
        icon: subject.icon,
        color: subject.color,
        estimatedDuration: subject.estimatedDuration,
        difficulty: subject.difficulty,
        isPremium: subject.isPremium,
        isActive: subject.isActive !== false,
        tags: subject.tags || [],
        topics: embeddedTopics,
        createdBy: subject.createdBy,
        createdAt: subject.createdAt || now,
        updatedAt: subject.updatedAt || now,
      });

      results.push(subjectId);
    }

    return { subjects: results };
  },
});
