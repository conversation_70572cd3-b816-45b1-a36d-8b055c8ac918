import React from "react";
import { View, useWindowDimensions } from "react-native";
import { TextInfoExercise } from "@/types/skill";
import { AnswerFeedback } from "../store";
import Markdown from "react-native-markdown-display";
import { MarkedStyles } from "react-native-marked";


interface Props {
  exercise: TextInfoExercise;
  onAnswer: (v: unknown) => void;
  currentAnswer?: unknown;
  feedback?: AnswerFeedback;
}

// Define custom styles for Markdown elements
const markdownStyles: MarkedStyles = {
  h2: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 20,
    color: "#1F2937",
    backgroundColor: "#F0FFF4",
    paddingHorizontal: 12,  
    paddingVertical: 8,
    borderRadius: 8,
    textAlign: "center",
  },
  h3: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 14,
    color: "#111827",
    backgroundColor: "#EBFAF0",
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: "#38A169",
  },
  paragraph: {
    flex: 0.3,
    backgroundColor: 'beige',
    borderWidth: 2,
    marginBottom: 14,
    
    borderRadius: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  strong: {
    fontWeight: "bold",
    color: "#276749",
    backgroundColor: "#E6FFFA",
    paddingHorizontal: 4,
    borderRadius: 4,
  },
  em: {
    fontStyle: "italic",
    backgroundColor: "#FFF5F7",
    color: "#B83280",
    paddingHorizontal: 4,
    borderRadius: 4,
  },
  image: {
    width: '100%',
    height: 100,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
    marginVertical: 12,
  },
  blockquote: {
    backgroundColor: "#FFFAF0",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderLeftWidth: 4,
    borderLeftColor: "#DD6B20",
    borderRadius: 8,
    marginVertical: 12,
  },
  code: {
    backgroundColor: "#F0F4F8",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    
    
    
  },
  
  list: {
    backgroundColor: "#F9F9FF",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginVertical: 8,
  }
  
};

export default function TextInfo({ exercise }: Props) {
  const { width } = useWindowDimensions();
  
  return (
    // The width subtraction accounts for the horizontal padding in SkillPlayer
    <View style={{ width: width - 48 }}>
      <Markdown
      >
        {exercise.payload.markdown}
      </Markdown>
    </View>
  );
}