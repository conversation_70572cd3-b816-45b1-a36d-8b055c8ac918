import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

interface SliderSlideProps {
  question: string;
  min: number;
  max: number;
  step: number;
  unit?: string;
  value: number;
  onChange: (v: number) => void;
  color: string;
}

export function SliderSlide({ question, min, max, step, unit, value, onChange, color }: SliderSlideProps) {
  // Defensive: Ensure min, max, step are valid numbers
  if (typeof min !== 'number' || typeof max !== 'number' || typeof step !== 'number' || step <= 0 || min > max) {
    return (
      <View style={{ width: '100%' }}>
        <Text style={styles.question}>{question}</Text>
        <Text style={{ color: 'red', marginTop: 16 }}>Invalid slider configuration. Please check min, max, and step values.</Text>
      </View>
    );
  }

  // Defensive: Ensure value is a number, fallback to min
  let safeValue = (typeof value === 'number' && !isNaN(value)) ? value : min;

  // Generate values for the slider
  const values = [];
  for (let v = min; v <= max; v += step) values.push(v);

  // If safeValue is not in values, use the first value
  if (!values.includes(safeValue)) {
    if (values.length > 0) {
      console.warn(`Slider value ${safeValue} is not a valid step. Using ${values[0]} instead.`);
      safeValue = values[0];
    }
  }

  return (
    <View style={{ width: '100%' }}>
      <Text style={styles.question}>{question}</Text>
      <View style={styles.valueRow}>
        <Text style={[styles.value, { color }]}>{safeValue}</Text>
        {unit && <Text style={styles.unit}>{unit}</Text>}
      </View>
      <View style={styles.buttonRow}>
        {values.map((v) => (
          <TouchableOpacity
            key={v}
            style={[styles.button, safeValue === v && { backgroundColor: color }]}
            onPress={() => onChange(v)}
          >
            <Text style={[styles.buttonText, safeValue === v && { color: '#fff' }]}>{v}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  question: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  valueRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  value: {
    fontSize: 32,
    fontWeight: 'bold',
    marginRight: 6,
  },
  unit: {
    fontSize: 18,
    color: '#888',
    marginBottom: 2,
  },
  buttonRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 12,
    justifyContent: 'center',
  },
  button: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: '#f7f8fa',
    marginHorizontal: 4,
    marginBottom: 8,
  },
  buttonText: {
    fontSize: 16,
    color: '#222',
    fontWeight: '600',
  },
  rangeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  rangeLabel: {
    fontSize: 14,
    color: '#888',
  },
}); 