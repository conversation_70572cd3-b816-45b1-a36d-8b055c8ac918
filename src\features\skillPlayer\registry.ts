import { Exercise } from "@/types/skill";
import TextInfo from "./components/TextInfo";
import SingleChoice from "./components/SingleChoice";
import MultiChoice from "./components/MultiChoice";
import FillBlank from "./components/FillBlank";
import DragOrder from "./components/DragOrder";
import TrueFalse from "./components/TrueFalse";
import MatchingPairs from "./components/MatchingPairs";

export const registry: Record<Exercise["type"], React.FC<any>> = {
  "text-info": TextInfo,
  "single-choice": SingleChoice,
  "multi-choice": MultiChoice,
  "fill-blank": FillBlank,
  "drag-order": DragOrder,
  "true-false": TrueFalse,
  "matching-pairs": MatchingPairs,
};

