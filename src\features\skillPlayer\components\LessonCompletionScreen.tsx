import React, { useEffect } from "react";
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  withDelay,
  runOnJS
} from "react-native-reanimated";
import { CompletionType, LessonStats } from "../store";
import { useTheme, useThemedStyles } from '~/lib/theme';
import { getThemedShadow } from '~/lib/themeUtils';

interface Props {
  completionType: CompletionType;
  lessonName: string;
  lessonStats: LessonStats | null;
  onStartNextLesson: () => void;
  onReviewLesson: () => void;
  onGoHome: () => void;
  canStartNextLesson: boolean;
}

export default function LessonCompletionScreen({
  completionType,
  lessonName,
  lessonStats,
  onStartNextLesson,
  onReviewLesson,
  onGoHome,
  canStartNextLesson
}: Props) {
  const { colors } = useTheme();
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const confettiScale = useSharedValue(0);
  const statsOpacity = useSharedValue(0);
  const buttonsOpacity = useSharedValue(0);

  useEffect(() => {
    // Celebration animation sequence
    opacity.value = withSpring(1, { damping: 15, stiffness: 300 });
    scale.value = withSequence(
      withSpring(1.2, { damping: 10, stiffness: 300 }),
      withSpring(1, { damping: 15, stiffness: 300 })
    );
    
    // Confetti animation
    confettiScale.value = withDelay(200, withSpring(1, { damping: 12, stiffness: 200 }));
    
    // Stats fade in
    statsOpacity.value = withDelay(600, withSpring(1, { damping: 15, stiffness: 300 }));
    
    // Buttons fade in
    buttonsOpacity.value = withDelay(1000, withSpring(1, { damping: 15, stiffness: 300 }));
  }, []);

  const mainAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const confettiAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: confettiScale.value }],
  }));

  const statsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: statsOpacity.value,
  }));

  const buttonsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: buttonsOpacity.value,
  }));

  const getCompletionTitle = () => {
    switch (completionType) {
      case 'lesson':
        return 'Lesson Complete!';
      case 'level':
        return 'Level Complete!';
      case 'skill':
        return 'Skill Mastered!';
      default:
        return 'Great Job!';
    }
  };

  const getCompletionMessage = () => {
    switch (completionType) {
      case 'lesson':
        return `You've successfully completed "${lessonName}"`;
      case 'level':
        return `Amazing! You've finished the entire level with "${lessonName}"`;
      case 'skill':
        return `Congratulations! You've mastered the entire skill!`;
      default:
        return 'Keep up the great work!';
    }
  };

  const getAccuracyPercentage = () => {
    if (!lessonStats || lessonStats.totalExercises === 0) return 0;
    return Math.round((lessonStats.correctAnswers / lessonStats.totalExercises) * 100);
  };

  const getTimeTaken = () => {
    if (!lessonStats || lessonStats.endTime === 0) return 'N/A';
    const minutes = Math.floor((lessonStats.endTime - lessonStats.startTime) / 60000);
    const seconds = Math.floor(((lessonStats.endTime - lessonStats.startTime) % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Create theme-aware styles
  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    confettiContainer: {
      position: 'absolute',
      top: 100,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'space-around',
      zIndex: 1,
    },
    confetti: {
      fontSize: 40,
    },
    content: {
      flex: 1,
      paddingHorizontal: 24,
      justifyContent: 'center',
    },
    mainSection: {
      alignItems: 'center',
      marginBottom: 40,
    },
    iconContainer: {
      marginBottom: 24,
    },
    title: {
      fontSize: 32,
      fontWeight: 'bold',
      color: theme.text,
      textAlign: 'center',
      marginBottom: 16,
    },
    message: {
      fontSize: 18,
      color: theme.secondaryText,
      textAlign: 'center',
      lineHeight: 24,
    },
    statsSection: {
      marginBottom: 40,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      backgroundColor: theme.card,
      borderRadius: 16,
      paddingVertical: 24,
      paddingHorizontal: 16,
      ...getThemedShadow(theme, 'sm'),
    },
    statItem: {
      alignItems: 'center',
    },
    statNumber: {
      fontSize: 28,
      fontWeight: 'bold',
      color: colors.primary[500],
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 14,
      color: theme.secondaryText,
      fontWeight: '500',
    },
    buttonsSection: {
      gap: 12,
    },
    button: {
      paddingVertical: 16,
      paddingHorizontal: 24,
      borderRadius: 12,
      alignItems: 'center',
    },
    primaryButton: {
      backgroundColor: theme.primaryButton,
    },
    primaryButtonText: {
      color: colors.white,
      fontSize: 16,
      fontWeight: 'bold',
    },
    secondaryButton: {
      backgroundColor: theme.card,
      borderWidth: 2,
      borderColor: theme.primaryButton,
    },
    secondaryButtonText: {
      color: theme.primaryButton,
      fontSize: 16,
      fontWeight: '600',
    },
    tertiaryButton: {
      backgroundColor: 'transparent',
    },
    tertiaryButtonText: {
      color: theme.secondaryText,
      fontSize: 16,
      fontWeight: '500',
    },
  }));

  const handleGoHome = () => {
    onGoHome();
    router.push('/(app)/(authenticated)/(tabs)');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Confetti/Celebration Elements */}
      <Animated.View style={[styles.confettiContainer, confettiAnimatedStyle]}>
        <Text style={styles.confetti}>🎉</Text>
        <Text style={styles.confetti}>✨</Text>
        <Text style={styles.confetti}>🎊</Text>
        <Text style={styles.confetti}>⭐</Text>
      </Animated.View>

      <View style={styles.content}>
        {/* Main Completion Message */}
        <Animated.View style={[styles.mainSection, mainAnimatedStyle]}>
          <View style={styles.iconContainer}>
            <Ionicons
              name="trophy"
              size={80}
              color={colors.warning[500]}
            />
          </View>
          
          <Text style={styles.title}>{getCompletionTitle()}</Text>
          <Text style={styles.message}>{getCompletionMessage()}</Text>
        </Animated.View>

        {/* Statistics Section */}
        <Animated.View style={[styles.statsSection, statsAnimatedStyle]}>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{lessonStats?.correctAnswers || 0}</Text>
              <Text style={styles.statLabel}>Correct</Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{getAccuracyPercentage()}%</Text>
              <Text style={styles.statLabel}>Accuracy</Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{getTimeTaken()}</Text>
              <Text style={styles.statLabel}>Time</Text>
            </View>
          </View>
        </Animated.View>

        {/* Action Buttons */}
        <Animated.View style={[styles.buttonsSection, buttonsAnimatedStyle]}>
          {canStartNextLesson && (
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={onStartNextLesson}
              activeOpacity={0.8}
            >
              <Text style={styles.primaryButtonText}>
                {completionType === 'lesson' ? 'Start Next Lesson' : 'Continue Learning'}
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={onReviewLesson}
            activeOpacity={0.8}
          >
            <Text style={styles.secondaryButtonText}>Review Lesson</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.tertiaryButton]}
            onPress={handleGoHome}
            activeOpacity={0.8}
          >
            <Text style={styles.tertiaryButtonText}>Back to Home</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}

const { width } = Dimensions.get('window');

// Styles are now created inside the component using useThemedStyles
