import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { HeeboText } from '@/components/ui/CustomText';
import { useFreemium } from '@/features/freemium/FreemiumProvider';

const Premium = () => {
  const { bottom } = useSafeAreaInsets();
  const { isFreeTier } = useFreemium();

  return (
    <View style={[styles.container, { paddingBottom: bottom + 12 }]}>
      <View style={styles.contentContainer}>
        <Ionicons name="construct-outline" size={80} color="#F59E0B" />
        <HeeboText fontWeight="BOLD" style={styles.headerText}>
          Coming Soon
        </HeeboText>
        <Text style={styles.descriptionText}>
          We're working on exciting premium content that will be available here soon!
        </Text>
        
        {isFreeTier && (
          <TouchableOpacity style={styles.upgradeButton}>
            <Ionicons name="diamond-outline" size={20} color="#FFFFFF" />
            <Text style={styles.upgradeButtonText}>Upgrade to Premium</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default Premium;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  headerText: {
    fontSize: 32,
    color: '#000',
    marginTop: 24,
    marginBottom: 16,
    textAlign: 'center',
  },
  descriptionText: {
    fontSize: 18,
    color: '#4B5563',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 26,
  },
  upgradeButton: {
    backgroundColor: '#F59E0B',
    borderRadius: 16,
    paddingHorizontal: 24,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#F59E0B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  upgradeButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});