import { v } from 'convex/values';
import { query, mutation } from './_generated/server';
import { Id } from './_generated/dataModel';

// ==================== TOPIC QUERIES ====================

export const getTopicsBySubjectId = query({
  args: { subjectId: v.id('subjects') },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('topics')
      .withIndex('bySubjectOrder', (q) => q.eq('subjectId', args.subjectId))
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();
  },
});

export const getTopicById = query({
  args: { id: v.id('topics') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

export const getTopicsByDifficulty = query({
  args: { difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')) },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('topics')
      .withIndex('byDifficulty', (q) => q.eq('difficulty', args.difficulty))
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();
  },
});

export const getAllTopics = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('topics')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();
  },
});

// ==================== TOPIC MUTATIONS ====================

export const createTopic = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    subjectId: v.id('subjects'),
    order: v.number(),
    estimatedDuration: v.number(),
    difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
    prerequisites: v.optional(v.array(v.id('topics'))),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    return await ctx.db.insert('topics', {
      ...args,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

export const updateTopic = mutation({
  args: {
    id: v.id('topics'),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    subjectId: v.optional(v.id('subjects')),
    order: v.optional(v.number()),
    estimatedDuration: v.optional(v.number()),
    difficulty: v.optional(v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced'))),
    prerequisites: v.optional(v.array(v.id('topics'))),
    isActive: v.optional(v.boolean()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    
    return await ctx.db.patch(id, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

export const deleteTopic = mutation({
  args: { id: v.id('topics') },
  handler: async (ctx, args) => {
    // Soft delete by setting isActive to false
    return await ctx.db.patch(args.id, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});

// ==================== TOPIC SEARCH ====================

export const searchTopics = query({
  args: { query: v.string() },
  handler: async (ctx, args) => {
    const topics = await ctx.db
      .query('topics')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    const searchTerm = args.query.toLowerCase();
    
    return topics.filter(topic => 
      topic.name.toLowerCase().includes(searchTerm) ||
      topic.description.toLowerCase().includes(searchTerm) ||
      (topic.tags && topic.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
    );
  },
});

// ==================== TOPIC PREREQUISITES ====================

export const checkTopicUnlocked = query({
  args: { 
    topicId: v.id('topics'),
    userId: v.id('users'),
  },
  handler: async (ctx, args) => {
    const topic = await ctx.db.get(args.topicId);
    if (!topic || !topic.prerequisites || topic.prerequisites.length === 0) {
      return true; // No prerequisites
    }

    // Check if all prerequisite topics are completed
    for (const prereqId of topic.prerequisites) {
      const progress = await ctx.db
        .query('topicProgress')
        .withIndex('byUserTopic', (q) => 
          q.eq('userId', args.userId).eq('topicId', prereqId)
        )
        .first();

      if (!progress || !progress.isCompleted) {
        return false;
      }
    }

    return true;
  },
});

export const getNextUnlockedTopic = query({
  args: { 
    subjectId: v.id('subjects'),
    userId: v.id('users'),
  },
  handler: async (ctx, args) => {
    const topics = await ctx.db
      .query('topics')
      .withIndex('bySubjectOrder', (q) => q.eq('subjectId', args.subjectId))
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    for (const topic of topics) {
      // Check if topic is unlocked
      const isUnlocked = await checkTopicUnlocked(ctx, { topicId: topic._id, userId: args.userId });
      
      // Check if topic is completed
      const progress = await ctx.db
        .query('topicProgress')
        .withIndex('byUserTopic', (q) => 
          q.eq('userId', args.userId).eq('topicId', topic._id)
        )
        .first();

      if (isUnlocked && (!progress || !progress.isCompleted)) {
        return topic;
      }
    }

    return null; // All topics completed or none unlocked
  },
});

// ==================== TOPIC WITH RELATIONSHIPS ====================

export const getTopicWithSkills = query({
  args: { topicId: v.id('topics') },
  handler: async (ctx, args) => {
    const topic = await ctx.db.get(args.topicId);
    if (!topic) return null;

    const skills = await ctx.db
      .query('skills')
      .withIndex('byTopicOrder', (q) => q.eq('topicId', args.topicId))
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    return {
      topic,
      skills,
    };
  },
});

export const getTopicWithProgress = query({
  args: { 
    topicId: v.id('topics'),
    userId: v.id('users'),
  },
  handler: async (ctx, args) => {
    const topic = await ctx.db.get(args.topicId);
    if (!topic) return null;

    const progress = await ctx.db
      .query('topicProgress')
      .withIndex('byUserTopic', (q) => 
        q.eq('userId', args.userId).eq('topicId', args.topicId)
      )
      .first();

    return {
      topic,
      progress,
    };
  },
});

// ==================== TOPIC ANALYTICS ====================

export const getTopicStats = query({
  args: { topicId: v.id('topics') },
  handler: async (ctx, args) => {
    // Get total skills count
    const skills = await ctx.db
      .query('skills')
      .withIndex('byTopic', (q) => q.eq('topicId', args.topicId))
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    // Get enrollment count (users who have progress in this topic)
    const enrollments = await ctx.db
      .query('topicProgress')
      .withIndex('byTopic', (q) => q.eq('topicId', args.topicId))
      .collect();

    // Get completion count
    const completions = enrollments.filter(p => p.isCompleted).length;

    // Calculate average completion time
    const completedProgresses = enrollments.filter(p => p.isCompleted && p.totalTimeSpent);
    const avgCompletionTime = completedProgresses.length > 0
      ? completedProgresses.reduce((sum, p) => sum + (p.totalTimeSpent || 0), 0) / completedProgresses.length
      : 0;

    return {
      totalSkills: skills.length,
      enrolledUsers: enrollments.length,
      completedUsers: completions,
      completionRate: enrollments.length > 0 ? (completions / enrollments.length) * 100 : 0,
      averageCompletionTime: Math.round(avgCompletionTime / 60), // Convert to minutes
    };
  },
});

// ==================== BULK OPERATIONS ====================

export const bulkCreateTopics = mutation({
  args: {
    topics: v.array(v.object({
      name: v.string(),
      description: v.string(),
      subjectId: v.id('subjects'),
      order: v.number(),
      estimatedDuration: v.number(),
      difficulty: v.union(v.literal('beginner'), v.literal('intermediate'), v.literal('advanced')),
      prerequisites: v.optional(v.array(v.id('topics'))),
      tags: v.optional(v.array(v.string())),
    })),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const results = [];

    for (const topic of args.topics) {
      const id = await ctx.db.insert('topics', {
        ...topic,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      results.push(id);
    }

    return results;
  },
});
