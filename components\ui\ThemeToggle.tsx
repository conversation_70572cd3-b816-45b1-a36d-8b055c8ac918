import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '~/lib/theme';
import { useThemedStyles } from '~/lib/theme';

interface ThemeToggleProps {
  size?: 'sm' | 'md' | 'lg';
  showLabels?: boolean;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  size = 'md', 
  showLabels = true 
}) => {
  const { themeMode, setTheme, colors } = useTheme();

  const options = [
    { key: 'light' as const, label: 'Light', icon: 'sunny' as const },
    { key: 'dark' as const, label: 'Dark', icon: 'moon' as const },
    { key: 'system' as const, label: 'System', icon: 'phone-portrait' as const },
  ];

  const styles = useThemedStyles((theme, colors, isDark) => {
    const sizes = {
      sm: { padding: 2, iconSize: 14, fontSize: 12, paddingVertical: 6, paddingHorizontal: 8 },
      md: { padding: 4, iconSize: 16, fontSize: 14, paddingVertical: 8, paddingHorizontal: 12 },
      lg: { padding: 6, iconSize: 18, fontSize: 16, paddingVertical: 10, paddingHorizontal: 16 },
    };

    const currentSize = sizes[size];

    return StyleSheet.create({
      container: {
        flexDirection: 'row',
        backgroundColor: theme.card,
        borderRadius: 12,
        padding: currentSize.padding,
        borderWidth: 1,
        borderColor: theme.border,
        shadowColor: theme.shadow,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 1,
      },
      option: {
        flex: 1,
        flexDirection: showLabels ? 'row' : 'column',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: currentSize.paddingVertical,
        paddingHorizontal: currentSize.paddingHorizontal,
        borderRadius: 8,
        minHeight: showLabels ? undefined : currentSize.paddingVertical * 2 + currentSize.iconSize,
      },
      activeOption: {
        backgroundColor: theme.primaryButton,
        shadowColor: isDark ? colors.primary[400] : colors.primary[700],
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 2,
      },
      optionText: {
        marginLeft: showLabels ? 6 : 0,
        marginTop: showLabels ? 0 : 4,
        fontSize: currentSize.fontSize,
        fontWeight: '500',
        color: theme.secondaryText,
      },
      activeOptionText: {
        color: colors.white,
      },
      iconSize: currentSize.iconSize,
    });
  });

  return (
    <View style={styles.container}>
      {options.map((option) => {
        const isActive = themeMode === option.key;
        
        return (
          <TouchableOpacity
            key={option.key}
            style={[
              styles.option,
              isActive && styles.activeOption,
            ]}
            onPress={() => setTheme(option.key)}
            activeOpacity={0.7}
            accessibilityRole="button"
            accessibilityLabel={`Switch to ${option.label} theme`}
            accessibilityState={{ selected: isActive }}
          >
            <Ionicons
              name={option.icon}
              size={styles.iconSize}
              color={isActive ? colors.white : styles.optionText.color}
            />
            {showLabels && (
              <Text
                style={[
                  styles.optionText,
                  isActive && styles.activeOptionText,
                ]}
              >
                {option.label}
              </Text>
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

// Compact version for use in headers/toolbars
export const CompactThemeToggle: React.FC = () => {
  return <ThemeToggle size="sm" showLabels={false} />;
};

// Simple toggle button (just light/dark, no system)
export const SimpleThemeToggle: React.FC = () => {
  const { isDarkMode, toggleTheme, colors } = useTheme();

  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    button: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: theme.card,
      borderWidth: 1,
      borderColor: theme.border,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    activeButton: {
      backgroundColor: theme.primaryButton,
    },
  }));

  return (
    <TouchableOpacity
      style={[styles.button, isDarkMode && styles.activeButton]}
      onPress={toggleTheme}
      activeOpacity={0.7}
      accessibilityRole="button"
      accessibilityLabel={`Switch to ${isDarkMode ? 'light' : 'dark'} theme`}
    >
      <Ionicons
        name={isDarkMode ? 'sunny' : 'moon'}
        size={20}
        color={isDarkMode ? colors.white : styles.button.borderColor}
      />
    </TouchableOpacity>
  );
};

export default ThemeToggle;
