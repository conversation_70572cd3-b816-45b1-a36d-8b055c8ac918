import React, { useEffect } from "react";
import { View, Text, StyleSheet } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  runOnJS
} from "react-native-reanimated";
import { Ionicons } from "@expo/vector-icons";
import { AnswerFeedback as AnswerFeedbackType } from "../store";
import { hapticService } from "../services/HapticService";
import { audioService } from "../services/AudioService";
import { useSettingsStore } from "../stores/SettingsStore";
import { useTheme, useThemedStyles } from '~/lib/theme';
import { getThemedShadow } from '~/lib/themeUtils';

interface Props {
  feedback?: AnswerFeedbackType;
  onAnimationComplete?: () => void;
}

export default function AnswerFeedback({ feedback, onAnimationComplete }: Props) {
  const { animationsEnabled, reducedMotion } = useSettingsStore();
  const { colors } = useTheme();
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const bounceScale = useSharedValue(1);
  const glowOpacity = useSharedValue(0);

  useEffect(() => {
    if (feedback?.showFeedback) {
      const isCorrect = feedback.isCorrect;

      // Trigger haptic and audio feedback
      if (isCorrect) {
        hapticService.onCorrectAnswer();
        audioService.playCorrect();
      } else {
        hapticService.onIncorrectAnswer();
        audioService.playIncorrect();
      }

      if (animationsEnabled && !reducedMotion) {
        // Enhanced entrance animation
        scale.value = withSequence(
          withSpring(1.2, { damping: 10, stiffness: 400 }),
          withSpring(1, { damping: 15, stiffness: 300 })
        );
        opacity.value = withTiming(1, { duration: 200 });

        // Bounce effect for correct answers
        if (isCorrect) {
          bounceScale.value = withSequence(
            withTiming(1.1, { duration: 150 }),
            withTiming(1, { duration: 150 })
          );

          // Glow effect for correct answers
          glowOpacity.value = withSequence(
            withTiming(0.6, { duration: 200 }),
            withTiming(0.3, { duration: 300 }),
            withTiming(0, { duration: 500 })
          );
        }
      } else {
        // Simple animation for reduced motion
        scale.value = withTiming(1, { duration: 200 });
        opacity.value = withTiming(1, { duration: 200 });
      }

      // Auto-hide after 2 seconds
      const timer = setTimeout(() => {
        opacity.value = withTiming(0, { duration: 300 }, () => {
          scale.value = 0;
          bounceScale.value = 1;
          glowOpacity.value = 0;
          if (onAnimationComplete) {
            runOnJS(onAnimationComplete)();
          }
        });
      }, 2000);

      return () => clearTimeout(timer);
    } else {
      // Hide feedback
      opacity.value = withTiming(0, { duration: 200 });
      scale.value = 0;
      bounceScale.value = 1;
      glowOpacity.value = 0;
    }
  }, [feedback?.showFeedback, animationsEnabled, reducedMotion]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value * bounceScale.value }],
    opacity: opacity.value,
  }));

  const glowStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));

  // Create theme-aware styles
  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    container: {
      position: "absolute",
      top: 20,
      right: 20,
      zIndex: 1000,
    },
    glow: {
      position: "absolute",
      top: -5,
      left: -5,
      right: -5,
      bottom: -5,
      backgroundColor: colors.success[500],
      borderRadius: 17,
      opacity: 0.3,
    },
    feedbackCard: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 12,
      ...getThemedShadow(theme, 'sm'),
    },
    correctCard: {
      backgroundColor: colors.success[50],
      borderWidth: 1,
      borderColor: colors.success[500],
    },
    incorrectCard: {
      backgroundColor: colors.error[50],
      borderWidth: 1,
      borderColor: colors.error[500],
    },
    iconContainer: {
      marginRight: 8,
    },
    feedbackText: {
      fontSize: 14,
      fontWeight: "600",
    },
    correctText: {
      color: colors.success[500],
    },
    incorrectText: {
      color: colors.error[500],
    },
  }));

  if (!feedback?.showFeedback) {
    return null;
  }

  const isCorrect = feedback.isCorrect;

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      {/* Glow effect for correct answers */}
      {isCorrect && (
        <Animated.View style={[styles.glow, glowStyle]} />
      )}

      <View style={[
        styles.feedbackCard,
        isCorrect ? styles.correctCard : styles.incorrectCard
      ]}>
        <View style={styles.iconContainer}>
          <Ionicons
            name={isCorrect ? "checkmark-circle" : "close-circle"}
            size={24}
            color={isCorrect ? colors.success[500] : colors.error[500]}
          />
        </View>
        <Text style={[
          styles.feedbackText,
          isCorrect ? styles.correctText : styles.incorrectText
        ]}>
          {isCorrect ? "Correct!" : "Try again"}
        </Text>
      </View>
    </Animated.View>
  );
}

// Styles are now created inside the component using useThemedStyles
