import { v } from 'convex/values';
import { query, mutation } from './_generated/server';
import { Id } from './_generated/dataModel';

// ==================== SKILL PROGRESS ====================

export const getSkillProgress = query({
  args: {
    userId: v.id('users'),
    skillId: v.string(), // Now a string UUID
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('skillProgress')
      .withIndex('byUserSkill', (q) =>
        q.eq('userId', args.userId).eq('skillId', args.skillId)
      )
      .first();
  },
});

export const updateSkillProgress = mutation({
  args: {
    userId: v.id('users'),
    skillId: v.string(), // Now a string UUID instead of document ID
    subjectId: v.id('subjects'), // Reference to subject document
    topicId: v.string(), // Topic UUID within subject
    lessonIndex: v.optional(v.number()),
    exerciseIndex: v.optional(v.number()),
    completedExercises: v.optional(v.array(v.string())),
    answers: v.optional(v.object({})),
    isCompleted: v.optional(v.boolean()),
    score: v.optional(v.number()),
    timeSpent: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { userId, skillId, subjectId, topicId, ...updates } = args;
    const now = Date.now();

    // Check if progress already exists
    const existingProgress = await ctx.db
      .query('skillProgress')
      .withIndex('byUserSkill', (q) =>
        q.eq('userId', userId).eq('skillId', skillId)
      )
      .first();

    if (existingProgress) {
      // Update existing progress
      await ctx.db.patch(existingProgress._id, {
        ...updates,
        lastAccessed: now,
        updatedAt: now,
        ...(updates.isCompleted && !existingProgress.isCompleted && {
          completionDate: now,
        }),
      });
      return existingProgress._id;
    } else {
      // Create new progress
      return await ctx.db.insert('skillProgress', {
        userId,
        skillId,
        subjectId,
        topicId,
        lessonIndex: updates.lessonIndex || 0,
        exerciseIndex: updates.exerciseIndex || 0,
        completedExercises: updates.completedExercises || [],
        answers: updates.answers || {},
        isCompleted: updates.isCompleted || false,
        score: updates.score,
        timeSpent: updates.timeSpent,
        attempts: 1,
        lastAccessed: now,
        createdAt: now,
        updatedAt: now,
        ...(updates.isCompleted && { completionDate: now }),
      });
    }
  },
});

export const getUserSkillProgress = query({
  args: { userId: v.id('users') },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('skillProgress')
      .withIndex('byUser', (q) => q.eq('userId', args.userId))
      .collect();
  },
});

// ==================== TOPIC PROGRESS ====================

// Internal function for calculating topic progress
async function calculateTopicProgressInternal(
  ctx: any,
  args: { userId: string; subjectId: string; topicId: string }
) {
    const { userId, subjectId, topicId } = args;
    const now = Date.now();

    // Get the subject and find the topic within it
    const subject = await ctx.db.get(subjectId);
    if (!subject || !subject.isActive) return null;

    const topic = subject.topics.find((t: any) => t.id === topicId && t.isActive !== false);
    if (!topic) return null;

    // Get skills for this topic from the flat skills array
    const skills = subject.skills.filter((s: any) => s.topicId === topicId && s.isActive !== false);
    if (skills.length === 0) return null;

    // Get progress for all skills
    let completedSkillsCount = 0;
    let totalScore = 0;
    let totalTimeSpent = 0;
    let lastAccessed = 0;
    let hasAnyProgress = false;

    for (const skill of skills) {
      const skillProgress = await ctx.db
        .query('skillProgress')
        .withIndex('byUserSkill', (q: any) =>
          q.eq('userId', userId).eq('skillId', skill.id)
        )
        .first();

      if (skillProgress) {
        hasAnyProgress = true;

        if (skillProgress.isCompleted) {
          completedSkillsCount++;
        }

        if (skillProgress.score) {
          totalScore += skillProgress.score;
        }

        if (skillProgress.timeSpent) {
          totalTimeSpent += skillProgress.timeSpent;
        }

        if (skillProgress.lastAccessed > lastAccessed) {
          lastAccessed = skillProgress.lastAccessed;
        }
      }
    }

    if (!hasAnyProgress) return null;

    const isCompleted = completedSkillsCount === skills.length;
    const overallScore = skills.length > 0 ? Math.round(totalScore / skills.length) : 0;

    // Check if topic progress already exists
    const existingProgress = await ctx.db
      .query('topicProgress')
      .withIndex('byUserTopic', (q: any) =>
        q.eq('userId', userId).eq('topicId', topicId)
      )
      .first();

    const progressData = {
      userId,
      subjectId,
      topicId,
      isCompleted,
      overallScore,
      totalTimeSpent,
      completedSkillsCount,
      totalSkillsCount: skills.length,
      lastAccessed: lastAccessed || now,
      updatedAt: now,
      ...(isCompleted && { completionDate: now }),
    };

    if (existingProgress) {
      await ctx.db.patch(existingProgress._id, progressData);
      return existingProgress._id;
    } else {
      return await ctx.db.insert('topicProgress', {
        ...progressData,
        createdAt: now,
      });
    }
}

export const calculateTopicProgress = mutation({
  args: {
    userId: v.id('users'),
    subjectId: v.id('subjects'),
    topicId: v.string(),
  },
  handler: async (ctx, args) => {
    return await calculateTopicProgressInternal(ctx, args);
  },
});

export const getTopicProgress = query({
  args: {
    userId: v.id('users'),
    topicId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('topicProgress')
      .withIndex('byUserTopic', (q) => 
        q.eq('userId', args.userId).eq('topicId', args.topicId)
      )
      .first();
  },
});

// ==================== SUBJECT PROGRESS ====================

// Internal function for calculating subject progress
async function calculateSubjectProgressInternal(
  ctx: any,
  args: { userId: string; subjectId: string }
) {
    const { userId, subjectId } = args;
    const now = Date.now();

    // Get the subject with embedded topics
    const subject = await ctx.db.get(subjectId);
    if (!subject || !subject.isActive) return null;

    const topics = subject.topics.filter((t: any) => t.isActive !== false);
    if (topics.length === 0) return null;

    // Get progress for all topics
    let completedTopicsCount = 0;
    let totalScore = 0;
    let totalTimeSpent = 0;
    let lastAccessed = 0;
    let hasAnyProgress = false;

    for (const topic of topics) {
      // Get skills for this topic from the flat skills array
      const topicSkills = subject.skills.filter((s: any) => s.topicId === topic.id && s.isActive !== false);

      const topicProgress = await ctx.db
        .query('topicProgress')
        .withIndex('byUserTopic', (q: any) =>
          q.eq('userId', userId).eq('topicId', topic.id)
        )
        .first();

      if (topicProgress) {
        hasAnyProgress = true;
        
        if (topicProgress.isCompleted) {
          completedTopicsCount++;
        }
        
        if (topicProgress.overallScore) {
          totalScore += topicProgress.overallScore;
        }
        
        if (topicProgress.totalTimeSpent) {
          totalTimeSpent += topicProgress.totalTimeSpent;
        }
        
        if (topicProgress.lastAccessed > lastAccessed) {
          lastAccessed = topicProgress.lastAccessed;
        }
      }
    }

    if (!hasAnyProgress) return null;

    const isCompleted = completedTopicsCount === topics.length;
    const overallScore = topics.length > 0 ? Math.round(totalScore / topics.length) : 0;

    // Check if subject progress already exists
    const existingProgress = await ctx.db
      .query('subjectProgress')
      .withIndex('byUserSubject', (q: any) =>
        q.eq('userId', userId).eq('subjectId', subjectId)
      )
      .first();

    const progressData = {
      userId,
      subjectId,
      isCompleted,
      overallScore,
      totalTimeSpent,
      completedTopicsCount,
      totalTopicsCount: topics.length,
      lastAccessed: lastAccessed || now,
      updatedAt: now,
      ...(isCompleted && { completionDate: now }),
    };

    if (existingProgress) {
      await ctx.db.patch(existingProgress._id, progressData);
      return existingProgress._id;
    } else {
      return await ctx.db.insert('subjectProgress', {
        ...progressData,
        createdAt: now,
      });
    }
}

export const calculateSubjectProgress = mutation({
  args: {
    userId: v.id('users'),
    subjectId: v.id('subjects'),
  },
  handler: async (ctx, args) => {
    return await calculateSubjectProgressInternal(ctx, args);
  },
});

export const getSubjectProgress = query({
  args: { 
    userId: v.id('users'),
    subjectId: v.id('subjects'),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('subjectProgress')
      .withIndex('byUserSubject', (q) => 
        q.eq('userId', args.userId).eq('subjectId', args.subjectId)
      )
      .first();
  },
});

// ==================== OVERALL STATISTICS ====================

export const getUserOverallStats = query({
  args: { userId: v.id('users') },
  handler: async (ctx, args) => {
    // Get all subjects
    const allSubjects = await ctx.db
      .query('subjects')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect();

    // Get user's subject progress
    const subjectProgresses = await ctx.db
      .query('subjectProgress')
      .withIndex('byUser', (q) => q.eq('userId', args.userId))
      .collect();

    // Get user's topic progress
    const topicProgresses = await ctx.db
      .query('topicProgress')
      .withIndex('byUser', (q) => q.eq('userId', args.userId))
      .collect();

    // Get user's skill progress
    const skillProgresses = await ctx.db
      .query('skillProgress')
      .withIndex('byUser', (q) => q.eq('userId', args.userId))
      .collect();

    // Calculate totals from embedded data
    let totalTopics = 0;
    let totalSkills = 0;

    for (const subject of allSubjects) {
      const topics = subject.topics.filter((t: any) => t.isActive !== false);
      totalTopics += topics.length;

      // Count skills from the flat skills array
      const skills = subject.skills.filter((s: any) => s.isActive !== false);
      totalSkills += skills.length;
    }

    const completedSubjects = subjectProgresses.filter(p => p.isCompleted).length;
    const completedTopics = topicProgresses.filter(p => p.isCompleted).length;
    const completedSkills = skillProgresses.filter(p => p.isCompleted).length;

    const overallProgress = totalSkills > 0 ? Math.round((completedSkills / totalSkills) * 100) : 0;

    return {
      totalSubjects: allSubjects.length,
      completedSubjects,
      totalTopics,
      completedTopics,
      totalSkills,
      completedSkills,
      overallProgress,
    };
  },
});

// ==================== PROGRESS TRIGGERS ====================

export const updateProgressHierarchy = mutation({
  args: {
    userId: v.id('users'),
    skillId: v.string(), // Skill UUID
    subjectId: v.id('subjects'),
    topicId: v.string(), // Topic UUID
  },
  handler: async (ctx, args) => {
    const { userId, skillId, subjectId, topicId } = args;

    // Update topic progress
    await calculateTopicProgressInternal(ctx, { userId, subjectId, topicId });

    // Update subject progress
    await calculateSubjectProgressInternal(ctx, { userId, subjectId });

    return { success: true };
  },
});
